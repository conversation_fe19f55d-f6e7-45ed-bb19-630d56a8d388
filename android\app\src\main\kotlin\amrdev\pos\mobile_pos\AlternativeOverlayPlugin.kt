package amrdev.pos.mobile_pos

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class AlternativeOverlayPlugin: FlutterPlugin, MethodCallHandler, ActivityAware {
    private lateinit var channel: MethodChannel
    private var context: Context? = null
    private var activity: Activity? = null
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var isOverlayVisible = false

    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "alternative_overlay")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            "initialize" -> {
                initialize(result)
            }
            "showOverlay" -> {
                val title = call.argument<String>("title") ?: "المساعد الصوتي"
                val message = call.argument<String>("message") ?: "أهلاً!"
                val width = call.argument<Int>("width") ?: 350
                val height = call.argument<Int>("height") ?: 500
                showOverlay(title, message, width, height, result)
            }
            "hideOverlay" -> {
                hideOverlay(result)
            }
            "updateContent" -> {
                val message = call.argument<String>("message") ?: ""
                updateContent(message, result)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun initialize(result: Result) {
        try {
            windowManager = context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            result.success(true)
        } catch (e: Exception) {
            result.success(false)
        }
    }

    private fun showOverlay(title: String, message: String, width: Int, height: Int, result: Result) {
        try {
            if (isOverlayVisible) {
                hideOverlay(null)
            }

            // إنشاء النافذة العائمة
            val layoutInflater = LayoutInflater.from(context)
            overlayView = layoutInflater.inflate(android.R.layout.simple_list_item_2, null)

            // تخصيص المحتوى
            val titleView = overlayView?.findViewById<TextView>(android.R.id.text1)
            val messageView = overlayView?.findViewById<TextView>(android.R.id.text2)
            
            titleView?.text = title
            messageView?.text = message
            
            // تخصيص التصميم
            overlayView?.setBackgroundColor(0xFF1e3c72.toInt())
            titleView?.setTextColor(0xFFFFFFFF.toInt())
            messageView?.setTextColor(0xFFCCCCCC.toInt())
            titleView?.textSize = 18f
            messageView?.textSize = 14f

            // إعداد معاملات النافذة
            val layoutParams = WindowManager.LayoutParams(
                width,
                height,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                PixelFormat.TRANSLUCENT
            )

            layoutParams.gravity = Gravity.CENTER
            layoutParams.x = 0
            layoutParams.y = 0

            // إضافة النافذة
            windowManager?.addView(overlayView, layoutParams)
            isOverlayVisible = true
            
            result.success(true)
        } catch (e: Exception) {
            result.success(false)
        }
    }

    private fun hideOverlay(result: Result?) {
        try {
            if (isOverlayVisible && overlayView != null) {
                windowManager?.removeView(overlayView)
                overlayView = null
                isOverlayVisible = false
            }
            result?.success(true)
        } catch (e: Exception) {
            result?.success(false)
        }
    }

    private fun updateContent(message: String, result: Result) {
        try {
            if (isOverlayVisible && overlayView != null) {
                val messageView = overlayView?.findViewById<TextView>(android.R.id.text2)
                messageView?.text = message
                result.success(true)
            } else {
                result.success(false)
            }
        } catch (e: Exception) {
            result.success(false)
        }
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        hideOverlay(null)
    }

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        activity = binding.activity
    }

    override fun onDetachedFromActivityForConfigChanges() {
        activity = null
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        activity = binding.activity
    }

    override fun onDetachedFromActivity() {
        activity = null
        hideOverlay(null)
    }
}
