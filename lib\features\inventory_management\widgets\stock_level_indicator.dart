// بسم الله الرحمن الرحيم
// مؤشر مستوى المخزون - مكون يعرض مستوى المخزون بشكل مرئي

import 'package:flutter/material.dart';

import '../models/product_model.dart';

/// مؤشر مستوى المخزون
class StockLevelIndicator extends StatelessWidget {
  /// ينشئ مؤشر مستوى المخزون
  const StockLevelIndicator({
    super.key,
    required this.quantity,
    required this.minQuantity,
    this.maxQuantity,
    this.showText = true,
    this.showIcon = true,
    this.height = 8.0,
    this.width = double.infinity,
    this.borderRadius = 4.0,
    this.backgroundColor,
    this.lowStockColor,
    this.normalStockColor,
    this.highStockColor,
    this.outOfStockColor,
  });

  /// ينشئ مؤشر مستوى المخزون من منتج
  factory StockLevelIndicator.fromProduct(
    ProductModel product, {
    bool showText = true,
    bool showIcon = true,
    double height = 8.0,
    double width = double.infinity,
    double borderRadius = 4.0,
    Color? backgroundColor,
    Color? lowStockColor,
    Color? normalStockColor,
    Color? highStockColor,
    Color? outOfStockColor,
  }) {
    return StockLevelIndicator(
      quantity: product.quantity,
      minQuantity: product.minQuantity,
      maxQuantity: product.maxQuantity,
      showText: showText,
      showIcon: showIcon,
      height: height,
      width: width,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      lowStockColor: lowStockColor,
      normalStockColor: normalStockColor,
      highStockColor: highStockColor,
      outOfStockColor: outOfStockColor,
    );
  }

  /// الكمية الحالية
  final int quantity;

  /// الحد الأدنى للكمية
  final int minQuantity;

  /// الحد الأقصى للكمية (اختياري)
  final int? maxQuantity;

  /// هل يتم عرض النص؟
  final bool showText;

  /// هل يتم عرض الأيقونة؟
  final bool showIcon;

  /// ارتفاع المؤشر
  final double height;

  /// عرض المؤشر
  final double width;

  /// نصف قطر الحدود
  final double borderRadius;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون المخزون المنخفض
  final Color? lowStockColor;

  /// لون المخزون العادي
  final Color? normalStockColor;

  /// لون المخزون المرتفع
  final Color? highStockColor;

  /// لون نفاذ المخزون
  final Color? outOfStockColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // تحديد الألوان
    final bgColor = backgroundColor ?? Colors.grey.shade200;
    final outOfStock = outOfStockColor ?? Colors.red;
    final lowStock = lowStockColor ?? Colors.orange;
    final normalStock = normalStockColor ?? Colors.green;
    final highStock = highStockColor ?? Colors.blue;

    // تحديد حالة المخزون
    final StockStatus status = _getStockStatus();

    // تحديد لون المؤشر
    Color indicatorColor;
    IconData statusIcon;
    String statusText;

    switch (status) {
      case StockStatus.outOfStock:
        indicatorColor = outOfStock;
        statusIcon = Icons.error;
        statusText = 'نفذ من المخزون';
        break;
      case StockStatus.lowStock:
        indicatorColor = lowStock;
        statusIcon = Icons.warning;
        statusText = 'منخفض المخزون';
        break;
      case StockStatus.normalStock:
        indicatorColor = normalStock;
        statusIcon = Icons.check_circle;
        statusText = 'متوفر';
        break;
      case StockStatus.highStock:
        indicatorColor = highStock;
        statusIcon = Icons.inventory;
        statusText = 'مخزون مرتفع';
        break;
    }

    // حساب نسبة المؤشر
    double indicatorPercentage = 0.0;
    if (quantity > 0) {
      if (maxQuantity != null && maxQuantity! > 0) {
        indicatorPercentage = quantity / maxQuantity!;
      } else {
        indicatorPercentage = quantity / (minQuantity * 2);
      }
      indicatorPercentage = indicatorPercentage.clamp(0.0, 1.0);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // شريط المؤشر
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: Row(
            children: [
              Container(
                width: width * indicatorPercentage,
                decoration: BoxDecoration(
                  color: indicatorColor,
                  borderRadius: BorderRadius.circular(borderRadius),
                ),
              ),
            ],
          ),
        ),

        // نص الحالة
        if (showText || showIcon) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              if (showIcon) ...[
                Icon(
                  statusIcon,
                  size: 14,
                  color: indicatorColor,
                ),
                const SizedBox(width: 4),
              ],
              if (showText)
                Text(
                  statusText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: indicatorColor,
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }

  /// الحصول على حالة المخزون
  StockStatus _getStockStatus() {
    if (quantity <= 0) {
      return StockStatus.outOfStock;
    } else if (quantity <= minQuantity) {
      return StockStatus.lowStock;
    } else if (maxQuantity != null && quantity >= maxQuantity!) {
      return StockStatus.highStock;
    } else {
      return StockStatus.normalStock;
    }
  }
}

/// حالة المخزون
enum StockStatus {
  /// نفذ من المخزون
  outOfStock,

  /// منخفض المخزون
  lowStock,

  /// مخزون عادي
  normalStock,

  /// مخزون مرتفع
  highStock,
}
