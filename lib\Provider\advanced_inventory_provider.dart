// مزود بيانات المخزون المتقدم
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Provider/product_provider.dart';
import 'package:mobile_pos/models/advanced_inventory_model.dart';
import 'package:mobile_pos/model/product_model.dart';
import 'package:mobile_pos/repository/advanced_inventory_repo.dart';

/// مزود بيانات المخزون المتقدم
final advancedInventoryProvider = StateNotifierProvider<
    AdvancedInventoryNotifier, AsyncValue<List<AdvancedInventoryModel>>>((ref) {
  final products = ref.watch(productProvider);

  return AdvancedInventoryNotifier(
    ref: ref,
    products: products.value ?? [],
  );
});

/// مزود حالة فترة المخزون
final inventoryPeriodProvider = StateProvider<InventoryPeriod>((ref) {
  // افتراضياً، الفترة هي الشهر الحالي
  final now = DateTime.now();
  final startOfMonth = DateTime(now.year, now.month, 1);
  final endOfMonth = DateTime(now.year, now.month + 1, 0);

  return InventoryPeriod(
    startDate: startOfMonth,
    endDate: endOfMonth,
  );
});

/// مزود تصفية المخزون
final inventoryFilterProvider = StateProvider<InventoryFilter>((ref) {
  return InventoryFilter(
    category: null,
    brand: null,
    searchQuery: '',
    sortBy: InventorySortBy.name,
    sortAscending: true,
  );
});

/// مزود بيانات المخزون المتقدم
class AdvancedInventoryNotifier
    extends StateNotifier<AsyncValue<List<AdvancedInventoryModel>>> {
  final Ref _ref;
  final List<ProductModel> products;
  final AdvancedInventoryRepo _repo = AdvancedInventoryRepo();

  AdvancedInventoryNotifier({
    required Ref ref,
    required this.products,
  })  : _ref = ref,
        super(const AsyncValue.loading()) {
    loadInventoryData();
  }

  /// تحميل بيانات المخزون
  Future<void> loadInventoryData() async {
    state = const AsyncValue.loading();

    try {
      final period = _ref.read(inventoryPeriodProvider);
      final filter = _ref.read(inventoryFilterProvider);

      // تحميل بيانات المخزون المتقدم
      final inventoryData = await _repo.getAdvancedInventoryData(
        products,
        period.startDate,
        period.endDate,
      );

      // تطبيق التصفية
      final filteredData = _applyFilter(inventoryData, filter);

      state = AsyncValue.data(filteredData);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// تحديث فترة المخزون
  Future<void> updatePeriod(DateTime startDate, DateTime endDate) async {
    _ref.read(inventoryPeriodProvider.notifier).state = InventoryPeriod(
      startDate: startDate,
      endDate: endDate,
    );

    await loadInventoryData();
  }

  /// تحديث تصفية المخزون
  Future<void> updateFilter(InventoryFilter filter) async {
    _ref.read(inventoryFilterProvider.notifier).state = filter;

    if (state.value != null) {
      final inventoryData = state.value!;
      final filteredData = _applyFilter(inventoryData, filter);
      state = AsyncValue.data(filteredData);
    }
  }

  /// تطبيق التصفية على بيانات المخزون
  List<AdvancedInventoryModel> _applyFilter(
      List<AdvancedInventoryModel> data, InventoryFilter filter) {
    var filteredData = data;

    // تصفية حسب الفئة
    if (filter.category != null && filter.category!.isNotEmpty) {
      filteredData = filteredData
          .where((item) => item.category == filter.category)
          .toList();
    }

    // تصفية حسب العلامة التجارية
    if (filter.brand != null && filter.brand!.isNotEmpty) {
      filteredData =
          filteredData.where((item) => item.brand == filter.brand).toList();
    }

    // تصفية حسب البحث
    if (filter.searchQuery.isNotEmpty) {
      filteredData = filteredData.where((item) {
        return item.productName
                .toLowerCase()
                .contains(filter.searchQuery.toLowerCase()) ||
            item.productCode
                .toLowerCase()
                .contains(filter.searchQuery.toLowerCase());
      }).toList();
    }

    // ترتيب البيانات
    filteredData.sort((a, b) {
      int result = 0;

      switch (filter.sortBy) {
        case InventorySortBy.name:
          result = a.productName.compareTo(b.productName);
          break;
        case InventorySortBy.code:
          result = a.productCode.compareTo(b.productCode);
          break;
        case InventorySortBy.category:
          result = a.category.compareTo(b.category);
          break;
        case InventorySortBy.openingStock:
          result = a.openingStock.compareTo(b.openingStock);
          break;
        case InventorySortBy.closingStock:
          result = a.closingStock.compareTo(b.closingStock);
          break;
        case InventorySortBy.sales:
          result = a.sales.compareTo(b.sales);
          break;
        case InventorySortBy.purchases:
          result = a.purchases.compareTo(b.purchases);
          break;
        case InventorySortBy.turnoverRate:
          result = a.turnoverRate.compareTo(b.turnoverRate);
          break;
      }

      return filter.sortAscending ? result : -result;
    });

    return filteredData;
  }

  /// الحصول على قائمة الفئات
  List<String> getCategories() {
    final categories = <String>{};

    for (var product in products) {
      if (product.productCategory.isNotEmpty) {
        categories.add(product.productCategory);
      }
    }

    return categories.toList()..sort();
  }

  /// الحصول على قائمة العلامات التجارية
  List<String> getBrands() {
    final brands = <String>{};

    for (var product in products) {
      if (product.brandName.isNotEmpty) {
        brands.add(product.brandName);
      }
    }

    return brands.toList()..sort();
  }

  /// الحصول على إجمالي قيمة المخزون الافتتاحي
  double getTotalOpeningStockValue() {
    if (state.value == null) {
      return 0;
    }

    return state.value!.fold(0, (sum, item) => sum + item.openingStockValue);
  }

  /// الحصول على إجمالي قيمة المخزون الختامي
  double getTotalClosingStockValue() {
    if (state.value == null) {
      return 0;
    }

    return state.value!.fold(0, (sum, item) => sum + item.closingStockValue);
  }

  /// الحصول على إجمالي قيمة المشتريات
  double getTotalPurchasesValue() {
    if (state.value == null) {
      return 0;
    }

    return state.value!.fold(0, (sum, item) => sum + item.purchasesValue);
  }

  /// الحصول على إجمالي قيمة المبيعات
  double getTotalSalesValue() {
    if (state.value == null) {
      return 0;
    }

    return state.value!.fold(0, (sum, item) => sum + item.salesValue);
  }

  /// الحصول على إجمالي الربح الإجمالي
  double getTotalGrossProfit() {
    if (state.value == null) {
      return 0;
    }

    return state.value!.fold(0, (sum, item) => sum + item.grossProfit);
  }
}

/// فترة المخزون
class InventoryPeriod {
  final DateTime startDate;
  final DateTime endDate;

  InventoryPeriod({
    required this.startDate,
    required this.endDate,
  });
}

/// تصفية المخزون
class InventoryFilter {
  final String? category;
  final String? brand;
  final String searchQuery;
  final InventorySortBy sortBy;
  final bool sortAscending;

  InventoryFilter({
    this.category,
    this.brand,
    this.searchQuery = '',
    this.sortBy = InventorySortBy.name,
    this.sortAscending = true,
  });

  InventoryFilter copyWith({
    String? category,
    String? brand,
    String? searchQuery,
    InventorySortBy? sortBy,
    bool? sortAscending,
  }) {
    return InventoryFilter(
      category: category ?? this.category,
      brand: brand ?? this.brand,
      searchQuery: searchQuery ?? this.searchQuery,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

/// ترتيب المخزون
enum InventorySortBy {
  name,
  code,
  category,
  openingStock,
  closingStock,
  sales,
  purchases,
  turnoverRate,
}
