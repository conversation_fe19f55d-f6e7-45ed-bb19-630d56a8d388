import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/daily_sales_report_model.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';

/// مزود طباعة التقرير اليومي
final dailyReportPrinterProvider =
    ChangeNotifierProvider((ref) => DailyReportPrinter());

class DailyReportPrinter extends ChangeNotifier {
  bool _isConnected = false;
  bool _isPrinting = false;
  String? _lastError;

  bool get isConnected => _isConnected;
  bool get isPrinting => _isPrinting;
  String? get lastError => _lastError;

  /// التحقق من حالة الاتصال
  Future<void> checkConnection() async {
    try {
      final status = await PrintBluetoothThermal.connectionStatus;
      _isConnected = status == true;
      notifyListeners();
    } catch (e) {
      _isConnected = false;
      _lastError = 'خطأ في التحقق من الاتصال: $e';
      notifyListeners();
    }
  }

  /// طباعة التقرير اليومي
  Future<bool> printDailyReport({
    required DailySalesReportModel report,
    required String companyName,
    required String companyPhone,
    String? companyAddress,
  }) async {
    _isPrinting = true;
    _lastError = null;
    notifyListeners();

    try {
      // التحقق من الاتصال
      bool? isConnected = await PrintBluetoothThermal.connectionStatus;
      if (isConnected != true) {
        _lastError = 'الطابعة غير متصلة';
        _isPrinting = false;
        notifyListeners();
        return false;
      }

      // إنشاء محتوى التقرير
      List<int> bytes = await _generateReportBytes(
        report: report,
        companyName: companyName,
        companyPhone: companyPhone,
        companyAddress: companyAddress,
      );

      // طباعة التقرير
      await PrintBluetoothThermal.writeBytes(bytes);

      _isPrinting = false;
      notifyListeners();
      return true;
    } catch (e) {
      _lastError = 'خطأ في الطباعة: $e';
      _isPrinting = false;
      notifyListeners();
      return false;
    }
  }

  /// إنشاء بايتات التقرير للطباعة
  Future<List<int>> _generateReportBytes({
    required DailySalesReportModel report,
    required String companyName,
    required String companyPhone,
    String? companyAddress,
  }) async {
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm80, profile);
    List<int> bytes = [];

    // رأس التقرير
    bytes += generator.text(
      companyName,
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size2,
        width: PosTextSize.size2,
        bold: true,
      ),
    );

    if (companyAddress != null && companyAddress.isNotEmpty) {
      bytes += generator.text(
        companyAddress,
        styles: const PosStyles(align: PosAlign.center),
      );
    }

    bytes += generator.text(
      'تليفون: $companyPhone',
      styles: const PosStyles(align: PosAlign.center),
    );

    bytes += generator.hr();

    // عنوان التقرير
    bytes += generator.text(
      'تقرير المبيعات اليومي',
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size2,
        width: PosTextSize.size1,
        bold: true,
      ),
    );

    bytes += generator.text(
      'تاريخ: ${DateFormat('yyyy/MM/dd', 'ar').format(report.reportDate)}',
      styles: const PosStyles(align: PosAlign.center),
    );

    bytes += generator.text(
      'وقت الطباعة: ${DateFormat('HH:mm', 'ar').format(DateTime.now())}',
      styles: const PosStyles(align: PosAlign.center),
    );

    bytes += generator.hr();

    // ملخص اليوم
    bytes += generator.text(
      'ملخص اليوم',
      styles: const PosStyles(bold: true, align: PosAlign.center),
    );

    bytes += generator.text('عدد الفواتير: ${report.summary.totalInvoices}');
    bytes += generator
        .text('إجمالي المبيعات: ${_formatCurrency(report.summary.totalSales)}');
    bytes += generator
        .text('إجمالي الضرائب: ${_formatCurrency(report.summary.totalVat)}');
    bytes += generator
        .text('إجمالي الخصم: ${_formatCurrency(report.summary.totalDiscount)}');
    bytes += generator
        .text('صافي المبيعات: ${_formatCurrency(report.summary.netSales)}');

    bytes += generator.hr();

    // ملخص طرق الدفع
    bytes += generator.text(
      'ملخص طرق الدفع',
      styles: const PosStyles(bold: true, align: PosAlign.center),
    );

    bytes += generator.text(
        'مبيعات نقدية: ${_formatCurrency(report.paymentSummary.cashSales)}');
    bytes += generator.text(
        'عدد الفواتير النقدية: ${report.paymentSummary.cashInvoicesCount}');
    bytes += generator.text(
        'مبيعات آجلة: ${_formatCurrency(report.paymentSummary.creditSales)}');
    bytes += generator.text(
        'عدد الفواتير الآجلة: ${report.paymentSummary.creditInvoicesCount}');

    bytes += generator.hr();

    // تفاصيل الفواتير
    if (report.salesTransactions.isNotEmpty) {
      bytes += generator.text(
        'تفاصيل الفواتير',
        styles: const PosStyles(bold: true, align: PosAlign.center),
      );

      for (int i = 0; i < report.salesTransactions.length; i++) {
        final transaction = report.salesTransactions[i];

        bytes += generator
            .text('${i + 1}. فاتورة رقم: ${transaction.invoiceNumber}');
        bytes += generator.text('   العميل: ${transaction.customerName}');
        bytes += generator.text(
            '   المبلغ: ${_formatCurrency(transaction.totalAmount ?? 0)}');
        bytes += generator.text(
            '   الوقت: ${DateFormat('HH:mm').format(DateTime.parse(transaction.purchaseDate))}');

        final paymentType = (transaction.dueAmount ?? 0) == 0 ? 'نقدي' : 'آجل';
        bytes += generator.text('   طريقة الدفع: $paymentType');

        if (i < report.salesTransactions.length - 1) {
          bytes += generator.text('');
        }
      }

      bytes += generator.hr();
    }

    // ملخص الأصناف الأكثر مبيعاً
    if (report.productsSummary.isNotEmpty) {
      bytes += generator.text(
        'أهم الأصناف المباعة',
        styles: const PosStyles(bold: true, align: PosAlign.center),
      );

      final topProducts = report.productsSummary.take(10).toList();
      for (int i = 0; i < topProducts.length; i++) {
        final product = topProducts[i];
        bytes += generator.text('${i + 1}. ${product.productName}');
        bytes += generator.text('   الكمية: ${product.totalQuantity}');
        bytes += generator
            .text('   المبلغ: ${_formatCurrency(product.totalAmount)}');

        if (i < topProducts.length - 1) {
          bytes += generator.text('');
        }
      }

      bytes += generator.hr();
    }

    // خاتمة التقرير
    bytes += generator.text(
      'شكراً لكم',
      styles: const PosStyles(align: PosAlign.center, bold: true),
    );

    bytes += generator.feed(3);
    bytes += generator.cut();

    return bytes;
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return '${formatter.format(amount)} جنيه';
  }
}
