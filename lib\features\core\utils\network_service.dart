// بسم الله الرحمن الرحيم
// خدمة الشبكة - توفر واجهة موحدة للتعامل مع الشبكة والاتصال بالإنترنت

import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// حالة الاتصال بالإنترنت
enum ConnectionStatus {
  /// متصل بالإنترنت
  online,

  /// غير متصل بالإنترنت
  offline,

  /// جاري التحقق من الاتصال
  checking,
}

/// خدمة الشبكة
class NetworkService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة الشبكة
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  // مثيل Connectivity
  final Connectivity _connectivity = Connectivity();

  // حالة الاتصال الحالية
  ConnectionStatus _connectionStatus = ConnectionStatus.checking;

  // مراقب تغيرات الاتصال
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // مراقبي حالة الاتصال
  final List<Function(ConnectionStatus)> _listeners = [];

  // الحصول على حالة الاتصال الحالية
  ConnectionStatus get connectionStatus => _connectionStatus;

  /// تهيئة خدمة الشبكة
  Future<void> initialize() async {
    // التحقق من حالة الاتصال الحالية
    await checkConnection();

    // الاستماع لتغيرات الاتصال
    _connectivitySubscription = _connectivity.onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      // استخدام النتيجة الأولى في القائمة (عادة ما تكون النتيجة الوحيدة)
      final result = results.first;
      if (result == ConnectivityResult.none) {
        _updateConnectionStatus(ConnectionStatus.offline);
      } else {
        _updateConnectionStatus(ConnectionStatus.online);
      }
    });
  }

  /// إيقاف خدمة الشبكة
  Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
    _listeners.clear();
  }

  /// التحقق من حالة الاتصال
  Future<ConnectionStatus> checkConnection() async {
    try {
      final results = await _connectivity.checkConnectivity();
      // استخدام النتيجة الأولى في القائمة (عادة ما تكون النتيجة الوحيدة)
      final result = results.first;
      if (result == ConnectivityResult.none) {
        _updateConnectionStatus(ConnectionStatus.offline);
      } else {
        _updateConnectionStatus(ConnectionStatus.online);
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من الاتصال: $e');
      _updateConnectionStatus(ConnectionStatus.offline);
    }
    return _connectionStatus;
  }

  /// تحديث حالة الاتصال وإخطار المراقبين
  void _updateConnectionStatus(ConnectionStatus status) {
    if (_connectionStatus != status) {
      _connectionStatus = status;
      _notifyListeners();
    }
  }

  /// إضافة مراقب لحالة الاتصال
  void addListener(Function(ConnectionStatus) listener) {
    if (!_listeners.contains(listener)) {
      _listeners.add(listener);
    }
  }

  /// إزالة مراقب لحالة الاتصال
  void removeListener(Function(ConnectionStatus) listener) {
    _listeners.remove(listener);
  }

  /// إخطار جميع المراقبين بتغير حالة الاتصال
  void _notifyListeners() {
    for (var listener in _listeners) {
      listener(_connectionStatus);
    }
  }
}

/// مزود خدمة الشبكة
final networkServiceProvider = Provider<NetworkService>((ref) {
  return NetworkService();
});

/// مزود حالة الاتصال
final connectionStatusProvider = StateProvider<ConnectionStatus>((ref) {
  return NetworkService().connectionStatus;
});
