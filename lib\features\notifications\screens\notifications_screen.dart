// بسم الله الرحمن الرحيم
// شاشة الإشعارات - تعرض قائمة الإشعارات للمستخدم

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/auth/auth_service.dart';
import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
// سيتم استبدال هذا لاحقًا بمكون AnimatedLogo
// import '../../../features/splash_screen/widgets/loading_indicator.dart' as new_loading;
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import 'notification_details_screen.dart';

/// شاشة الإشعارات
class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  static const String routeName = '/notifications';

  @override
  ConsumerState<NotificationsScreen> createState() =>
      _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  bool _isLoading = false;

  // تحديث حالة الإشعار
  Future<void> _markAsRead(NotificationModel notification) async {
    if (notification.status == NotificationStatus.unread) {
      setState(() {
        _isLoading = true;
      });

      try {
        final currentUser = ref.read(authServiceProvider).currentUser;
        if (currentUser != null) {
          await ref.read(notificationServiceProvider).updateNotificationStatus(
                currentUser.uid,
                notification.id,
                NotificationStatus.read,
              );

          // تحديث مزودات الإشعارات
          // ignore: unused_result
          ref.refresh(currentUserNotificationsProvider);
          // ignore: unused_result
          ref.refresh(unreadNotificationsCountProvider);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // حذف الإشعار
  Future<void> _deleteNotification(NotificationModel notification) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = ref.read(authServiceProvider).currentUser;
      if (currentUser != null) {
        await ref.read(notificationServiceProvider).deleteNotification(
              currentUser.uid,
              notification.id,
            );

        // تحديث مزودات الإشعارات
        // ignore: unused_result
        ref.refresh(currentUserNotificationsProvider);
        // ignore: unused_result
        ref.refresh(unreadNotificationsCountProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الإشعار')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // حذف جميع الإشعارات
  Future<void> _deleteAllNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = ref.read(authServiceProvider).currentUser;
      if (currentUser != null) {
        await ref.read(notificationServiceProvider).deleteAllUserNotifications(
              currentUser.uid,
            );

        // تحديث مزودات الإشعارات
        // ignore: unused_result
        ref.refresh(currentUserNotificationsProvider);
        // ignore: unused_result
        ref.refresh(unreadNotificationsCountProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف جميع الإشعارات')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // الحصول على أيقونة نوع الإشعار
  IconData _getNotificationTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return Icons.money_off;
      case NotificationType.message:
        return Icons.message;
      case NotificationType.system:
        return Icons.system_update;
      case NotificationType.general:
        return Icons.notifications;
    }
  }

  // الحصول على لون نوع الإشعار
  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return Colors.red;
      case NotificationType.message:
        return Colors.blue;
      case NotificationType.system:
        return Colors.purple;
      case NotificationType.general:
        return Colors.orange;
    }
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم ${DateFormat.Hm().format(date)}';
    } else if (difference.inDays == 1) {
      return 'أمس ${DateFormat.Hm().format(date)}';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return DateFormat.yMd().format(date);
    }
  }

  @override
  Widget build(BuildContext context) {
    final notificationsAsync = ref.watch(currentUserNotificationsProvider);

    return Scaffold(
      backgroundColor: AppColors.mainColor,
      appBar: AppBar(
        backgroundColor: AppColors.mainColor,
        title: const Text('الإشعارات', style: TextStyle(color: Colors.white)),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0.0,
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_sweep, color: Colors.white),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('حذف جميع الإشعارات'),
                  content: const Text(
                      'هل أنت متأكد من حذف جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _deleteAllNotifications();
                      },
                      child: const Text('حذف'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 15),
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
            ),
          ),
          child: _isLoading
              ? const FullScreenLoadingIndicator(
                  message: 'جاري تحديث الإشعارات...',
                )
              // سيتم استبدال هذا لاحقًا بمكون AnimatedLogo
              /*
              ? const new_loading.FullScreenLoadingIndicator(
                  type: new_loading.LoadingIndicatorType.logo,
                  message: 'جاري تحديث الإشعارات...',
                )
              */
              : notificationsAsync.when(
                  data: (notifications) {
                    if (notifications.isEmpty) {
                      return const Center(
                        child: Text(
                          'لا توجد إشعارات',
                          style: TextStyle(
                              fontSize: 18, color: AppColors.titleColor),
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        // تحديث مزودات الإشعارات
                        ref.invalidate(currentUserNotificationsProvider);
                        ref.invalidate(unreadNotificationsCountProvider);
                        return Future.delayed(
                            const Duration(milliseconds: 500));
                      },
                      child: ListView.builder(
                        itemCount: notifications.length,
                        itemBuilder: (context, index) {
                          final notification = notifications[index];
                          return Dismissible(
                            key: Key(notification.id),
                            direction: DismissDirection.endToStart,
                            background: Container(
                              color: Colors.red,
                              alignment: Alignment.centerRight,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: const Icon(
                                Icons.delete,
                                color: Colors.white,
                              ),
                            ),
                            onDismissed: (direction) {
                              _deleteNotification(notification);
                            },
                            child: Card(
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              color: notification.status ==
                                      NotificationStatus.unread
                                  ? AppColors.notificationUnread
                                  : null,
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: _getNotificationTypeColor(
                                      notification.type),
                                  child: Icon(
                                    _getNotificationTypeIcon(notification.type),
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(
                                  notification.title,
                                  style: TextStyle(
                                    fontWeight: notification.status ==
                                            NotificationStatus.unread
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      notification.body,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _formatDate(notification.createdAt),
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: AppColors.greyTextColor,
                                      ),
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  // تعليم الإشعار كمقروء ثم الانتقال إلى صفحة التفاصيل
                                  _markAsRead(notification);
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          NotificationDetailsScreen(
                                        notification: notification,
                                      ),
                                    ),
                                  );
                                },
                                trailing: notification.status ==
                                        NotificationStatus.unread
                                    ? Container(
                                        width: 12,
                                        height: 12,
                                        decoration: const BoxDecoration(
                                          color: AppColors.mainColor,
                                          shape: BoxShape.circle,
                                        ),
                                      )
                                    : null,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                  loading: () => const LoadingIndicatorWidget(
                    message: 'جاري تحميل الإشعارات...',
                  ),
                  // سيتم استبدال هذا لاحقًا بمكون AnimatedLogo
                  /*
              loading: () => const new_loading.LoadingIndicator(
                type: new_loading.LoadingIndicatorType.logo,
                message: 'جاري تحميل الإشعارات...',
              ),
              */
                  error: (error, stackTrace) => Center(
                    child: Text('حدث خطأ: $error',
                        style: const TextStyle(color: Colors.red)),
                  ),
                ),
        ),
      ),
    );
  }
}
