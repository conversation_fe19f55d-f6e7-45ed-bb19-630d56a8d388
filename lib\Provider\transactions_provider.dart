import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/transition_model.dart';
import '../repository/transactions_repo.dart';

// استخدام مزود واحد للمستودع
final transitionRepoProvider = Provider<TransitionRepo>((ref) {
  return TransitionRepo();
});

// استخدام مزود واحد للمستودع
final purchaseTransitionRepoProvider = Provider<PurchaseTransitionRepo>((ref) {
  return PurchaseTransitionRepo();
});

// استخدام FutureProvider.autoDispose لتجنب تخزين البيانات القديمة
final transitionProvider =
    FutureProvider.autoDispose<List<SalesTransitionModel>>((ref) async {
  // الحصول على المستودع من المزود
  final repo = ref.watch(transitionRepoProvider);

  try {
    // تعيين مهلة زمنية للحصول على البيانات
    return await repo.getAllTransition().timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        debugPrint('انتهت مهلة الحصول على بيانات المبيعات في المزود');
        throw TimeoutException('انتهت مهلة الحصول على بيانات المبيعات');
      },
    );
  } catch (e) {
    debugPrint('خطأ في مزود بيانات المبيعات: $e');
    // إعادة قائمة فارغة في حالة الخطأ
    return [];
  }
});

// استخدام FutureProvider.autoDispose لتجنب تخزين البيانات القديمة
final purchaseTransitionProvider =
    FutureProvider.autoDispose<List<dynamic>>((ref) async {
  // الحصول على المستودع من المزود
  final repo = ref.watch(purchaseTransitionRepoProvider);

  try {
    // تعيين مهلة زمنية للحصول على البيانات
    return await repo.getAllTransition().timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        debugPrint('انتهت مهلة الحصول على بيانات المشتريات في المزود');
        throw TimeoutException('انتهت مهلة الحصول على بيانات المشتريات');
      },
    );
  } catch (e) {
    debugPrint('خطأ في مزود بيانات المشتريات: $e');
    // إعادة قائمة فارغة في حالة الخطأ
    return [];
  }
});
