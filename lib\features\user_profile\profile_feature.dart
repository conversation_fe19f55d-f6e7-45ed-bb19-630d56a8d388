// بسم الله الرحمن الرحيم
// ميزة الملف الشخصي - تنفيذ واجهة الميزة للملف الشخصي

import 'package:flutter/material.dart';
import '../core/feature_interface.dart';
import 'screens/profile_screen.dart';
import 'services/user_profile_service.dart';

/// ميزة الملف الشخصي
class ProfileFeature implements FeatureInterface {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من ميزة الملف الشخصي
  static final ProfileFeature _instance = ProfileFeature._internal();
  factory ProfileFeature() => _instance;
  ProfileFeature._internal();

  // حالة تفعيل الميزة
  bool _isEnabled = true;

  @override
  String get featureName => 'الملف الشخصي';

  @override
  String get featureDescription => 'عرض وتعديل معلومات الملف الشخصي';

  @override
  IconData get featureIcon => Icons.person;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Widget getMainScreen() {
    return const ProfileScreen();
  }

  @override
  Map<String, WidgetBuilder> getRoutes() {
    return {
      ProfileScreen.routeName: (context) => const ProfileScreen(),
    };
  }

  @override
  Future<void> initialize() async {
    // تهيئة خدمة الملف الشخصي
    await UserProfileService().initialize();
  }

  @override
  Future<void> dispose() async {
    // لا شيء للتنظيف حاليًا
  }
}
