import 'dart:convert';

/// نموذج بيانات تحديث التطبيق
class AppUpdateModel {
  final String currentVersion;
  final String latestVersion;
  final String releaseNotes;
  final String downloadUrl;
  final bool isForceUpdate;
  final int updateSize; // بالميجابايت
  final DateTime releaseDate;
  final String minRequiredVersion;
  final bool isUpdateAvailable;

  AppUpdateModel({
    required this.currentVersion,
    required this.latestVersion,
    required this.releaseNotes,
    required this.downloadUrl,
    this.isForceUpdate = false,
    this.updateSize = 0,
    DateTime? releaseDate,
    this.minRequiredVersion = '',
    this.isUpdateAvailable = false,
  }) : releaseDate = releaseDate ?? DateTime.now();

  /// إنشاء نسخة من النموذج مع تحديث بعض الحقول
  AppUpdateModel copyWith({
    String? currentVersion,
    String? latestVersion,
    String? releaseNotes,
    String? downloadUrl,
    bool? isForceUpdate,
    int? updateSize,
    DateTime? releaseDate,
    String? minRequiredVersion,
    bool? isUpdateAvailable,
  }) {
    return AppUpdateModel(
      currentVersion: currentVersion ?? this.currentVersion,
      latestVersion: latestVersion ?? this.latestVersion,
      releaseNotes: releaseNotes ?? this.releaseNotes,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      isForceUpdate: isForceUpdate ?? this.isForceUpdate,
      updateSize: updateSize ?? this.updateSize,
      releaseDate: releaseDate ?? this.releaseDate,
      minRequiredVersion: minRequiredVersion ?? this.minRequiredVersion,
      isUpdateAvailable: isUpdateAvailable ?? this.isUpdateAvailable,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'currentVersion': currentVersion,
      'latestVersion': latestVersion,
      'releaseNotes': releaseNotes,
      'downloadUrl': downloadUrl,
      'isForceUpdate': isForceUpdate,
      'updateSize': updateSize,
      'releaseDate': releaseDate.toIso8601String(),
      'minRequiredVersion': minRequiredVersion,
      'isUpdateAvailable': isUpdateAvailable,
    };
  }

  /// إنشاء نموذج من Map
  factory AppUpdateModel.fromMap(Map<String, dynamic> map) {
    // التحقق من وجود حقل updateUrl أو downloadUrl
    final downloadUrl = map['updateUrl'] ?? map['downloadUrl'] ?? '';

    // محاولة استخراج حجم التحديث
    int updateSize = 0;
    if (map['updateSize'] != null) {
      updateSize = map['updateSize'] is int
          ? map['updateSize']
          : int.tryParse(map['updateSize'].toString()) ?? 0;
    }

    return AppUpdateModel(
      currentVersion: map['currentVersion'] ?? '',
      latestVersion: map['latestVersion'] ?? '',
      releaseNotes: map['releaseNotes'] ?? '',
      downloadUrl: downloadUrl,
      isForceUpdate: map['forceUpdate'] ?? map['isForceUpdate'] ?? false,
      updateSize: updateSize,
      releaseDate: map['releaseDate'] != null
          ? DateTime.parse(map['releaseDate'])
          : null,
      minRequiredVersion: map['minRequiredVersion'] ?? '',
      isUpdateAvailable: map['isUpdateAvailable'] ?? false,
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء نموذج من JSON
  factory AppUpdateModel.fromJson(String source) =>
      AppUpdateModel.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'AppUpdateModel(currentVersion: $currentVersion, latestVersion: $latestVersion, isUpdateAvailable: $isUpdateAvailable, isForceUpdate: $isForceUpdate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AppUpdateModel &&
        other.currentVersion == currentVersion &&
        other.latestVersion == latestVersion &&
        other.releaseNotes == releaseNotes &&
        other.downloadUrl == downloadUrl &&
        other.isForceUpdate == isForceUpdate &&
        other.updateSize == updateSize &&
        other.releaseDate == releaseDate &&
        other.minRequiredVersion == minRequiredVersion &&
        other.isUpdateAvailable == isUpdateAvailable;
  }

  @override
  int get hashCode {
    return currentVersion.hashCode ^
        latestVersion.hashCode ^
        releaseNotes.hashCode ^
        downloadUrl.hashCode ^
        isForceUpdate.hashCode ^
        updateSize.hashCode ^
        releaseDate.hashCode ^
        minRequiredVersion.hashCode ^
        isUpdateAvailable.hashCode;
  }
}

/// نموذج بيانات سجل التغييرات
class ChangelogModel {
  final String version;
  final String releaseNotes;
  final DateTime releaseDate;
  final int updateSize; // بالميجابايت

  ChangelogModel({
    required this.version,
    required this.releaseNotes,
    required this.releaseDate,
    this.updateSize = 0,
  });

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'version': version,
      'releaseNotes': releaseNotes,
      'releaseDate': releaseDate.toIso8601String(),
      'updateSize': updateSize,
    };
  }

  /// إنشاء نموذج من Map
  factory ChangelogModel.fromMap(Map<String, dynamic> map) {
    return ChangelogModel(
      version: map['version'] ?? '',
      releaseNotes: map['releaseNotes'] ?? '',
      releaseDate: map['releaseDate'] != null
          ? DateTime.parse(map['releaseDate'])
          : DateTime.now(),
      updateSize: map['updateSize'] ?? 0,
    );
  }
}

/// نموذج حالة التنزيل
class DownloadStatus {
  final bool isDownloading;
  final double progress; // من 0 إلى 1
  final String downloadedBytes;
  final String totalBytes;
  final String speed; // سرعة التنزيل
  final String remainingTime; // الوقت المتبقي
  final String filePath; // مسار الملف المنزل
  final bool isCompleted;
  final bool hasError;
  final String errorMessage;

  DownloadStatus({
    this.isDownloading = false,
    this.progress = 0.0,
    this.downloadedBytes = '0 KB',
    this.totalBytes = '0 KB',
    this.speed = '0 KB/s',
    this.remainingTime = '0s',
    this.filePath = '',
    this.isCompleted = false,
    this.hasError = false,
    this.errorMessage = '',
  });

  /// إنشاء نسخة من النموذج مع تحديث بعض الحقول
  DownloadStatus copyWith({
    bool? isDownloading,
    double? progress,
    String? downloadedBytes,
    String? totalBytes,
    String? speed,
    String? remainingTime,
    String? filePath,
    bool? isCompleted,
    bool? hasError,
    String? errorMessage,
  }) {
    return DownloadStatus(
      isDownloading: isDownloading ?? this.isDownloading,
      progress: progress ?? this.progress,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
      totalBytes: totalBytes ?? this.totalBytes,
      speed: speed ?? this.speed,
      remainingTime: remainingTime ?? this.remainingTime,
      filePath: filePath ?? this.filePath,
      isCompleted: isCompleted ?? this.isCompleted,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
