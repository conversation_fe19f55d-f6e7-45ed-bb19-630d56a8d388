
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';
import '../debug/login_debug.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:amrdev_win_pos/Repository/profile_details_repo.dart';
import 'package:amrdev_win_pos/Screen/Home/home_screen.dart';

import '../Screen/Authentication/add_profile.dart';
import '../const.dart';
import '../model/user_role_model.dart';

final logInProvider = ChangeNotifierProvider((ref) => LogInRepo());

class LogInRepo extends ChangeNotifier {
  String email = '';
  String password = '';

  Future<void> signIn(BuildContext context) async {
    EasyLoading.show(status: 'Login...');

    // تشخيص مشاكل تسجيل الدخول في وضع التطوير
    if (kDebugMode) {
      await LoginDebugger.diagnoseLoginIssues(email, password);
    }

    try {
      mainLoginEmail = email;
      mainLoginPassword = password;
      await FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password)
          .then((value) async {
            debugPrint('🔐 تم تسجيل الدخول بنجاح في Firebase Auth');
            debugPrint('👤 المستخدم: ${value.user?.email}');

            // تحديث constUserId أولاً للمستخدم الرئيسي
            final currentUserId = FirebaseAuth.instance.currentUser!.uid;
            constUserId = currentUserId;
            debugPrint('🔄 تم تحديث constUserId للمستخدم الرئيسي: $constUserId');

            bool isSubUser = await checkSubUser();
            debugPrint('🔍 نتيجة فحص المستخدم الفرعي: $isSubUser');

            if (isSubUser) {
              debugPrint('👥 المستخدم فرعي - الانتقال للشاشة الرئيسية');
              debugPrint('🔄 constUserId للمستخدم الفرعي: $constUserId');
              EasyLoading.showSuccess('Successful');
              setUserDataOnLocalData(
                uid: constUserId,
                subUserTitle: constSubUserTitle,
                isSubUser: true,
              );
              putUserDataImidiyate(uid: constUserId, title: constSubUserTitle, isSubUse: true);
              Navigator.of(context).pushNamed(MtHomeScreen.route);
            } else {
              debugPrint('👤 مستخدم رئيسي - فحص إعداد الملف الشخصي');
              debugPrint('🔄 constUserId للمستخدم الرئيسي: $constUserId');
              EasyLoading.showSuccess('Successful');
              await setUserDataOnLocalData(
                uid: currentUserId,
                subUserTitle: '',
                isSubUser: false,
              );
              putUserDataImidiyate(
                uid: currentUserId,
                title: '',
                isSubUse: false,
              );

              try {
                bool isProfileSetup = await ProfileRepo().isProfileSetupDone();
                debugPrint('🔍 حالة إعداد الملف الشخصي: $isProfileSetup');

                if (isProfileSetup) {
                  debugPrint('✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية');
                  Navigator.of(context).pushNamed(MtHomeScreen.route);
                } else {
                  debugPrint('⚠️ الملف الشخصي غير معد - الانتقال لإعداد الملف الشخصي');
                  const ProfileAdd().launch(context);
                }
              } catch (e) {
                debugPrint('❌ خطأ في فحص إعداد الملف الشخصي: $e');
                debugPrint('🔄 الانتقال لإعداد الملف الشخصي كإجراء احتياطي');
                const ProfileAdd().launch(context);
              }
            }
          });
    } on FirebaseAuthException catch (e) {
      EasyLoading.showError(e.message.toString());
      if (e.code == 'user-not-found') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No user found for that email.'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (e.code == 'wrong-password') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Wrong password provided for that user.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      EasyLoading.showError(e.toString());
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<bool> checkSubUser() async {
    debugPrint('🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: $email');

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isSubUser = false;

    try {
      final snapshot = await FirebaseDatabaseService.ref('Admin Panel')
          .child('User Role')
          .orderByKey()
          .get();

      debugPrint('📊 تم الحصول على بيانات User Role من Firebase');

      if (!snapshot.exists || snapshot.value == null) {
        debugPrint('❌ لا توجد بيانات في Admin Panel/User Role');
        await prefs.setBool('isSubUser', false);
        debugPrint('🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)');
        return false;
      }

      debugPrint('📋 عدد المستخدمين الفرعيين: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        try {
          if (element.value == null) continue;

          var data = UserRoleModel.fromJson(
            jsonDecode(jsonEncode(element.value)),
          );
          debugPrint('🔍 فحص المستخدم: ${data.email}');

          if (data.email == email) {
            debugPrint('✅ تم العثور على المستخدم الفرعي: ${data.email}');
            debugPrint('🔍 صلاحية المبيعات: ${data.salePermission}');
            debugPrint('🔍 صلاحية الأطراف: ${data.partiesPermission}');
            debugPrint('🔍 صلاحية التقارير: ${data.reportsPermission}');

            // حفظ بيانات الصلاحيات في SharedPreferences
            await prefs.setString('userPermission', json.encode(data));
            await prefs.setString('subUserEmail', email);
            await prefs.setBool('isSubUser', true);
            await prefs.setString('userId', data.databaseId);
            await prefs.setString('subUserTitle', data.userTitle);

            debugPrint('💾 تم حفظ بيانات الصلاحيات في SharedPreferences');

            // تحديث المتغيرات العامة
            finalUserRoleModel = data;
            constUserId = data.databaseId;
            constSubUserTitle = data.userTitle;
            isSubUser = true;
            break; // الخروج من الحلقة بعد العثور على المستخدم
          }
        } catch (e) {
          debugPrint('❌ خطأ في معالجة بيانات المستخدم: $e');
          continue;
        }
      }

      if (!isSubUser) {
        debugPrint('ℹ️ لم يتم العثور على مستخدم فرعي - مستخدم رئيسي');
        await prefs.setBool('isSubUser', false);
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من المستخدمين الفرعيين: $e');
      // في حالة الخطأ، نعتبر المستخدم رئيسي
      await prefs.setBool('isSubUser', false);
    }

    debugPrint('🔄 انتهاء التحقق - النتيجة: $isSubUser');
    return isSubUser;
  }
}

Future<void> sendEmailVerification() async {
  User? user = FirebaseAuth.instance.currentUser;

  try {
    await user?.sendEmailVerification();
    debugPrint('Email verification link sent');
  } catch (e) {
    debugPrint('Error sending email verification link: $e');
  }
}
