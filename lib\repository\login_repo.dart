// ignore_for_file: use_build_context_synchronously, unnecessary_import, unused_local_variable

import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/local_database_service.dart';
import 'package:mobile_pos/services/user_accounts_service.dart';
import 'package:mobile_pos/services/user_log_service.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/model/user_role_model.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/subscription.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../Screens/Authentication/success_screen.dart';
import '../constant.dart';

final logInProvider = ChangeNotifierProvider((ref) => LogInRepo());

class LogInRepo extends ChangeNotifier {
  String email = '';
  String password = '';

  Future<void> signIn(BuildContext context) async {
    EasyLoading.show(status: 'بندخل...');
    try {
      // تسجيل الدخول باستخدام Firebase Auth
      UserCredential userCredential = await FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password);

      // حفظ بيانات المستخدم في التخزين المحلي
      if (userCredential.user != null) {
        final user = userCredential.user!;

        // التحقق من وجود بيانات المستخدم في Firebase
        try {
          final userRef = FirebaseDatabase.instance.ref(
            '${user.uid}/Personal Information',
          );
          final snapshot = await userRef.get();

          // الحصول على اسم المستخدم من البيانات إن وجدت
          String displayName = email.split('@')[0];
          if (snapshot.exists) {
            final data = snapshot.value as Map<dynamic, dynamic>?;
            final companyName = data?['companyName'] as String?;
            if (companyName != null && companyName.isNotEmpty) {
              displayName = companyName;
            }
          }

          // حفظ بيانات المستخدم في التخزين المحلي
          final userAccount = UserAccount(
            email: user.email ?? email,
            userId: user.uid,
            displayName: displayName,
            photoUrl: user.photoURL,
            lastLogin: DateTime.now(),
          );
          await UserAccountsService.saveAccount(userAccount);

          // حفظ بيانات المستخدم في قاعدة البيانات المحلية على ويندوز
          if (Platform.isWindows) {
            try {
              // تهيئة قاعدة البيانات المحلية إذا لم تكن مهيأة بالفعل
              if (!LocalDatabaseService.isInitialized) {
                await LocalDatabaseService.initialize();
              }

              // حفظ بيانات المستخدم
              await LocalDatabaseService.saveUserData(
                userId: user.uid,
                displayName: displayName,
                email: user.email ?? email,
                photoUrl: user.photoURL,
                subscriptionType: 'Admin',
              );

              // حفظ ميزات الاشتراك الافتراضية
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Sales',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Purchase',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Customers',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Products',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Reports',
                enabled: true,
              );

              // حفظ بيانات افتراضية للمستخدم
              await LocalDatabaseService.saveData(
                '${user.uid}/Personal Information',
                {'companyName': displayName, 'phone': '0000000000'},
              );

              await LocalDatabaseService.saveData('${user.uid}/Subscription', {
                'name': 'Admin',
                'status': 'active',
              });

              debugPrint(
                'تم حفظ بيانات المستخدم في قاعدة البيانات المحلية على ويندوز',
              );
            } catch (e) {
              debugPrint(
                'خطأ في حفظ بيانات المستخدم في قاعدة البيانات المحلية: $e',
              );
            }
          }

          // التحقق من وجود بيانات الاشتراك
          try {
            final subscriptionRef = FirebaseDatabase.instance.ref(
              '${user.uid}/Subscription',
            );
            final subscriptionSnapshot = await subscriptionRef.get();

            if (!subscriptionSnapshot.exists) {
              debugPrint(
                'لم يتم العثور على بيانات الاشتراك، جاري إعداد الاشتراك المجاني',
              );
              // استيراد دالة إعداد الاشتراك المجاني
              await setupFreeSubscription();
            }
          } catch (e) {
            debugPrint('خطأ في التحقق من بيانات الاشتراك: $e');
            // إعداد الاشتراك المجاني في حالة حدوث خطأ
            await setupFreeSubscription();
          }
        } catch (e) {
          debugPrint('خطأ في الحصول على بيانات المستخدم: $e');

          // حفظ بيانات المستخدم الأساسية على الأقل
          final userAccount = UserAccount(
            email: user.email ?? email,
            userId: user.uid,
            displayName: email.split('@')[0],
            photoUrl: user.photoURL,
            lastLogin: DateTime.now(),
          );
          await UserAccountsService.saveAccount(userAccount);

          // حفظ بيانات المستخدم في قاعدة البيانات المحلية على ويندوز
          if (Platform.isWindows) {
            try {
              // تهيئة قاعدة البيانات المحلية إذا لم تكن مهيأة بالفعل
              if (!LocalDatabaseService.isInitialized) {
                await LocalDatabaseService.initialize();
              }

              // حفظ بيانات المستخدم
              await LocalDatabaseService.saveUserData(
                userId: user.uid,
                displayName: email.split('@')[0],
                email: user.email ?? email,
                photoUrl: user.photoURL,
                subscriptionType: 'Admin',
              );

              // حفظ ميزات الاشتراك الافتراضية
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Sales',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Purchase',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Customers',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Products',
                enabled: true,
              );
              await LocalDatabaseService.saveSubscriptionFeature(
                userId: user.uid,
                feature: 'Reports',
                enabled: true,
              );

              debugPrint(
                'تم حفظ بيانات المستخدم الأساسية في قاعدة البيانات المحلية على ويندوز',
              );
            } catch (e) {
              debugPrint(
                'خطأ في حفظ بيانات المستخدم الأساسية في قاعدة البيانات المحلية: $e',
              );
            }
          }

          // إعداد الاشتراك المجاني في حالة حدوث خطأ
          await setupFreeSubscription();
        }
      }

      EasyLoading.showSuccess('Successful');

      // تسجيل نشاط تسجيل الدخول
      try {
        final userLogService = UserLogService();
        await userLogService.logUserAction(
          actionType: UserLogActionTypes.userLogin,
          description: 'تم تسجيل الدخول بنجاح',
          data: {
            'email': email,
            'timestamp': DateTime.now().toIso8601String(),
            'deviceInfo': Platform.operatingSystem,
          },
        );
        debugPrint('تم تسجيل نشاط تسجيل الدخول بنجاح');
      } catch (e) {
        debugPrint('خطأ في تسجيل نشاط تسجيل الدخول: $e');
      }

      // التحقق من المستخدمين الفرعيين قبل الانتقال
      debugPrint('🚀 بدء التحقق من المستخدمين الفرعيين للإيميل: $email');
      bool isSubUserFound = await _checkSubUser(email);
      debugPrint('🎯 نتيجة التحقق من المستخدم الفرعي: $isSubUserFound');

      // الانتقال إلى شاشة النجاح
      if (context.mounted) {
        await Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => SuccessScreen(email: email)),
          (route) => false,
        );
      }
    } on FirebaseAuthException catch (e) {
      EasyLoading.showError(e.message.toString());
      if (e.code == 'user-not-found') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No user found for that email.'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (e.code == 'wrong-password') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Wrong password provided for that user.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      EasyLoading.showError('Failed with Error');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // تسجيل الخروج وتنظيف الموارد
  Future<void> signOut(BuildContext context) async {
    try {
      // تسجيل نشاط تسجيل الخروج
      try {
        final userLogService = UserLogService();
        final currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser != null) {
          await userLogService.logUserAction(
            actionType: UserLogActionTypes.userLogout,
            description: 'تم تسجيل الخروج بنجاح',
            data: {
              'email': currentUser.email ?? 'غير معروف',
              'timestamp': DateTime.now().toIso8601String(),
              'deviceInfo': Platform.operatingSystem,
            },
          );
          debugPrint('تم تسجيل نشاط تسجيل الخروج بنجاح');
        }
      } catch (e) {
        debugPrint('خطأ في تسجيل نشاط تسجيل الخروج: $e');
      }

      // إلغاء مزامنة جميع المراجع قبل تسجيل الخروج
      FirebaseDatabaseService.clearAllSyncedReferences();

      // تسجيل الخروج من Firebase Auth
      await FirebaseAuth.instance.signOut();
    } catch (e) {
      debugPrint('خطأ في تسجيل الخروج: $e');
    }
  }

  /// دالة لإعداد الاشتراك المجاني
  Future<void> setupFreeSubscription() async {
    try {
      debugPrint('بدء إعداد الاشتراك المجاني...');

      // تعيين اسم الاشتراك في Subscription
      Subscription.selectedItem = 'Admin';

      // الحصول على معرف المستخدم الحالي
      final userId = FirebaseAuth.instance.currentUser!.uid;

      // إنشاء نموذج الاشتراك
      final Map<String, dynamic> subscriptionData = {
        'subscriptionName': 'Admin',
        'subscriptionDate': DateTime.now().toString(),
        'products': 'unlimited',
        'duration': 'unlimited',
        'dueNumber': 'unlimited',
        'partiesNumber': 'unlimited',
        'purchaseNumber': 'unlimited',
        'saleNumber': 'unlimited',
      };

      // معالجة خاصة لمنصة ويندوز
      if (Platform.isWindows) {
        try {
          // تهيئة قاعدة البيانات المحلية إذا لم تكن مهيأة بالفعل
          if (!LocalDatabaseService.isInitialized) {
            await LocalDatabaseService.initialize();
          }

          // حفظ بيانات الاشتراك في قاعدة البيانات المحلية
          await LocalDatabaseService.saveData(
            '$userId/Subscription',
            subscriptionData,
          );

          // حفظ ميزات الاشتراك الافتراضية
          await LocalDatabaseService.saveSubscriptionFeature(
            userId: userId,
            feature: 'Sales',
            enabled: true,
          );
          await LocalDatabaseService.saveSubscriptionFeature(
            userId: userId,
            feature: 'Purchase',
            enabled: true,
          );
          await LocalDatabaseService.saveSubscriptionFeature(
            userId: userId,
            feature: 'Customers',
            enabled: true,
          );
          await LocalDatabaseService.saveSubscriptionFeature(
            userId: userId,
            feature: 'Products',
            enabled: true,
          );
          await LocalDatabaseService.saveSubscriptionFeature(
            userId: userId,
            feature: 'Reports',
            enabled: true,
          );

          debugPrint(
            'تم إعداد الاشتراك المجاني في قاعدة البيانات المحلية للمستخدم: $userId',
          );
        } catch (e) {
          debugPrint(
            'خطأ في إعداد الاشتراك المجاني في قاعدة البيانات المحلية: $e',
          );
        }
      } else {
        // الحصول على مرجع الاشتراك
        final DatabaseReference subscriptionRef = FirebaseDatabase.instance
            .ref()
            .child(userId)
            .child('Subscription');

        // حفظ بيانات الاشتراك
        await subscriptionRef.set(subscriptionData);
      }

      debugPrint('تم إعداد الاشتراك المجاني بنجاح للمستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في إعداد الاشتراك المجاني: $e');
    }
  }

  /// التحقق من المستخدمين الفرعيين وحفظ بياناتهم
  Future<bool> _checkSubUser(String email) async {
    try {
      debugPrint('🔍 التحقق من المستخدمين الفرعيين للإيميل: $email');

      final prefs = await SharedPreferences.getInstance();
      bool isSubUserFound = false;

      // البحث في Admin Panel/User Role
      final userRoleRef = FirebaseDatabase.instance.ref(
        'Admin Panel/User Role',
      );
      final snapshot = await userRoleRef.orderByKey().get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          try {
            var data = UserRoleModel.fromJson(
              jsonDecode(jsonEncode(element.value)),
            );

            if (data.email == email) {
              debugPrint('✅ تم العثور على المستخدم الفرعي: ${data.email}');
              debugPrint('🔍 حالة التفعيل: ${data.isActive}');

              // التحقق من حالة تفعيل المستخدم
              if (!data.isActive) {
                debugPrint('❌ المستخدم معطل - لا يمكن تسجيل الدخول');
                throw Exception(
                  'تم تعطيل هذا المستخدم. يرجى التواصل مع المشرف.',
                );
              }

              debugPrint('🔍 صلاحية المبيعات: ${data.salePermission}');
              debugPrint('🔍 صلاحية الأطراف: ${data.partiesPermission}');
              debugPrint('🔍 صلاحية الدردشة: ${data.aiChatPermission}');

              // حفظ بيانات الصلاحيات في SharedPreferences
              await prefs.setString(
                'userPermission',
                json.encode(data.toJson()),
              );
              await prefs.setString('subUserEmail', email);
              await prefs.setBool('isSubUser', true);
              await prefs.setString('userId', data.databaseId);
              await prefs.setString('subUserTitle', data.userTitle);

              debugPrint('💾 تم حفظ بيانات الصلاحيات في SharedPreferences');

              // تحديث المتغيرات العامة
              constUserId = data.databaseId;
              constSubUserTitle = data.userTitle;
              isSubUser = true;
              subUserEmail = email;
              finalUserRoleModel = data;

              isSubUserFound = true;
              break;
            }
          } catch (e) {
            debugPrint('خطأ في معالجة بيانات المستخدم: $e');
            continue;
          }
        }
      }

      if (!isSubUserFound) {
        debugPrint('ℹ️ لم يتم العثور على مستخدم فرعي - مستخدم رئيسي');
        // تأكد من تعيين isSubUser = false للمستخدم الرئيسي
        await prefs.setBool('isSubUser', false);
        isSubUser = false;
      }

      return isSubUserFound;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من المستخدمين الفرعيين: $e');
      return false;
    }
  }
}
