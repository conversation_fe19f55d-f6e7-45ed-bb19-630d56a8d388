// بسم الله الرحمن الرحيم
// نموذج الإشعار - يمثل بيانات الإشعار

import 'dart:convert';

/// نوع الإشعار
enum NotificationType {
  /// إشعار عام
  general,

  /// إشعار المديونية
  debt,

  /// إشعار الرسائل
  message,

  /// إشعار النظام
  system,
}

/// حالة الإشعار
enum NotificationStatus {
  /// غير مقروء
  unread,

  /// مقروء
  read,

  /// تم التفاعل معه
  acted,
}

/// أولوية الإشعار
enum NotificationPriority {
  /// منخفضة
  low,

  /// متوسطة
  medium,

  /// عالية
  high,
}

/// نموذج الإشعار
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationStatus status;
  final NotificationPriority priority;
  final DateTime createdAt;
  final String? imageUrl;
  final String? actionUrl;
  final Map<String, dynamic>? data;
  final String? senderId;
  final String recipientId;
  final List<String> deletedByUsers; // قائمة المستخدمين الذين حذفوا الإشعار

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.status,
    required this.priority,
    required this.createdAt,
    required this.recipientId,
    this.imageUrl,
    this.actionUrl,
    this.data,
    this.senderId,
    List<String>? deletedByUsers,
  }) : deletedByUsers = deletedByUsers ?? [];

  /// نسخ النموذج مع تحديث بعض الحقول
  NotificationModel copyWith({
    String? title,
    String? body,
    NotificationType? type,
    NotificationStatus? status,
    NotificationPriority? priority,
    DateTime? createdAt,
    String? imageUrl,
    String? actionUrl,
    Map<String, dynamic>? data,
    String? senderId,
    String? recipientId,
    List<String>? deletedByUsers,
  }) {
    return NotificationModel(
      id: id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
      senderId: senderId ?? this.senderId,
      recipientId: recipientId ?? this.recipientId,
      deletedByUsers: deletedByUsers ?? this.deletedByUsers,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.index,
      'status': status.index,
      'priority': priority.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'data': data,
      'senderId': senderId,
      'recipientId': recipientId,
      'deletedByUsers': deletedByUsers,
    };
  }

  /// إنشاء نموذج من Map
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    List<String> deletedUsers = [];
    if (map['deletedByUsers'] != null) {
      if (map['deletedByUsers'] is List) {
        deletedUsers = List<String>.from(map['deletedByUsers']);
      } else if (map['deletedByUsers'] is Map) {
        // في حالة تخزين البيانات كـ Map في Firebase
        final deletedMap = Map<String, dynamic>.from(map['deletedByUsers']);
        deletedUsers = deletedMap.keys.toList();
      }
    }

    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      type: NotificationType.values[map['type'] ?? 0],
      status: NotificationStatus.values[map['status'] ?? 0],
      priority: NotificationPriority.values[map['priority'] ?? 0],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      imageUrl: map['imageUrl'],
      actionUrl: map['actionUrl'],
      data: map['data'],
      senderId: map['senderId'],
      recipientId: map['recipientId'] ?? '',
      deletedByUsers: deletedUsers,
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory NotificationModel.fromJson(String source) =>
      NotificationModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, status: $status)';
  }
}
