// مزود التنبيهات للتقارير
import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/currency.dart';

/// مزود التنبيهات للتقارير
final reportAlertsProvider =
    StateNotifierProvider<ReportAlertsNotifier, List<ReportAlert>>((ref) {
  return ReportAlertsNotifier();
});

/// مزود عدد التنبيهات غير المقروءة
final unreadAlertsCountProvider = Provider<int>((ref) {
  final alerts = ref.watch(reportAlertsProvider);
  return alerts.where((alert) => !alert.isRead).length;
});

/// مزود حالة التنبيهات
final alertsStateProvider = StateProvider<AlertsState>((ref) {
  return AlertsState(isLoading: false);
});

/// مزود التنبيهات
class ReportAlertsNotifier extends StateNotifier<List<ReportAlert>> {
  ReportAlertsNotifier() : super([]) {
    loadAlerts();
  }

  /// تحميل التنبيهات
  Future<void> loadAlerts() async {
    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/ReportAlerts');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        final List<ReportAlert> alerts = [];

        for (var child in snapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));
          alerts.add(ReportAlert.fromJson(data, child.key!));
        }

        // ترتيب التنبيهات حسب التاريخ (الأحدث أولاً)
        alerts.sort((a, b) => b.date.compareTo(a.date));

        state = alerts;
      }
    } catch (e) {
      // خطأ في تحميل التنبيهات
    }
  }

  /// إضافة تنبيه جديد
  Future<void> addAlert(ReportAlert alert) async {
    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/ReportAlerts');
      final newRef = ref.push();

      await newRef.set(alert.toJson());

      // تحديث القائمة
      state = [alert.copyWith(id: newRef.key!), ...state];
    } catch (e) {
      // خطأ في إضافة تنبيه
    }
  }

  /// تحديث حالة قراءة التنبيه
  Future<void> markAsRead(String alertId) async {
    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/ReportAlerts/$alertId');
      await ref.update({'isRead': true});

      // تحديث القائمة
      state = state.map((alert) {
        if (alert.id == alertId) {
          return alert.copyWith(isRead: true);
        }
        return alert;
      }).toList();
    } catch (e) {
      // خطأ في تحديث حالة قراءة التنبيه
    }
  }

  /// تحديث حالة قراءة جميع التنبيهات
  Future<void> markAllAsRead() async {
    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/ReportAlerts');

      for (var alert in state) {
        if (!alert.isRead) {
          await ref.child(alert.id).update({'isRead': true});
        }
      }

      // تحديث القائمة
      state = state.map((alert) => alert.copyWith(isRead: true)).toList();
    } catch (e) {
      // خطأ في تحديث حالة قراءة جميع التنبيهات
    }
  }

  /// حذف تنبيه
  Future<void> deleteAlert(String alertId) async {
    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/ReportAlerts/$alertId');
      await ref.remove();

      // تحديث القائمة
      state = state.where((alert) => alert.id != alertId).toList();
    } catch (e) {
      // خطأ في حذف التنبيه
    }
  }

  /// حذف جميع التنبيهات
  Future<void> deleteAllAlerts() async {
    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/ReportAlerts');
      await ref.remove();

      // تحديث القائمة
      state = [];
    } catch (e) {
      // خطأ في حذف جميع التنبيهات
    }
  }

  /// إنشاء تنبيه لعدم اتساق البيانات
  Future<void> createInconsistencyAlert(
      String reportType, int inconsistentItems, String details) async {
    final alert = ReportAlert(
      id: '',
      title: 'عدم اتساق في بيانات $reportType',
      message:
          'تم اكتشاف $inconsistentItems عنصر غير متسق في تقرير $reportType',
      details: details,
      type: AlertType.inconsistency,
      reportType: reportType,
      date: DateTime.now(),
      isRead: false,
    );

    await addAlert(alert);
  }
}

/// نموذج تنبيه التقارير
class ReportAlert {
  final String id;
  final String title;
  final String message;
  final String details;
  final AlertType type;
  final String reportType;
  final DateTime date;
  final bool isRead;

  ReportAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.details,
    required this.type,
    required this.reportType,
    required this.date,
    required this.isRead,
  });

  factory ReportAlert.fromJson(Map<String, dynamic> json, String id) {
    return ReportAlert(
      id: id,
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      details: json['details'] ?? '',
      type: _parseAlertType(json['type']),
      reportType: json['reportType'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      isRead: json['isRead'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'message': message,
      'details': details,
      'type': type.toString().split('.').last,
      'reportType': reportType,
      'date': date.toString(),
      'isRead': isRead,
    };
  }

  ReportAlert copyWith({
    String? id,
    String? title,
    String? message,
    String? details,
    AlertType? type,
    String? reportType,
    DateTime? date,
    bool? isRead,
  }) {
    return ReportAlert(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      details: details ?? this.details,
      type: type ?? this.type,
      reportType: reportType ?? this.reportType,
      date: date ?? this.date,
      isRead: isRead ?? this.isRead,
    );
  }

  static AlertType _parseAlertType(String? type) {
    switch (type) {
      case 'inconsistency':
        return AlertType.inconsistency;
      case 'warning':
        return AlertType.warning;
      case 'info':
        return AlertType.info;
      default:
        return AlertType.info;
    }
  }
}

/// أنواع التنبيهات
enum AlertType {
  inconsistency,
  warning,
  info,
}

/// حالة التنبيهات
class AlertsState {
  final bool isLoading;

  AlertsState({
    required this.isLoading,
  });

  AlertsState copyWith({
    bool? isLoading,
  }) {
    return AlertsState(
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
