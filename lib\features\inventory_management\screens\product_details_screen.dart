// بسم الله الرحمن الرحيم
// شاشة تفاصيل المنتج - تعرض تفاصيل المنتج وتتيح إدارته

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../models/stock_movement_model.dart';
import '../models/product_transaction_model.dart';
import '../services/inventory_service.dart';
import '../services/product_transaction_service.dart';
import '../widgets/stock_level_indicator.dart';
import 'add_product_screen.dart';
import 'stock_movement_screen.dart';
import 'product_transactions_screen.dart';
import 'product_report_screen.dart';

/// شاشة تفاصيل المنتج
class ProductDetailsScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة تفاصيل المنتج
  const ProductDetailsScreen({
    super.key,
    required this.product,
  });

  /// المنتج
  final ProductModel product;

  @override
  ConsumerState<ProductDetailsScreen> createState() =>
      _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends ConsumerState<ProductDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  ProductModel? _product;
  CategoryModel? _category;
  List<StockMovementModel> _stockMovements = [];
  ProductStatistics? _productStatistics;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _product = widget.product;
    _loadProductDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // تحميل تفاصيل المنتج
  Future<void> _loadProductDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المنتج المحدث
      final updatedProduct =
          await ref.read(inventoryServiceProvider).getProduct(_product!.id);
      if (updatedProduct != null) {
        _product = updatedProduct;
      }

      // تحميل الفئة
      final category = await ref
          .read(inventoryServiceProvider)
          .getCategory(_product!.categoryId);
      if (category != null) {
        _category = category;
      }

      // تحميل حركات المخزون
      final stockMovements = await ref
          .read(inventoryServiceProvider)
          .getProductStockMovements(_product!.id);
      _stockMovements = stockMovements;

      // تحميل إحصائيات المنتج
      final productStatistics = await ref
          .read(productTransactionServiceProvider)
          .getProductStatistics(_product!.barcode);
      _productStatistics = productStatistics;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تعديل المنتج
  void _editProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddProductScreen(product: _product),
      ),
    ).then((_) => _loadProductDetails());
  }

  // حذف المنتج
  Future<void> _deleteProduct() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: Text('هل أنت متأكد من حذف المنتج "${_product!.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success = await ref
            .read(inventoryServiceProvider)
            .deleteProduct(_product!.id);
        if (success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حذف المنتج بنجاح')),
            );
            Navigator.pop(context);
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('فشل في حذف المنتج')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // تعديل كمية المنتج
  Future<void> _adjustQuantity() async {
    final TextEditingController quantityController = TextEditingController();
    final TextEditingController notesController = TextEditingController();
    StockMovementType selectedType = StockMovementType.addition;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('تعديل كمية المنتج'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // نوع الحركة
                DropdownButtonFormField<StockMovementType>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الحركة',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedType,
                  items: const [
                    DropdownMenuItem(
                      value: StockMovementType.addition,
                      child: Text('إضافة مخزون'),
                    ),
                    DropdownMenuItem(
                      value: StockMovementType.withdrawal,
                      child: Text('سحب مخزون'),
                    ),
                    DropdownMenuItem(
                      value: StockMovementType.adjustment,
                      child: Text('تعديل مخزون'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // الكمية
                TextField(
                  controller: quantityController,
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),

                // ملاحظات
                TextField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  final quantity = int.tryParse(quantityController.text);
                  if (quantity == null || quantity <= 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يرجى إدخال كمية صحيحة')),
                    );
                    return;
                  }

                  Navigator.pop(context, {
                    'type': selectedType,
                    'quantity': quantity,
                    'notes': notesController.text,
                  });
                },
                child: const Text('حفظ'),
              ),
            ],
          );
        },
      ),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final type = result['type'] as StockMovementType;
        final quantity = result['quantity'] as int;
        final notes = result['notes'] as String;

        int newQuantity;
        if (type == StockMovementType.addition) {
          newQuantity = _product!.quantity + quantity;
        } else if (type == StockMovementType.withdrawal) {
          newQuantity = _product!.quantity - quantity;
          if (newQuantity < 0) {
            newQuantity = 0;
          }
        } else {
          newQuantity = quantity;
        }

        final success =
            await ref.read(inventoryServiceProvider).adjustProductQuantity(
                  productId: _product!.id,
                  newQuantity: newQuantity,
                  type: type,
                  notes: notes,
                );

        if (success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم تعديل كمية المنتج بنجاح')),
            );
          }
          _loadProductDetails();
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('فشل في تعديل كمية المنتج')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // عرض حركات المخزون
  void _viewStockMovements() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StockMovementScreen(productId: _product!.id),
      ),
    ).then((_) => _loadProductDetails());
  }

  @override
  Widget build(BuildContext context) {
    if (_product == null) {
      return const Scaffold(
        body: Center(
          child: Text('المنتج غير موجود'),
        ),
      );
    }

    final currencyFormat = NumberFormat.currency(
      symbol: 'جنيه',
      decimalDigits: 2,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(_product!.name),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editProduct,
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _openProductReport,
            tooltip: 'التقرير الشامل',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteProduct,
            tooltip: 'حذف',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'التفاصيل'),
            Tab(text: 'المخزون'),
            Tab(text: 'الحركات'),
            Tab(text: 'التقارير'),
          ],
        ),
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل البيانات...',
            )
          : TabBarView(
              controller: _tabController,
              children: [
                // علامة التبويب الأولى: التفاصيل
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // صورة المنتج
                      Center(
                        child: Container(
                          width: 200,
                          height: 200,
                          decoration: BoxDecoration(
                            color: AppColors.lightGreyColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _product!.imagePath != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    _product!.imagePath!,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : const Icon(
                                  Icons.inventory,
                                  size: 80,
                                  color: AppColors.greyTextColor,
                                ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // معلومات المنتج
                      _buildInfoCard(
                        title: 'معلومات المنتج',
                        children: [
                          _buildInfoRow('الاسم', _product!.name),
                          _buildInfoRow('الباركود', _product!.barcode),
                          _buildInfoRow('الفئة', _category?.name ?? 'غير محدد'),
                          _buildInfoRow(
                              'الوصف', _product!.description ?? 'لا يوجد وصف'),
                          _buildInfoRow('وحدة القياس', _product!.unit),
                          _buildInfoRow(
                              'الحالة', _getStatusText(_product!.status)),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // معلومات السعر
                      _buildInfoCard(
                        title: 'معلومات السعر',
                        children: [
                          _buildInfoRow('سعر البيع',
                              currencyFormat.format(_product!.price)),
                          _buildInfoRow('تكلفة الشراء',
                              currencyFormat.format(_product!.cost)),
                          _buildInfoRow('نسبة الخصم', '${_product!.discount}%'),
                          _buildInfoRow('نسبة الضريبة', '${_product!.tax}%'),
                          _buildInfoRow('السعر بعد الخصم',
                              currencyFormat.format(_product!.sellingPrice)),
                          _buildInfoRow('السعر النهائي',
                              currencyFormat.format(_product!.finalPrice)),
                          _buildInfoRow('هامش الربح',
                              '${_product!.profitMargin.toStringAsFixed(2)}%'),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // معلومات إضافية
                      _buildInfoCard(
                        title: 'معلومات إضافية',
                        children: [
                          _buildInfoRow(
                              'تاريخ الإنشاء',
                              DateFormat('yyyy-MM-dd HH:mm')
                                  .format(_product!.createdAt)),
                          if (_product!.updatedAt != null)
                            _buildInfoRow(
                                'تاريخ التحديث',
                                DateFormat('yyyy-MM-dd HH:mm')
                                    .format(_product!.updatedAt!)),
                          _buildInfoRow(
                              'المورد', _product!.supplierId ?? 'غير محدد'),
                          _buildInfoRow(
                              'الموقع', _product!.locationId ?? 'غير محدد'),
                        ],
                      ),
                    ],
                  ),
                ),

                // علامة التبويب الثانية: المخزون
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // بطاقة المخزون
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'حالة المخزون',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildInfoRow('الكمية الحالية',
                                  '${_product!.quantity} ${_product!.unit}'),
                              _buildInfoRow('الحد الأدنى للكمية',
                                  '${_product!.minQuantity} ${_product!.unit}'),
                              if (_product!.maxQuantity != null)
                                _buildInfoRow('الحد الأقصى للكمية',
                                    '${_product!.maxQuantity} ${_product!.unit}'),
                              _buildInfoRow('قيمة المخزون',
                                  currencyFormat.format(_product!.stockValue)),
                              const SizedBox(height: 16),
                              StockLevelIndicator.fromProduct(_product!),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // أزرار إدارة المخزون
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _adjustQuantity,
                              icon: const Icon(Icons.edit),
                              label: const Text('تعديل الكمية'),
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _viewStockMovements,
                              icon: const Icon(Icons.history),
                              label: const Text('سجل الحركات'),
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // آخر حركات المخزون
                      const Text(
                        'آخر حركات المخزون',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _stockMovements.isEmpty
                          ? const Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 16.0),
                                child: Text('لا توجد حركات مخزون'),
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: _stockMovements.length > 5
                                  ? 5
                                  : _stockMovements.length,
                              itemBuilder: (context, index) {
                                final movement = _stockMovements[index];
                                return Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  child: ListTile(
                                    leading:
                                        _getStockMovementIcon(movement.type),
                                    title: Text(_getStockMovementTypeText(
                                        movement.type)),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            'الكمية: ${movement.quantity} ${_product!.unit}'),
                                        if (movement.notes != null &&
                                            movement.notes!.isNotEmpty)
                                          Text('ملاحظات: ${movement.notes}'),
                                      ],
                                    ),
                                    trailing: Text(
                                      DateFormat('yyyy-MM-dd')
                                          .format(movement.createdAt),
                                      style: const TextStyle(
                                        color: AppColors.greyTextColor,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                    ],
                  ),
                ),

                // علامة التبويب الثالثة: الحركات
                _stockMovements.isEmpty
                    ? const Center(
                        child: Text('لا توجد حركات مخزون'),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16.0),
                        itemCount: _stockMovements.length,
                        itemBuilder: (context, index) {
                          final movement = _stockMovements[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      _getStockMovementIcon(movement.type),
                                      const SizedBox(width: 8),
                                      Text(
                                        _getStockMovementTypeText(
                                            movement.type),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const Spacer(),
                                      Text(
                                        DateFormat('yyyy-MM-dd HH:mm')
                                            .format(movement.createdAt),
                                        style: const TextStyle(
                                          color: AppColors.greyTextColor,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Divider(),
                                  _buildInfoRow('الكمية',
                                      '${movement.quantity} ${_product!.unit}'),
                                  if (movement.previousQuantity != null)
                                    _buildInfoRow('الكمية السابقة',
                                        '${movement.previousQuantity} ${_product!.unit}'),
                                  if (movement.newQuantity != null)
                                    _buildInfoRow('الكمية الجديدة',
                                        '${movement.newQuantity} ${_product!.unit}'),
                                  if (movement.notes != null &&
                                      movement.notes!.isNotEmpty)
                                    _buildInfoRow('ملاحظات', movement.notes!),
                                  if (movement.referenceId != null)
                                    _buildInfoRow(
                                        'رقم المرجع', movement.referenceId!),
                                  if (movement.referenceType != null)
                                    _buildInfoRow(
                                        'نوع المرجع', movement.referenceType!),
                                  if (movement.fromLocationId != null)
                                    _buildInfoRow(
                                        'من الموقع', movement.fromLocationId!),
                                  if (movement.toLocationId != null)
                                    _buildInfoRow(
                                        'إلى الموقع', movement.toLocationId!),
                                  if (movement.createdBy != null)
                                    _buildInfoRow(
                                        'بواسطة', movement.createdBy!),
                                ],
                              ),
                            ),
                          );
                        },
                      ),

                // علامة التبويب الرابعة: التقارير
                _buildReportsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _adjustQuantity,
        tooltip: 'تعديل الكمية',
        child: const Icon(Icons.edit),
      ),
    );
  }

  // بناء بطاقة معلومات
  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.greyTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على نص حالة المنتج
  String _getStatusText(ProductStatus status) {
    switch (status) {
      case ProductStatus.available:
        return 'متوفر';
      case ProductStatus.lowStock:
        return 'منخفض المخزون';
      case ProductStatus.outOfStock:
        return 'نفذ من المخزون';
      case ProductStatus.inactive:
        return 'غير نشط';
    }
  }

  // الحصول على أيقونة حركة المخزون
  Widget _getStockMovementIcon(StockMovementType type) {
    IconData icon;
    Color color;

    switch (type) {
      case StockMovementType.addition:
        icon = Icons.add_circle;
        color = Colors.green;
        break;
      case StockMovementType.withdrawal:
        icon = Icons.remove_circle;
        color = Colors.red;
        break;
      case StockMovementType.adjustment:
        icon = Icons.edit;
        color = Colors.blue;
        break;
      case StockMovementType.transfer:
        icon = Icons.swap_horiz;
        color = Colors.purple;
        break;
      case StockMovementType.sale:
        icon = Icons.shopping_cart;
        color = Colors.orange;
        break;
      case StockMovementType.return_:
        icon = Icons.assignment_return;
        color = Colors.teal;
        break;
      case StockMovementType.damaged:
        icon = Icons.dangerous;
        color = Colors.red;
        break;
      case StockMovementType.inventory:
        icon = Icons.inventory;
        color = Colors.blue;
        break;
    }

    return Icon(
      icon,
      color: color,
      size: 24,
    );
  }

  // الحصول على نص نوع حركة المخزون
  String _getStockMovementTypeText(StockMovementType type) {
    switch (type) {
      case StockMovementType.addition:
        return 'إضافة مخزون';
      case StockMovementType.withdrawal:
        return 'سحب مخزون';
      case StockMovementType.adjustment:
        return 'تعديل مخزون';
      case StockMovementType.transfer:
        return 'نقل مخزون';
      case StockMovementType.sale:
        return 'بيع';
      case StockMovementType.return_:
        return 'مرتجع';
      case StockMovementType.damaged:
        return 'تالف';
      case StockMovementType.inventory:
        return 'جرد';
    }
  }

  // بناء تبويب التقارير
  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildQuickStats(),
          const SizedBox(height: 24),

          // تقارير المبيعات
          _buildSalesReports(),
          const SizedBox(height: 24),

          // تقارير المشتريات
          _buildPurchaseReports(),
          const SizedBox(height: 24),

          // تحليل الأداء
          _buildPerformanceAnalysis(),
          const SizedBox(height: 24),

          // زر التقرير الشامل
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _openProductReport,
              icon: const Icon(Icons.analytics),
              label: const Text('عرض التقرير الشامل'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    final currencyFormat = NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المبيعات',
                    currencyFormat.format(_productStatistics?.totalSalesAmount ?? 0),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المشتريات',
                    currencyFormat.format(_productStatistics?.totalPurchaseAmount ?? 0),
                    Icons.shopping_cart,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'صافي الربح',
                    currencyFormat.format(_productStatistics?.netProfit ?? 0),
                    Icons.account_balance_wallet,
                    _productStatistics?.netProfit != null && _productStatistics!.netProfit >= 0
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'هامش الربح',
                    '${(_productStatistics?.profitMargin ?? 0).toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.greyTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء تقارير المبيعات
  Widget _buildSalesReports() {
    final currencyFormat = NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2);

    return _buildInfoCard(
      title: 'تقارير المبيعات',
      children: [
        ListTile(
          leading: const Icon(Icons.point_of_sale, color: Colors.green),
          title: const Text('إجمالي الكمية المباعة'),
          trailing: Text('${_productStatistics?.totalSalesQuantity ?? 0} ${_product?.unit ?? ''}'),
          onTap: () {
            _showSalesTransactions();
          },
        ),
        ListTile(
          leading: const Icon(Icons.monetization_on, color: Colors.green),
          title: const Text('إجمالي قيمة المبيعات'),
          trailing: Text(currencyFormat.format(_productStatistics?.totalSalesAmount ?? 0)),
          onTap: () {
            _showSalesTransactions();
          },
        ),
        ListTile(
          leading: const Icon(Icons.receipt, color: Colors.green),
          title: const Text('عدد الفواتير'),
          trailing: Text('${_productStatistics?.salesInvoiceCount ?? 0}'),
          onTap: () {
            _showSalesTransactions();
          },
        ),
        ListTile(
          leading: const Icon(Icons.trending_up, color: Colors.green),
          title: const Text('متوسط سعر البيع'),
          trailing: Text(currencyFormat.format(_productStatistics?.averageSalePrice ?? 0)),
          onTap: () {
            _showSalesTransactions();
          },
        ),
      ],
    );
  }

  // بناء تقارير المشتريات
  Widget _buildPurchaseReports() {
    final currencyFormat = NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2);

    return _buildInfoCard(
      title: 'تقارير المشتريات',
      children: [
        ListTile(
          leading: const Icon(Icons.shopping_bag, color: Colors.blue),
          title: const Text('إجمالي الكمية المشتراة'),
          trailing: Text('${_productStatistics?.totalPurchaseQuantity ?? 0} ${_product?.unit ?? ''}'),
          onTap: () {
            _showPurchaseTransactions();
          },
        ),
        ListTile(
          leading: const Icon(Icons.attach_money, color: Colors.blue),
          title: const Text('إجمالي قيمة المشتريات'),
          trailing: Text(currencyFormat.format(_productStatistics?.totalPurchaseAmount ?? 0)),
          onTap: () {
            _showPurchaseTransactions();
          },
        ),
        ListTile(
          leading: const Icon(Icons.receipt_long, color: Colors.blue),
          title: const Text('عدد فواتير الشراء'),
          trailing: Text('${_productStatistics?.purchaseInvoiceCount ?? 0}'),
          onTap: () {
            _showPurchaseTransactions();
          },
        ),
        ListTile(
          leading: const Icon(Icons.trending_down, color: Colors.blue),
          title: const Text('متوسط سعر الشراء'),
          trailing: Text(currencyFormat.format(_productStatistics?.averagePurchasePrice ?? 0)),
          onTap: () {
            _showPurchaseTransactions();
          },
        ),
      ],
    );
  }

  // بناء تحليل الأداء
  Widget _buildPerformanceAnalysis() {
    return _buildInfoCard(
      title: 'تحليل الأداء',
      children: [
        ListTile(
          leading: const Icon(Icons.analytics, color: Colors.orange),
          title: const Text('هامش الربح'),
          trailing: Text('${_product!.profitMargin.toStringAsFixed(2)}%'),
          onTap: () {
            // فتح تحليل هامش الربح
          },
        ),
        ListTile(
          leading: const Icon(Icons.speed, color: Colors.orange),
          title: const Text('معدل دوران المخزون'),
          trailing: const Text('0 مرة/شهر'),
          onTap: () {
            // فتح تحليل معدل الدوران
          },
        ),
        ListTile(
          leading: const Icon(Icons.timeline, color: Colors.orange),
          title: const Text('اتجاه المبيعات'),
          trailing: const Text('مستقر'),
          onTap: () {
            // فتح تحليل اتجاه المبيعات
          },
        ),
        ListTile(
          leading: const Icon(Icons.warning, color: Colors.red),
          title: const Text('تنبيهات المخزون'),
          trailing: Text(_product!.quantity <= _product!.minQuantity ? 'منخفض' : 'طبيعي'),
          onTap: () {
            // فتح تنبيهات المخزون
          },
        ),
      ],
    );
  }

  // عرض معاملات البيع
  void _showSalesTransactions() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductTransactionsScreen(
          productCode: _product!.barcode,
          productName: _product!.name,
          transactionType: TransactionType.sale,
        ),
      ),
    );
  }

  // عرض معاملات الشراء
  void _showPurchaseTransactions() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductTransactionsScreen(
          productCode: _product!.barcode,
          productName: _product!.name,
          transactionType: TransactionType.purchase,
        ),
      ),
    );
  }

  // فتح التقرير الشامل للمنتج
  void _openProductReport() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductReportScreen(
          product: _product!,
        ),
      ),
    );
  }
}
