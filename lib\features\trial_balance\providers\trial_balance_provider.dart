// بسم الله الرحمن الرحيم
// مزود بيانات تقرير Trial Balance

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/trial_balance_model.dart';
import '../services/trial_balance_service.dart';

/// مزود خدمة Trial Balance
final trialBalanceServiceProvider = Provider<TrialBalanceService>((ref) {
  return TrialBalanceService();
});

/// مزود حالة Trial Balance
final trialBalanceProvider =
    StateNotifierProvider<TrialBalanceNotifier, TrialBalanceState>((ref) {
  final service = ref.watch(trialBalanceServiceProvider);
  return TrialBalanceNotifier(service);
});

/// حالة تقرير Trial Balance
class TrialBalanceState {
  final bool isLoading;
  final TrialBalanceReport? currentReport;
  final List<TrialBalanceReport> savedReports;
  final String? error;
  final DateTime fromDate;
  final DateTime toDate;

  TrialBalanceState({
    this.isLoading = false,
    this.currentReport,
    this.savedReports = const [],
    this.error,
    DateTime? fromDate,
    DateTime? toDate,
  })  : fromDate = fromDate ?? _getFirstDayOfMonth(),
        toDate = toDate ?? _getLastDayOfMonth();

  static DateTime _getFirstDayOfMonth() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }

  static DateTime _getLastDayOfMonth() {
    final now = DateTime.now();
    return DateTime(now.year, now.month + 1, 0);
  }

  TrialBalanceState copyWith({
    bool? isLoading,
    TrialBalanceReport? currentReport,
    List<TrialBalanceReport>? savedReports,
    String? error,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    return TrialBalanceState(
      isLoading: isLoading ?? this.isLoading,
      currentReport: currentReport ?? this.currentReport,
      savedReports: savedReports ?? this.savedReports,
      error: error,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
    );
  }

  /// التحقق من وجود تقرير حالي
  bool get hasCurrentReport => currentReport != null;

  /// التحقق من توازن التقرير الحالي
  bool get isCurrentReportBalanced => currentReport?.isBalanced ?? false;

  /// الحصول على الفرق في التقرير الحالي
  double get currentReportDifference => currentReport?.difference ?? 0;

  /// الحصول على إجمالي المدين
  double get totalDebits => currentReport?.totalDebits ?? 0;

  /// الحصول على إجمالي الدائن
  double get totalCredits => currentReport?.totalCredits ?? 0;
}

/// مدير حالة Trial Balance
class TrialBalanceNotifier extends StateNotifier<TrialBalanceState> {
  final TrialBalanceService _service;

  TrialBalanceNotifier(this._service) : super(TrialBalanceState()) {
    _loadSavedReports();
  }

  /// تحديث تاريخ البداية
  void updateFromDate(DateTime date) {
    state = state.copyWith(fromDate: date);
  }

  /// تحديث تاريخ النهاية
  void updateToDate(DateTime date) {
    state = state.copyWith(toDate: date);
  }

  /// تحديث نطاق التواريخ
  void updateDateRange(DateTime fromDate, DateTime toDate) {
    state = state.copyWith(fromDate: fromDate, toDate: toDate);
  }

  /// إنشاء تقرير جديد
  Future<void> generateReport({String? title}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final report = await _service.generateTrialBalance(
        fromDate: state.fromDate,
        toDate: state.toDate,
        reportTitle: title,
      );

      state = state.copyWith(
        isLoading: false,
        currentReport: report,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في إنشاء التقرير: $e',
      );
    }
  }

  /// حفظ التقرير الحالي
  Future<bool> saveCurrentReport() async {
    if (state.currentReport == null) return false;

    try {
      final success = await _service.saveReport(state.currentReport!);
      if (success) {
        await _loadSavedReports();
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: 'خطأ في حفظ التقرير: $e');
      return false;
    }
  }

  /// تحميل التقارير المحفوظة
  Future<void> _loadSavedReports() async {
    try {
      final reports = await _service.getSavedReports();
      state = state.copyWith(savedReports: reports);
    } catch (e) {
      state = state.copyWith(error: 'خطأ في تحميل التقارير: $e');
    }
  }

  /// إعادة تحميل التقارير المحفوظة
  Future<void> refreshSavedReports() async {
    await _loadSavedReports();
  }

  /// حذف تقرير محفوظ
  Future<bool> deleteReport(String reportId) async {
    try {
      final success = await _service.deleteReport(reportId);
      if (success) {
        await _loadSavedReports();
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: 'خطأ في حذف التقرير: $e');
      return false;
    }
  }

  /// تحميل تقرير محفوظ كتقرير حالي
  void loadSavedReport(TrialBalanceReport report) {
    state = state.copyWith(
      currentReport: report,
      fromDate: report.fromDate,
      toDate: report.toDate,
    );
  }

  /// مسح التقرير الحالي
  void clearCurrentReport() {
    state = state.copyWith(currentReport: null);
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// تعيين فترة زمنية محددة مسبقاً
  void setPresetPeriod(PresetPeriod period) {
    final now = DateTime.now();
    DateTime fromDate;
    DateTime toDate;

    switch (period) {
      case PresetPeriod.today:
        fromDate = DateTime(now.year, now.month, now.day);
        toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case PresetPeriod.thisWeek:
        final weekday = now.weekday;
        fromDate = now.subtract(Duration(days: weekday - 1));
        fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case PresetPeriod.thisMonth:
        fromDate = DateTime(now.year, now.month, 1);
        toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case PresetPeriod.thisQuarter:
        final quarter = ((now.month - 1) ~/ 3) + 1;
        final quarterStartMonth = (quarter - 1) * 3 + 1;
        fromDate = DateTime(now.year, quarterStartMonth, 1);
        toDate = DateTime(now.year, quarterStartMonth + 3, 0, 23, 59, 59);
        break;
      case PresetPeriod.thisYear:
        fromDate = DateTime(now.year, 1, 1);
        toDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case PresetPeriod.lastMonth:
        fromDate = DateTime(now.year, now.month - 1, 1);
        toDate = DateTime(now.year, now.month, 0, 23, 59, 59);
        break;
      case PresetPeriod.lastQuarter:
        final quarter = ((now.month - 1) ~/ 3) + 1;
        final lastQuarter = quarter == 1 ? 4 : quarter - 1;
        final year = quarter == 1 ? now.year - 1 : now.year;
        final quarterStartMonth = (lastQuarter - 1) * 3 + 1;
        fromDate = DateTime(year, quarterStartMonth, 1);
        toDate = DateTime(year, quarterStartMonth + 3, 0, 23, 59, 59);
        break;
      case PresetPeriod.lastYear:
        fromDate = DateTime(now.year - 1, 1, 1);
        toDate = DateTime(now.year - 1, 12, 31, 23, 59, 59);
        break;
    }

    state = state.copyWith(fromDate: fromDate, toDate: toDate);
  }
}

/// الفترات الزمنية المحددة مسبقاً
enum PresetPeriod {
  today('اليوم'),
  thisWeek('هذا الأسبوع'),
  thisMonth('هذا الشهر'),
  thisQuarter('هذا الربع'),
  thisYear('هذا العام'),
  lastMonth('الشهر الماضي'),
  lastQuarter('الربع الماضي'),
  lastYear('العام الماضي');

  const PresetPeriod(this.arabicName);
  final String arabicName;
}

/// مزود للحصول على التقارير المحفوظة فقط
final savedTrialBalanceReportsProvider =
    FutureProvider<List<TrialBalanceReport>>((ref) async {
  final service = ref.watch(trialBalanceServiceProvider);
  return await service.getSavedReports();
});

/// مزود لإنشاء تقرير جديد
final generateTrialBalanceProvider =
    FutureProvider.family<TrialBalanceReport, TrialBalanceParams>(
        (ref, params) async {
  final service = ref.watch(trialBalanceServiceProvider);
  return await service.generateTrialBalance(
    fromDate: params.fromDate,
    toDate: params.toDate,
    reportTitle: params.title,
  );
});

/// معاملات إنشاء تقرير Trial Balance
class TrialBalanceParams {
  final DateTime fromDate;
  final DateTime toDate;
  final String? title;

  const TrialBalanceParams({
    required this.fromDate,
    required this.toDate,
    this.title,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrialBalanceParams &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.title == title;
  }

  @override
  int get hashCode => fromDate.hashCode ^ toDate.hashCode ^ title.hashCode;
}
