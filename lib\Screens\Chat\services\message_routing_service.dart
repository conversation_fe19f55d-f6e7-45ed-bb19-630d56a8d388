import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'gemini_service.dart';
import 'chat_notification_service.dart';

/// خدمة توجيه الرسائل بين المستخدمين والذكاء الاصطناعي
class MessageRoutingService {
  static const String _aiUserId = 'ai_assistant';
  static const String _messagesPath = 'Chat Messages';

  /// إرسال رسالة للمستخدم
  static Future<bool> sendMessageToUser({
    required String recipientUserId,
    required String message,
    String type = 'text',
    String? filePath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        debugPrint('لا يوجد مستخدم مسجل دخول');
        return false;
      }

      final messageData = {
        'senderId': currentUser.uid,
        'recipientId': recipientUserId,
        'message': message,
        'type': type,
        'timestamp': ServerValue.timestamp,
        'isRead': false,
        'messageId': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      if (filePath != null) {
        messageData['filePath'] = filePath;
      }

      if (metadata != null) {
        metadata.forEach((key, value) {
          messageData[key] = value;
        });
      }

      // حفظ الرسالة في قاعدة البيانات
      final chatId = _generateChatId(currentUser.uid, recipientUserId);
      final messageRef = FirebaseDatabase.instance
          .ref(_messagesPath)
          .child(chatId)
          .child('messages')
          .push();

      await messageRef.set(messageData);

      // تحديث آخر رسالة في معلومات المحادثة
      await FirebaseDatabase.instance
          .ref(_messagesPath)
          .child(chatId)
          .child('lastMessage')
          .set({
        'message': message,
        'timestamp': ServerValue.timestamp,
        'senderId': currentUser.uid,
        'type': type,
      });

      // إرسال إشعار للمستخدم المستقبل
      try {
        if (type == 'voice') {
          await ChatNotificationService.sendNewVoiceMessageNotification(
            senderId: currentUser.uid,
            senderName: currentUser.email?.split('@')[0] ?? 'مستخدم',
            duration: metadata?['duration'] ?? 0,
            chatId: chatId,
          );
        } else if (type == 'image') {
          await ChatNotificationService.sendNewImageNotification(
            senderId: currentUser.uid,
            senderName: currentUser.email?.split('@')[0] ?? 'مستخدم',
            chatId: chatId,
          );
        } else {
          await ChatNotificationService.sendNewMessageNotification(
            senderId: currentUser.uid,
            senderName: currentUser.email?.split('@')[0] ?? 'مستخدم',
            message: message,
            chatId: chatId,
          );
        }
      } catch (e) {
        debugPrint('خطأ في إرسال إشعار الرسالة: $e');
      }

      debugPrint('تم إرسال الرسالة للمستخدم: $recipientUserId');
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة للمستخدم: $e');
      return false;
    }
  }

  /// إرسال رسالة للذكاء الاصطناعي
  static Future<String?> sendMessageToAI({
    required String message,
    String type = 'text',
    String? filePath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        debugPrint('لا يوجد مستخدم مسجل دخول');
        return null;
      }

      debugPrint('إرسال رسالة للذكاء الاصطناعي: $message');

      // حفظ رسالة المستخدم
      await _saveAIMessage(
        senderId: currentUser.uid,
        message: message,
        type: type,
        filePath: filePath,
        isFromUser: true,
      );

      // إرسال الرسالة للذكاء الاصطناعي والحصول على الرد
      String aiResponse;
      if (type == 'image' && filePath != null) {
        aiResponse = await GeminiService.sendMessage(
            'تم إرسال صورة: $message\nرابط الصورة: $filePath');
      } else {
        aiResponse = await GeminiService.sendMessage(message);
      }

      // حفظ رد الذكاء الاصطناعي
      await _saveAIMessage(
        senderId: _aiUserId,
        message: aiResponse,
        type: 'text',
        isFromUser: false,
      );

      debugPrint('تم الحصول على رد من الذكاء الاصطناعي');
      return aiResponse;
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة للذكاء الاصطناعي: $e');
      return 'عذراً، حدث خطأ في معالجة رسالتك. يرجى المحاولة مرة أخرى.';
    }
  }

  /// حفظ رسالة الذكاء الاصطناعي
  static Future<void> _saveAIMessage({
    required String senderId,
    required String message,
    required String type,
    String? filePath,
    required bool isFromUser,
  }) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final messageData = {
        'senderId': senderId,
        'recipientId': isFromUser ? _aiUserId : currentUser.uid,
        'message': message,
        'type': type,
        'timestamp': ServerValue.timestamp,
        'isRead': true, // رسائل الذكاء الاصطناعي تُعتبر مقروءة دائماً
        'messageId': DateTime.now().millisecondsSinceEpoch.toString(),
        'isFromUser': isFromUser,
      };

      if (filePath != null) {
        messageData['filePath'] = filePath;
      }

      // حفظ الرسالة في محادثة الذكاء الاصطناعي
      final chatId = _generateChatId(currentUser.uid, _aiUserId);
      final messageRef = FirebaseDatabase.instance
          .ref(_messagesPath)
          .child(chatId)
          .child('messages')
          .push();

      await messageRef.set(messageData);

      // تحديث آخر رسالة
      await FirebaseDatabase.instance
          .ref(_messagesPath)
          .child(chatId)
          .child('lastMessage')
          .set({
        'message': message,
        'timestamp': ServerValue.timestamp,
        'senderId': senderId,
        'type': type,
        'isFromUser': isFromUser,
      });
    } catch (e) {
      debugPrint('خطأ في حفظ رسالة الذكاء الاصطناعي: $e');
    }
  }

  /// جلب رسائل المحادثة
  static Stream<List<Map<String, dynamic>>> getChatMessages(
      String otherUserId) {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      return Stream.value([]);
    }

    final chatId = _generateChatId(currentUser.uid, otherUserId);
    return FirebaseDatabase.instance
        .ref(_messagesPath)
        .child(chatId)
        .child('messages')
        .orderByChild('timestamp')
        .onValue
        .map((event) {
      final List<Map<String, dynamic>> messages = [];
      if (event.snapshot.value != null) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        data.forEach((key, value) {
          final messageData = Map<String, dynamic>.from(value);
          messageData['key'] = key;
          messages.add(messageData);
        });
      }
      return messages;
    });
  }

  /// تحديد نوع المحادثة (مستخدم أم ذكاء اصطناعي)
  static bool isAIChat(String userId) {
    return userId == _aiUserId;
  }

  /// توليد معرف المحادثة
  static String _generateChatId(String userId1, String userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  /// تحديث حالة قراءة الرسائل
  static Future<void> markMessagesAsRead(String otherUserId) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final chatId = _generateChatId(currentUser.uid, otherUserId);
      final messagesRef = FirebaseDatabase.instance
          .ref(_messagesPath)
          .child(chatId)
          .child('messages');

      final snapshot = await messagesRef
          .orderByChild('recipientId')
          .equalTo(currentUser.uid)
          .once();

      if (snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);
        final updates = <String, dynamic>{};

        data.forEach((key, value) {
          final messageData = Map<String, dynamic>.from(value);
          if (messageData['isRead'] == false) {
            updates['$key/isRead'] = true;
          }
        });

        if (updates.isNotEmpty) {
          await messagesRef.update(updates);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة قراءة الرسائل: $e');
    }
  }

  /// حذف محادثة
  static Future<bool> deleteChat(String otherUserId) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return false;

      final chatId = _generateChatId(currentUser.uid, otherUserId);
      await FirebaseDatabase.instance.ref(_messagesPath).child(chatId).remove();

      debugPrint('تم حذف المحادثة مع: $otherUserId');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف المحادثة: $e');
      return false;
    }
  }
}
