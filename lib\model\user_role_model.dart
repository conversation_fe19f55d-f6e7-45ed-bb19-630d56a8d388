class UserRoleModel {
  late String email, userTitle, databaseId;
  String? profilePicture; // صورة البروفايل
  late bool isActive; // حالة تفعيل المستخدم
  late bool salePermission,
      partiesPermission,
      purchasePermission,
      productPermission,
      profileEditPermission,
      addExpensePermission,
      lossProfitPermission,
      dueListPermission,
      stockPermission,
      reportsPermission,
      salesListPermission,
      purchaseListPermission,
      // الصلاحيات الجديدة - الذكاء الاصطناعي
      aiChatPermission,
      aiAssistantPermission,
      voiceAssistantPermission,
      // الخزينة
      treasuryPermission,
      cashBoxPermission,
      // إدارة التوصيل
      deliveryManagementPermission,
      // الموارد البشرية
      hrmPermission,
      employeesPermission,
      designationPermission,
      salariesPermission,
      // التقارير المتقدمة
      financialReportsPermission,
      salesTargetsPermission,
      taxReportsPermission,
      // الإعدادات المتقدمة
      userLogsPermission,
      notificationsPermission,
      warrantyPermission,
      settingsPermission,
      userManagementPermission,
      // صلاحيات إضافية
      ledgerPermission,
      // فلاتر المياه - الصلاحية العامة
      waterFiltersPermission,
      // فلاتر المياه - صلاحيات مفصلة
      waterFilterProductsPermission,
      waterFilterCustomersPermission,
      waterFilterSystemsPermission,
      waterFilterMaintenancePermission,
      waterFilterInstallmentsPermission,
      waterFilterReportsPermission;

  String? userKey;

  UserRoleModel({
    required this.email,
    required this.userTitle,
    required this.databaseId,
    this.profilePicture,
    this.isActive = true, // افتراضيًا المستخدم مفعل
    required this.salePermission,
    required this.partiesPermission,
    required this.purchasePermission,
    required this.productPermission,
    required this.profileEditPermission,
    required this.addExpensePermission,
    required this.lossProfitPermission,
    required this.dueListPermission,
    required this.stockPermission,
    required this.reportsPermission,
    required this.salesListPermission,
    required this.purchaseListPermission,
    // الصلاحيات الجديدة - الذكاء الاصطناعي
    required this.aiChatPermission,
    required this.aiAssistantPermission,
    required this.voiceAssistantPermission,
    // الخزينة
    required this.treasuryPermission,
    required this.cashBoxPermission,
    // إدارة التوصيل
    required this.deliveryManagementPermission,
    // الموارد البشرية
    required this.hrmPermission,
    required this.employeesPermission,
    required this.designationPermission,
    required this.salariesPermission,
    // التقارير المتقدمة
    required this.financialReportsPermission,
    required this.salesTargetsPermission,
    required this.taxReportsPermission,
    // الإعدادات المتقدمة
    required this.userLogsPermission,
    required this.notificationsPermission,
    required this.warrantyPermission,
    required this.settingsPermission,
    required this.userManagementPermission,
    // صلاحيات إضافية
    required this.ledgerPermission,
    // فلاتر المياه - الصلاحية العامة
    required this.waterFiltersPermission,
    // فلاتر المياه - صلاحيات مفصلة
    required this.waterFilterProductsPermission,
    required this.waterFilterCustomersPermission,
    required this.waterFilterSystemsPermission,
    required this.waterFilterMaintenancePermission,
    required this.waterFilterInstallmentsPermission,
    required this.waterFilterReportsPermission,
    this.userKey,
  });

  UserRoleModel.fromJson(Map<dynamic, dynamic> json)
      : email = json['email'],
        userTitle = json['userTitle'],
        databaseId = json['databaseId'],
        profilePicture = json['profilePicture'],
        salePermission = json['salePermission'] ?? false,
        partiesPermission = json['partiesPermission'] ?? false,
        purchasePermission = json['purchasePermission'] ?? false,
        productPermission = json['productPermission'] ?? false,
        profileEditPermission = json['profileEditPermission'] ?? false,
        addExpensePermission = json['addExpensePermission'] ?? false,
        lossProfitPermission = json['lossProfitPermission'] ?? false,
        dueListPermission = json['dueListPermission'] ?? false,
        stockPermission = json['stockPermission'] ?? false,
        reportsPermission = json['reportsPermission'] ?? false,
        salesListPermission = json['salesListPermission'] ?? false,
        purchaseListPermission = json['purchaseListPermission'] ?? false,
        // الصلاحيات الجديدة - الذكاء الاصطناعي
        aiChatPermission = json['aiChatPermission'] ?? false,
        aiAssistantPermission = json['aiAssistantPermission'] ?? false,
        voiceAssistantPermission = json['voiceAssistantPermission'] ?? false,
        // الخزينة
        treasuryPermission = json['treasuryPermission'] ?? false,
        cashBoxPermission = json['cashBoxPermission'] ?? false,
        // إدارة التوصيل
        deliveryManagementPermission =
            json['deliveryManagementPermission'] ?? false,
        // الموارد البشرية
        hrmPermission = json['hrmPermission'] ?? false,
        employeesPermission = json['employeesPermission'] ?? false,
        designationPermission = json['designationPermission'] ?? false,
        salariesPermission = json['salariesPermission'] ?? false,
        // التقارير المتقدمة
        financialReportsPermission =
            json['financialReportsPermission'] ?? false,
        salesTargetsPermission = json['salesTargetsPermission'] ?? false,
        taxReportsPermission = json['taxReportsPermission'] ?? false,
        // الإعدادات المتقدمة
        userLogsPermission = json['userLogsPermission'] ?? false,
        notificationsPermission = json['notificationsPermission'] ?? false,
        warrantyPermission = json['warrantyPermission'] ?? false,
        settingsPermission = json['settingsPermission'] ?? false,
        userManagementPermission = json['userManagementPermission'] ?? false,
        // صلاحيات إضافية
        ledgerPermission = json['ledgerPermission'] ?? false,
        // فلاتر المياه - الصلاحية العامة
        waterFiltersPermission = json['waterFiltersPermission'] ?? false,
        // فلاتر المياه - صلاحيات مفصلة (إذا كان لديه صلاحية عامة، يحصل على كل الصلاحيات المفصلة)
        waterFilterProductsPermission = json['waterFilterProductsPermission'] ??
            (json['waterFiltersPermission'] ?? false),
        waterFilterCustomersPermission =
            json['waterFilterCustomersPermission'] ??
                (json['waterFiltersPermission'] ?? false),
        waterFilterSystemsPermission = json['waterFilterSystemsPermission'] ??
            (json['waterFiltersPermission'] ?? false),
        waterFilterMaintenancePermission =
            json['waterFilterMaintenancePermission'] ??
                (json['waterFiltersPermission'] ?? false),
        waterFilterInstallmentsPermission =
            json['waterFilterInstallmentsPermission'] ??
                (json['waterFiltersPermission'] ?? false),
        waterFilterReportsPermission = json['waterFilterReportsPermission'] ??
            (json['waterFiltersPermission'] ?? false),
        // حالة التفعيل
        isActive = json['isActive'] ?? true; // افتراضيًا مفعل للبيانات القديمة

  Map<dynamic, dynamic> toJson() => <String, dynamic>{
        'email': email,
        'userTitle': userTitle,
        'databaseId': databaseId,
        'profilePicture': profilePicture,
        'salePermission': salePermission,
        'partiesPermission': partiesPermission,
        'purchasePermission': purchasePermission,
        'productPermission': productPermission,
        'profileEditPermission': profileEditPermission,
        'addExpensePermission': addExpensePermission,
        'lossProfitPermission': lossProfitPermission,
        'dueListPermission': dueListPermission,
        'stockPermission': stockPermission,
        'reportsPermission': reportsPermission,
        'salesListPermission': salesListPermission,
        'purchaseListPermission': purchaseListPermission,
        // الصلاحيات الجديدة - الذكاء الاصطناعي
        'aiChatPermission': aiChatPermission,
        'aiAssistantPermission': aiAssistantPermission,
        'voiceAssistantPermission': voiceAssistantPermission,
        // الخزينة
        'treasuryPermission': treasuryPermission,
        'cashBoxPermission': cashBoxPermission,
        // إدارة التوصيل
        'deliveryManagementPermission': deliveryManagementPermission,
        // الموارد البشرية
        'hrmPermission': hrmPermission,
        'employeesPermission': employeesPermission,
        'designationPermission': designationPermission,
        'salariesPermission': salariesPermission,
        // التقارير المتقدمة
        'financialReportsPermission': financialReportsPermission,
        'salesTargetsPermission': salesTargetsPermission,
        'taxReportsPermission': taxReportsPermission,
        // الإعدادات المتقدمة
        'userLogsPermission': userLogsPermission,
        'notificationsPermission': notificationsPermission,
        'warrantyPermission': warrantyPermission,
        'settingsPermission': settingsPermission,
        'userManagementPermission': userManagementPermission,
        // صلاحيات إضافية
        'ledgerPermission': ledgerPermission,
        // فلاتر المياه - الصلاحية العامة
        'waterFiltersPermission': waterFiltersPermission,
        // فلاتر المياه - صلاحيات مفصلة
        'waterFilterProductsPermission': waterFilterProductsPermission,
        'waterFilterCustomersPermission': waterFilterCustomersPermission,
        'waterFilterSystemsPermission': waterFilterSystemsPermission,
        'waterFilterMaintenancePermission': waterFilterMaintenancePermission,
        'waterFilterInstallmentsPermission': waterFilterInstallmentsPermission,
        'waterFilterReportsPermission': waterFilterReportsPermission,
        // حالة التفعيل
        'isActive': isActive,
      };
}

/*class UserRoleModel {
  late String email, userTitle, databaseId;
  late bool salePermission,
      partiesPermission,
      purchasePermission,
      productPermission,
      profileEditPermission,
      addExpensePermission,
      lossProfitPermission,
      dueListPermission,
      stockPermission,
      reportsPermission,
      salesListPermission,
      purchaseListPermission;

  String? userKey;

  UserRoleModel({
    required this.email,
    required this.userTitle,
    required this.databaseId,
    required this.salePermission,
    required this.partiesPermission,
    required this.purchasePermission,
    required this.productPermission,
    required this.profileEditPermission,
    required this.addExpensePermission,
    required this.lossProfitPermission,
    required this.dueListPermission,
    required this.stockPermission,
    required this.reportsPermission,
    required this.salesListPermission,
    required this.purchaseListPermission,
    this.userKey,
  });

  UserRoleModel.fromJson(Map<dynamic, dynamic> json)
      : email = json['email'] as String,
        userTitle = json['userTitle'] as String,
        databaseId = json['databaseId'] as String,
        salePermission = (json['salePermission'] as bool?) ?? false,  // Handle null values
        partiesPermission = (json['partiesPermission'] as bool?) ?? false,
        purchasePermission = (json['purchasePermission'] as bool?) ?? false,
        productPermission = (json['productPermission'] as bool?) ?? false,
        profileEditPermission = (json['profileEditPermission'] as bool?) ?? false,
        addExpensePermission = (json['addExpensePermission'] as bool?) ?? false,
        lossProfitPermission = (json['lossProfitPermission'] as bool?) ?? false,
        dueListPermission = (json['dueListPermission'] as bool?) ?? false,
        stockPermission = (json['stockPermission'] as bool?) ?? false,
        reportsPermission = (json['reportsPermission'] as bool?) ?? false,
        salesListPermission = (json['salesListPermission'] as bool?) ?? false,
        purchaseListPermission = (json['purchaseListPermission'] as bool?) ?? false;

  Map<dynamic, dynamic> toJson() => <String, dynamic>{
    'email': email,
    'userTitle': userTitle,
    'databaseId': databaseId,
    'salePermission': salePermission,
    'partiesPermission': partiesPermission,
    'purchasePermission': purchasePermission,
    'productPermission': productPermission,
    'profileEditPermission': profileEditPermission,
    'addExpensePermission': addExpensePermission,
    'lossProfitPermission': lossProfitPermission,
    'dueListPermission': dueListPermission,
    'stockPermission': stockPermission,
    'reportsPermission': reportsPermission,
    'salesListPermission': salesListPermission,
    'purchaseListPermission': purchaseListPermission,
  };
}*/
//settlementPermission
// class UserRoleModel {
//   String email;
//   String userTitle;
//   String databaseId;
//   bool salePermission;
//   bool partiesPermission;
//   bool settlementPermission;
//   bool purchasePermission;
//   bool productPermission;
//   bool profileEditPermission;
//   bool addExpensePermission;
//   bool lossProfitPermission;
//   bool dueListPermission;
//   bool stockPermission;
//   bool reportsPermission;
//   bool salesListPermission;
//   bool purchaseListPermission;

//   String? userKey;
// //settlementPermission
//   UserRoleModel({
//     required this.email,
//     required this.userTitle,
//     required this.databaseId,
//     required this.salePermission,
//     required this.partiesPermission,
//     required this.settlementPermission,
//     required this.purchasePermission,
//     required this.productPermission,
//     required this.profileEditPermission,
//     required this.addExpensePermission,
//     required this.lossProfitPermission,
//     required this.dueListPermission,
//     required this.stockPermission,
//     required this.reportsPermission,
//     required this.salesListPermission,
//     required this.purchaseListPermission,
//     this.userKey,
//   });
// //settlementPermission
//   factory UserRoleModel.fromJson(Map<String, dynamic> json) => UserRoleModel(
//         email: json["email"] ?? '',
//         userTitle: json["userTitle"] ?? '',
//         databaseId: json["databaseId"] ?? '',
//         salePermission: json["salePermission"] ?? false,
//         partiesPermission: json["partiesPermission"] ?? false,
//         settlementPermission: json["settlementPermission"] ?? false,
//         purchasePermission: json["purchasePermission"] ?? false,
//         productPermission: json["productPermission"] ?? false,
//         profileEditPermission: json["profileEditPermission"] ?? false,
//         addExpensePermission: json["addExpensePermission"] ?? false,
//         lossProfitPermission: json["lossProfitPermission"] ?? false,
//         dueListPermission: json["dueListPermission"] ?? false,
//         stockPermission: json["stockPermission"] ?? false,
//         reportsPermission: json["reportsPermission"] ?? false,
//         salesListPermission: json["salesListPermission"] ?? false,
//         purchaseListPermission: json["purchaseListPermission"] ?? false,
//       );

//   Map<String, dynamic> toJson() => {
//         "email": email,
//         "userTitle": userTitle,
//         "databaseId": databaseId,
//         "salePermission": salePermission,
//         "partiesPermission": partiesPermission,
//         "purchasePermission": purchasePermission,
//         "productPermission": productPermission,
//         "profileEditPermission": profileEditPermission,
//         "addExpensePermission": addExpensePermission,
//         "lossProfitPermission": lossProfitPermission,
//         "dueListPermission": dueListPermission,
//         "stockPermission": stockPermission,
//         "reportsPermission": reportsPermission,
//         "salesListPermission": salesListPermission,
//         "purchaseListPermission": purchaseListPermission,
//       };
// }
