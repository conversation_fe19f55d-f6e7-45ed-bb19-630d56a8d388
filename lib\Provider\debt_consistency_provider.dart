import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Provider/customer_provider.dart';
import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/utils/debt_update_utils.dart';

/// مزود للتحقق من اتساق بيانات المديونية
final debtConsistencyProvider = Provider<DebtConsistencyService>((ref) {
  return DebtConsistencyService(ref);
});

/// مزود لحالة التحقق من اتساق البيانات
final debtConsistencyStateProvider = StateProvider<DebtConsistencyState>((ref) {
  return DebtConsistencyState(
    isChecking: false,
    totalCustomers: 0,
    checkedCustomers: 0,
    inconsistentCustomers: 0,
    inconsistencyResults: [],
  );
});

/// خدمة التحقق من اتساق بيانات المديونية
class DebtConsistencyService {
  final Ref _ref;

  DebtConsistencyService(this._ref);

  /// التحقق من اتساق بيانات المديونية لجميع العملاء
  Future<void> checkAllCustomersDebtConsistency() async {
    // تحديث حالة التحقق
    _ref.read(debtConsistencyStateProvider.notifier).state =
        DebtConsistencyState(
      isChecking: true,
      totalCustomers: 0,
      checkedCustomers: 0,
      inconsistentCustomers: 0,
      inconsistencyResults: [],
    );

    try {
      // الحصول على جميع العملاء
      final customers = await _ref.read(customerProvider.future);

      // تحديث إجمالي العملاء
      _ref
          .read(debtConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                totalCustomers: customers.length,
              ));

      // التحقق من اتساق بيانات كل عميل
      for (var customer in customers) {
        // تجاهل العملاء الذين ليس لديهم ديون
        if (double.parse(customer.dueAmount) <= 0) {
          _ref
              .read(debtConsistencyStateProvider.notifier)
              .update((state) => state.copyWith(
                    checkedCustomers: state.checkedCustomers + 1,
                  ));
          continue;
        }

        // قراءة قيمة الدين الحالية
        final storedDue = int.tryParse(customer.dueAmount) ?? 0;

        // حساب إجمالي الدين من الفواتير
        final calculatedDue =
            await calculateTotalDueFromInvoices(customer.phoneNumber);

        // التحقق من اتساق البيانات
        final isConsistent = storedDue == calculatedDue;

        // تحديث حالة التحقق
        _ref.read(debtConsistencyStateProvider.notifier).update((state) {
          final newResults =
              List<Map<String, dynamic>>.from(state.inconsistencyResults);

          newResults.add({
            'phoneNumber': customer.phoneNumber,
            'storedDue': storedDue,
            'calculatedDue': calculatedDue,
            'difference': calculatedDue - storedDue,
            'isConsistent': isConsistent,
            'customerName': customer.customerName,
          });

          return state.copyWith(
            checkedCustomers: state.checkedCustomers + 1,
            inconsistentCustomers: isConsistent
                ? state.inconsistentCustomers
                : state.inconsistentCustomers + 1,
            inconsistencyResults: newResults,
          );
        });
      }

      // تحديث حالة التحقق
      _ref
          .read(debtConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المديونية

      // تحديث حالة التحقق
      _ref
          .read(debtConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    }
  }

  /// التحقق من اتساق بيانات المديونية لعميل محدد
  Future<Map<String, dynamic>> checkCustomerDebtConsistency(
      CustomerModel customer) async {
    try {
      // قراءة قيمة الدين الحالية
      final storedDue = int.tryParse(customer.dueAmount) ?? 0;

      // حساب إجمالي الدين من الفواتير
      final calculatedDue =
          await calculateTotalDueFromInvoices(customer.phoneNumber);

      // التحقق من اتساق البيانات
      final isConsistent = storedDue == calculatedDue;

      return {
        'phoneNumber': customer.phoneNumber,
        'storedDue': storedDue,
        'calculatedDue': calculatedDue,
        'difference': calculatedDue - storedDue,
        'isConsistent': isConsistent,
        'customerName': customer.customerName,
      };
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المديونية للعميل
      return {
        'phoneNumber': customer.phoneNumber,
        'storedDue': 0,
        'calculatedDue': 0,
        'difference': 0,
        'isConsistent': true,
        'customerName': customer.customerName,
        'error': e.toString(),
      };
    }
  }

  /// تصحيح عدم اتساق بيانات المديونية لعميل محدد
  Future<bool> fixCustomerDebtInconsistency(
      String phoneNumber, int calculatedDue) async {
    try {
      final ref = FirebaseDatabaseService.getReference('$constUserId/Customers',
          keepSynced: true);

      final snapshot =
          await ref.orderByChild('phoneNumber').equalTo(phoneNumber).get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          final storedDue =
              int.tryParse(element.child('due').value.toString()) ?? 0;

          await ref
              .child(element.key!)
              .update({'due': calculatedDue.toString()});

          // تسجيل عملية التصحيح
          logInconsistency(
            phoneNumber: phoneNumber,
            storedDue: storedDue,
            calculatedDue: calculatedDue,
          );
        }

        return true;
      }

      return false;
    } catch (e) {
      // خطأ في تصحيح عدم اتساق بيانات المديونية للعميل
      return false;
    }
  }

  /// تصحيح عدم اتساق بيانات المديونية لجميع العملاء
  Future<int> fixAllCustomersDebtInconsistency() async {
    try {
      final state = _ref.read(debtConsistencyStateProvider);
      int fixedCount = 0;

      for (var result in state.inconsistencyResults) {
        if (result['isConsistent'] == false) {
          final success = await fixCustomerDebtInconsistency(
            result['phoneNumber'],
            result['calculatedDue'],
          );

          if (success) {
            fixedCount++;

            // تحديث حالة التحقق
            _ref.read(debtConsistencyStateProvider.notifier).update((state) {
              final newResults =
                  List<Map<String, dynamic>>.from(state.inconsistencyResults);
              final index = newResults
                  .indexWhere((r) => r['phoneNumber'] == result['phoneNumber']);

              if (index != -1) {
                newResults[index] = {
                  ...newResults[index],
                  'storedDue': result['calculatedDue'],
                  'difference': 0,
                  'isConsistent': true,
                };
              }

              return state.copyWith(
                inconsistentCustomers: state.inconsistentCustomers - 1,
                inconsistencyResults: newResults,
              );
            });
          }
        }
      }

      return fixedCount;
    } catch (e) {
      // خطأ في تصحيح عدم اتساق بيانات المديونية لجميع العملاء
      return 0;
    }
  }
}

/// حالة التحقق من اتساق بيانات المديونية
class DebtConsistencyState {
  final bool isChecking;
  final int totalCustomers;
  final int checkedCustomers;
  final int inconsistentCustomers;
  final List<Map<String, dynamic>> inconsistencyResults;

  DebtConsistencyState({
    required this.isChecking,
    required this.totalCustomers,
    required this.checkedCustomers,
    required this.inconsistentCustomers,
    required this.inconsistencyResults,
  });

  DebtConsistencyState copyWith({
    bool? isChecking,
    int? totalCustomers,
    int? checkedCustomers,
    int? inconsistentCustomers,
    List<Map<String, dynamic>>? inconsistencyResults,
  }) {
    return DebtConsistencyState(
      isChecking: isChecking ?? this.isChecking,
      totalCustomers: totalCustomers ?? this.totalCustomers,
      checkedCustomers: checkedCustomers ?? this.checkedCustomers,
      inconsistentCustomers:
          inconsistentCustomers ?? this.inconsistentCustomers,
      inconsistencyResults: inconsistencyResults ?? this.inconsistencyResults,
    );
  }
}
