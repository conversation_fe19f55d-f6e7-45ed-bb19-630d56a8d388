// استخدام التاريخ العادي بدلاً من Timestamp

class WhatsappCampaignModel {
  final String id;
  final String name;
  final String message;
  final DateTime createdAt;
  final DateTime scheduledAt;
  final String status; // draft, scheduled, sent, failed
  final List<String> recipientGroups;
  final List<WhatsappRecipientModel> recipients;
  final Map<String, dynamic> statistics;

  WhatsappCampaignModel({
    required this.id,
    required this.name,
    required this.message,
    required this.createdAt,
    required this.scheduledAt,
    required this.status,
    required this.recipientGroups,
    required this.recipients,
    required this.statistics,
  });

  // تحويل من Firestore
  factory WhatsappCampaignModel.fromMap(Map<String, dynamic> map, String id) {
    List<WhatsappRecipientModel> recipientsList = [];
    if (map['recipients'] != null && map['recipients'] is List) {
      recipientsList = List<WhatsappRecipientModel>.from(
        (map['recipients'] as List).map(
          (recipient) => WhatsappRecipientModel.fromMap(recipient),
        ),
      );
    }

    List<String> groupsList = [];
    if (map['recipientGroups'] != null && map['recipientGroups'] is List) {
      groupsList = List<String>.from(map['recipientGroups']);
    }

    return WhatsappCampaignModel(
      id: id,
      name: map['name'] ?? '',
      message: map['message'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      scheduledAt: map['scheduledAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['scheduledAt'])
          : DateTime.now(),
      status: map['status'] ?? 'draft',
      recipientGroups: groupsList,
      recipients: recipientsList,
      statistics: map['statistics'] ??
          {
            'sent': 0,
            'delivered': 0,
            'read': 0,
            'failed': 0,
          },
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'message': message,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'scheduledAt': scheduledAt.millisecondsSinceEpoch,
      'status': status,
      'recipientGroups': recipientGroups,
      'recipients': recipients.map((recipient) => recipient.toMap()).toList(),
      'statistics': statistics,
    };
  }

  // نسخة مع تعديلات
  WhatsappCampaignModel copyWith({
    String? id,
    String? name,
    String? message,
    DateTime? createdAt,
    DateTime? scheduledAt,
    String? status,
    List<String>? recipientGroups,
    List<WhatsappRecipientModel>? recipients,
    Map<String, dynamic>? statistics,
  }) {
    return WhatsappCampaignModel(
      id: id ?? this.id,
      name: name ?? this.name,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      status: status ?? this.status,
      recipientGroups: recipientGroups ?? this.recipientGroups,
      recipients: recipients ?? this.recipients,
      statistics: statistics ?? this.statistics,
    );
  }
}

class WhatsappRecipientModel {
  final String name;
  final String phone;
  final String status; // pending, sent, delivered, read, failed
  final DateTime? sentAt;

  WhatsappRecipientModel({
    required this.name,
    required this.phone,
    required this.status,
    this.sentAt,
  });

  // تحويل من Map
  factory WhatsappRecipientModel.fromMap(Map<dynamic, dynamic> map) {
    return WhatsappRecipientModel(
      name: map['name'] ?? '',
      phone: map['phone'] ?? '',
      status: map['status'] ?? 'pending',
      sentAt: map['sentAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['sentAt'])
          : null,
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phone': phone,
      'status': status,
      'sentAt': sentAt?.millisecondsSinceEpoch,
    };
  }

  // نسخة مع تعديلات
  WhatsappRecipientModel copyWith({
    String? name,
    String? phone,
    String? status,
    DateTime? sentAt,
  }) {
    return WhatsappRecipientModel(
      name: name ?? this.name,
      phone: phone ?? this.phone,
      status: status ?? this.status,
      sentAt: sentAt ?? this.sentAt,
    );
  }
}

class WhatsappGroupModel {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final List<WhatsappContactModel> contacts;

  WhatsappGroupModel({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.contacts,
  });

  // تحويل من Firestore
  factory WhatsappGroupModel.fromMap(Map<String, dynamic> map, String id) {
    List<WhatsappContactModel> contactsList = [];
    if (map['contacts'] != null && map['contacts'] is List) {
      contactsList = List<WhatsappContactModel>.from(
        (map['contacts'] as List).map(
          (contact) => WhatsappContactModel.fromMap(contact),
        ),
      );
    }

    return WhatsappGroupModel(
      id: id,
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      contacts: contactsList,
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'contacts': contacts.map((contact) => contact.toMap()).toList(),
    };
  }

  // نسخة مع تعديلات
  WhatsappGroupModel copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    List<WhatsappContactModel>? contacts,
  }) {
    return WhatsappGroupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      contacts: contacts ?? this.contacts,
    );
  }
}

class WhatsappContactModel {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String notes;
  final DateTime createdAt;

  WhatsappContactModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.notes,
    required this.createdAt,
  });

  // تحويل من Map
  factory WhatsappContactModel.fromMap(Map<dynamic, dynamic> map) {
    return WhatsappContactModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      phone: map['phone'] ?? '',
      email: map['email'] ?? '',
      notes: map['notes'] ?? '',
      createdAt: map['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : DateTime.now(),
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'notes': notes,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  // نسخة مع تعديلات
  WhatsappContactModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? notes,
    DateTime? createdAt,
  }) {
    return WhatsappContactModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
