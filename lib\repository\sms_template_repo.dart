import 'package:flutter/foundation.dart';
import 'package:mobile_pos/models/sms_template_model.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';

class SmsTemplateRepo {
  // الحصول على قوالب الرسائل
  Stream<SmsTemplateModel> getSmsTemplates() {
    // استخدام خدمة FirebaseDatabaseService للاستماع للتغييرات
    return FirebaseDatabaseService.listenToPath('SmsTemplates').map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null || data.isEmpty) {
        // إذا لم تكن هناك بيانات، قم بإنشاء نموذج فارغ
        debugPrint('لا توجد قوالب رسائل، إنشاء نموذج فارغ');
        return SmsTemplateModel(
          id: 'default',
          saleTemplate: '',
          saleReturnTemplate: '',
          quotationTemplate: '',
          purchaseTemplate: '',
          purchaseReturnTemplate: '',
          dueTemplate: '',
          bulkSmsTemplate: '',
        );
      }

      // استخراج المفتاح الأول (يجب أن يكون هناك نموذج واحد فقط)
      final key = data.keys.first.toString();
      final value = data[key];

      if (value is Map<dynamic, dynamic>) {
        Map<String, dynamic> templateMap = {};
        value.forEach((k, v) {
          templateMap[k.toString()] = v;
        });
        return SmsTemplateModel.fromMap(templateMap, key);
      }

      // إذا لم يكن هناك بيانات صالحة، قم بإنشاء نموذج فارغ
      return SmsTemplateModel(
        id: 'default',
        saleTemplate: '',
        saleReturnTemplate: '',
        quotationTemplate: '',
        purchaseTemplate: '',
        purchaseReturnTemplate: '',
        dueTemplate: '',
        bulkSmsTemplate: '',
      );
    });
  }

  // حفظ قوالب الرسائل
  Future<void> saveSmsTemplates(SmsTemplateModel templates) async {
    try {
      // استخدام التخزين المؤقت للحصول على البيانات
      final snapshot = await FirebaseDatabaseService.getDataWithCaching(
          'SmsTemplates',
          cacheDuration: const Duration(minutes: 5));

      if (snapshot.exists) {
        // إذا كانت موجودة، قم بتحديثها
        final data = snapshot.value as Map<dynamic, dynamic>;
        final key = data.keys.first.toString();

        // استخدام مرجع آمن للتحديث
        final ref = FirebaseDatabaseService.getReference('SmsTemplates/$key');
        await ref.update(templates.toMap());

        // تحديث التخزين المؤقت
        FirebaseDatabaseService.clearCache();

        debugPrint('تم تحديث قوالب الرسائل بنجاح');
      } else {
        // إذا لم تكن موجودة، قم بإنشاء جديدة
        final ref = FirebaseDatabaseService.getReference('SmsTemplates');
        await ref.push().set(templates.toMap());

        // تحديث التخزين المؤقت
        FirebaseDatabaseService.clearCache();

        debugPrint('تم إنشاء قوالب الرسائل بنجاح');
      }
    } catch (e) {
      // خطأ في حفظ قوالب الرسائل
      debugPrint('خطأ في حفظ قوالب الرسائل: $e');
      rethrow;
    }
  }
}
