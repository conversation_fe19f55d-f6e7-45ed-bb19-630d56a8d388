// import 'dart:convert';
// // import 'package:firebase_database/firebase_database.dart'; // غير مستخدم
// import 'package:flutter/material.dart';
// import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
// // import 'package:mobile_pos/constant.dart'; // غير مستخدم
// import 'package:mobile_pos/services/firebase_database_service.dart';
// import 'package:mobile_pos/currency.dart';

// class PaginatedCustomerRepo {
//   // تحميل العملاء بالصفحات
//   Future<List<CustomerModel>> getCustomersByPage(int page, int limit) async {
//     List<CustomerModel> customerList = [];

//     try {
//       // حساب نقطة البداية للصفحة الحالية
//       final startAt = (page - 1) * limit;

//       // استعلام Firebase مع حدود للنتائج
//       final query = FirebaseDatabaseService.getReference(
//               '$constUserId/Customers')
//           .orderByKey()
//           .startAt(startAt.toString()) // استخدام startAt للبدء من نقطة معينة
//           .limitToFirst(limit);

//       final snapshot = await query.get();

//       if (snapshot.exists) {
//         for (var element in snapshot.children) {
//           var data = jsonDecode(jsonEncode(element.value));
//           var customer = CustomerModel.fromJson(data);
//           // تعيين معرف العميل
//           customer.id = element.key ?? '';
//           customerList.add(customer);
//         }
//       }

//       // الحفاظ على المزامنة للعمليات المستقبلية
//       FirebaseDatabaseService.getReference('$constUserId/Customers',
//           keepSynced: true);

//       return customerList;
//     } catch (e) {
//       debugPrint('خطأ في تحميل العملاء: $e');
//       return [];
//     }
//   }

//   // البحث عن العملاء
//   Future<List<CustomerModel>> searchCustomers(String query) async {
//     List<CustomerModel> customerList = [];

//     try {
//       // استعلام Firebase للحصول على جميع العملاء (مع حد أقصى للتحكم في الأداء)
//       final snapshot =
//           await FirebaseDatabaseService.getReference('$constUserId/Customers')
//               .orderByChild('customerName') // يمكن تغييره حسب حقل البحث المفضل
//               .limitToFirst(100) // حد أقصى للنتائج
//               .get();

//       if (snapshot.exists) {
//         for (var element in snapshot.children) {
//           var data = jsonDecode(jsonEncode(element.value));
//           var customer = CustomerModel.fromJson(data);
//           // تعيين معرف العميل
//           customer.id = element.key ?? '';

//           // تصفية النتائج محليًا حسب الاستعلام
//           if (customer.customerName
//                   .toLowerCase()
//                   .contains(query.toLowerCase()) ||
//               customer.phoneNumber.contains(query)) {
//             customerList.add(customer);
//           }
//         }
//       }

//       return customerList;
//     } catch (e) {
//       debugPrint('خطأ في البحث عن العملاء: $e');
//       return [];
//     }
//   }

//   // تحميل العملاء حسب النوع
//   Future<List<CustomerModel>> getCustomersByType(
//       String type, int page, int limit) async {
//     if (type == 'الكل') {
//       return getCustomersByPage(page, limit);
//     }

//     // تحويل نوع العميل من العربية إلى الإنجليزية
//     String englishType;
//     switch (type) {
//       case 'تاجر تجزئة':
//         englishType = 'Retailer';
//         break;
//       case 'مورد':
//         englishType = 'Supplier';
//         break;
//       case 'تاجر جملة':
//         englishType = 'Wholesaler';
//         break;
//       case 'موزع':
//         englishType = 'Dealer';
//         break;
//       default:
//         englishType = '';
//     }

//     List<CustomerModel> customerList = [];

//     try {
//       // استعلام Firebase للحصول على العملاء حسب النوع
//       final query =
//           FirebaseDatabaseService.getReference('$constUserId/Customers')
//               .orderByChild('type')
//               .equalTo(englishType)
//               .limitToFirst(limit);

//       final snapshot = await query.get();

//       if (snapshot.exists) {
//         for (var element in snapshot.children) {
//           var data = jsonDecode(jsonEncode(element.value));
//           var customer = CustomerModel.fromJson(data);
//           // تعيين معرف العميل
//           customer.id = element.key ?? '';
//           customerList.add(customer);
//         }
//       }

//       return customerList;
//     } catch (e) {
//       debugPrint('خطأ في تحميل العملاء حسب النوع: $e');
//       return [];
//     }
//   }

//   // تخزين العملاء محليًا (يمكن استخدامه مع SQLite أو Hive)
//   Future<void> cacheCustomers(List<CustomerModel> customers) async {
//     // سيتم تنفيذه لاحقًا عند إضافة التخزين المحلي
//   }
// }
