/// نموذج مستخدم الدردشة في Firebase
class FirebaseChatUser {
  String? id; // معرف المستخدم في Firebase
  String userId; // معرف المستخدم في النظام
  String name; // اسم المستخدم
  String email; // البريد الإلكتروني
  String profileImage; // صورة المستخدم
  String deviceToken; // رمز الجهاز
  DateTime lastSeen; // آخر ظهور
  bool isOnline; // حالة الاتصال
  String phone; // رقم الهاتف
  String role; // دور المستخدم

  FirebaseChatUser({
    this.id,
    this.userId = '',
    this.name = '',
    this.email = '',
    this.profileImage = '',
    this.deviceToken = '',
    DateTime? lastSeen,
    this.isOnline = false,
    this.phone = '',
    this.role = 'user',
  }) : lastSeen = lastSeen ?? DateTime.now();

  // إنشاء من Map (Firebase)
  factory FirebaseChatUser.fromMap(Map<String, dynamic> map, String id) {
    return FirebaseChatUser(
      id: id,
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      profileImage: map['profileImage'] ?? '',
      deviceToken: map['deviceToken'] ?? '',
      lastSeen: map['lastSeen'] != null
          ? DateTime.parse(map['lastSeen'])
          : DateTime.now(),
      isOnline: map['isOnline'] ?? false,
      phone: map['phone'] ?? '',
      role: map['role'] ?? 'user',
    );
  }

  // تحويل إلى Map (Firebase)
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'email': email,
      'profileImage': profileImage,
      'deviceToken': deviceToken,
      'lastSeen': lastSeen.toIso8601String(),
      'isOnline': isOnline,
      'phone': phone,
      'role': role,
    };
  }
}
