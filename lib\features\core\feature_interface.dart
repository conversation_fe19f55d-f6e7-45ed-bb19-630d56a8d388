// بسم الله الرحمن الرحيم
// واجهة الميزات - تعريف الواجهة التي يجب أن تنفذها جميع الميزات

import 'package:flutter/material.dart';

/// واجهة الميزة - يجب تنفيذها من قبل جميع الميزات
abstract class FeatureInterface {
  /// اسم الميزة
  String get featureName;

  /// وصف الميزة
  String get featureDescription;

  /// أيقونة الميزة
  IconData get featureIcon;

  /// هل الميزة مفعلة
  bool get isEnabled;

  /// تعيين حالة تفعيل الميزة
  Future<void> setEnabled(bool enabled);

  /// الحصول على شاشة الميزة الرئيسية
  Widget getMainScreen();

  /// الحصول على مسارات الميزة
  Map<String, WidgetBuilder> getRoutes();

  /// تهيئة الميزة
  Future<void> initialize();

  /// إيقاف الميزة
  Future<void> dispose();
}
