// بسم الله الرحمن الرحيم
// مثال على استخدام ميزة Trial Balance

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/trial_balance_screen.dart';
import 'providers/trial_balance_provider.dart';
import 'trial_balance_feature.dart';

/// مثال على كيفية استخدام ميزة Trial Balance في التطبيق
class TrialBalanceExampleUsage {
  /// 1. الوصول المباشر إلى شاشة Trial Balance
  static void navigateToTrialBalance(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TrialBalanceScreen()),
    );
  }

  /// 2. إنشاء تقرير برمجياً
  static Future<void> createReportProgrammatically(WidgetRef ref) async {
    // تحديد الفترة الزمنية
    final fromDate = DateTime(2024, 1, 1);
    final toDate = DateTime(2024, 1, 31);

    // تحديث التواريخ في المزود
    ref.read(trialBalanceProvider.notifier).updateDateRange(fromDate, toDate);

    // إنشاء التقرير
    await ref.read(trialBalanceProvider.notifier).generateReport(
          title: 'تقرير ميزان المراجعة - يناير 2024',
        );

    // التحقق من النتيجة
    final state = ref.read(trialBalanceProvider);
    if (state.hasCurrentReport) {
      print('تم إنشاء التقرير بنجاح');
      print('إجمالي المدين: ${state.totalDebits}');
      print('إجمالي الدائن: ${state.totalCredits}');
      print('متوازن: ${state.isCurrentReportBalanced}');
    }
  }

  /// 3. حفظ التقرير الحالي
  static Future<bool> saveCurrentReport(WidgetRef ref) async {
    final success =
        await ref.read(trialBalanceProvider.notifier).saveCurrentReport();
    if (success) {
      print('تم حفظ التقرير بنجاح');
    } else {
      print('فشل في حفظ التقرير');
    }
    return success;
  }

  /// 4. الحصول على التقارير المحفوظة
  static Future<void> loadSavedReports(WidgetRef ref) async {
    await ref.read(trialBalanceProvider.notifier).refreshSavedReports();
    final state = ref.read(trialBalanceProvider);

    print('عدد التقارير المحفوظة: ${state.savedReports.length}');
    for (var report in state.savedReports) {
      print(
          '- ${report.reportTitle} (${report.isBalanced ? "متوازن" : "غير متوازن"})');
    }
  }

  /// 5. استخدام الفترات المحددة مسبقاً
  static void setPresetPeriod(WidgetRef ref, PresetPeriod period) {
    ref.read(trialBalanceProvider.notifier).setPresetPeriod(period);

    final state = ref.read(trialBalanceProvider);
    print('تم تحديد الفترة: ${period.arabicName}');
    print('من: ${state.fromDate}');
    print('إلى: ${state.toDate}');
  }

  /// 6. بناء ويدجت مخصص للوصول السريع
  static Widget buildQuickAccessButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => navigateToTrialBalance(context),
      icon: const Icon(Icons.balance),
      label: const Text('ميزان المراجعة'),
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
      ),
    );
  }

  /// 7. بناء ويدجت عرض ملخص سريع
  static Widget buildQuickSummary() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(trialBalanceProvider);

        if (!state.hasCurrentReport) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('لا يوجد تقرير حالي'),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ملخص ميزان المراجعة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('إجمالي المدين'),
                        Text(
                          '${state.totalDebits.toStringAsFixed(2)} جنيه',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('إجمالي الدائن'),
                        Text(
                          '${state.totalCredits.toStringAsFixed(2)} جنيه',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: state.isCurrentReportBalanced
                        ? Colors.green
                        : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    state.isCurrentReportBalanced ? 'متوازن ✓' : 'غير متوازن ✗',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 8. مثال على شاشة مخصصة تستخدم الميزة
  static Widget buildCustomScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال Trial Balance'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // ملخص سريع
            buildQuickSummary(),
            const SizedBox(height: 16),

            // أزرار الإجراءات
            Consumer(
              builder: (context, ref, child) {
                return Column(
                  children: [
                    ElevatedButton(
                      onPressed: () => createReportProgrammatically(ref),
                      child: const Text('إنشاء تقرير لشهر يناير'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () => saveCurrentReport(ref),
                      child: const Text('حفظ التقرير الحالي'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () => loadSavedReports(ref),
                      child: const Text('تحديث التقارير المحفوظة'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () =>
                          setPresetPeriod(ref, PresetPeriod.thisMonth),
                      child: const Text('تحديد فترة الشهر الحالي'),
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // زر الانتقال إلى الشاشة الكاملة
            Consumer(
              builder: (context, ref, child) {
                return buildQuickAccessButton(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 9. تهيئة الميزة في التطبيق الرئيسي
  static Future<void> initializeFeature() async {
    final feature = TrialBalanceFeature();
    await feature.initialize();
    print('تم تهيئة ميزة Trial Balance بنجاح');
  }

  /// 10. الحصول على معلومات الميزة
  static void printFeatureInfo() {
    final feature = TrialBalanceFeature();

    print('اسم الميزة: ${feature.featureName}');
    print('وصف الميزة: ${feature.featureDescription}');
    print('حالة التفعيل: ${feature.isEnabled}');
    print('المسارات المتاحة: ${feature.getRoutes().keys.join(', ')}');
    print('الإعدادات: ${feature.getConfiguration()}');
    print('نص المساعدة: ${feature.getHelpText()}');
  }
}

/// مثال على شاشة تطبيق كاملة تستخدم الميزة
class TrialBalanceExampleApp extends StatelessWidget {
  const TrialBalanceExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        title: 'مثال Trial Balance',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Cairo',
        ),
        home: TrialBalanceExampleUsage.buildCustomScreen(),
        routes: {
          '/trial-balance': (context) => const TrialBalanceScreen(),
        },
      ),
    );
  }
}
