// بسم الله الرحمن الرحيم
// خدمة التخزين - توفر واجهة موحدة للتخزين المحلي

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين المحلي
class StorageService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة التخزين
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // مثيل Shared Preferences
  SharedPreferences? _prefs;

  /// تهيئة خدمة التخزين
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// حفظ قيمة نصية
  Future<bool> setString(String key, String value) async {
    if (_prefs == null) await initialize();
    return await _prefs!.setString(key, value);
  }

  /// الحصول على قيمة نصية
  String? getString(String key) {
    return _prefs?.getString(key);
  }

  /// حفظ قيمة منطقية
  Future<bool> setBool(String key, bool value) async {
    if (_prefs == null) await initialize();
    return await _prefs!.setBool(key, value);
  }

  /// الحصول على قيمة منطقية
  bool? getBool(String key) {
    return _prefs?.getBool(key);
  }

  /// حفظ قيمة عددية صحيحة
  Future<bool> setInt(String key, int value) async {
    if (_prefs == null) await initialize();
    return await _prefs!.setInt(key, value);
  }

  /// الحصول على قيمة عددية صحيحة
  int? getInt(String key) {
    return _prefs?.getInt(key);
  }

  /// حفظ قيمة عددية عشرية
  Future<bool> setDouble(String key, double value) async {
    if (_prefs == null) await initialize();
    return await _prefs!.setDouble(key, value);
  }

  /// الحصول على قيمة عددية عشرية
  double? getDouble(String key) {
    return _prefs?.getDouble(key);
  }

  /// حفظ قائمة نصية
  Future<bool> setStringList(String key, List<String> value) async {
    if (_prefs == null) await initialize();
    return await _prefs!.setStringList(key, value);
  }

  /// الحصول على قائمة نصية
  List<String>? getStringList(String key) {
    return _prefs?.getStringList(key);
  }

  /// حفظ كائن
  Future<bool> setObject(String key, Map<String, dynamic> value) async {
    if (_prefs == null) await initialize();
    return await _prefs!.setString(key, jsonEncode(value));
  }

  /// الحصول على كائن
  Map<String, dynamic>? getObject(String key) {
    final data = _prefs?.getString(key);
    if (data != null && data.isNotEmpty) {
      try {
        return jsonDecode(data) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('خطأ في تحويل البيانات: $e');
      }
    }
    return null;
  }

  /// حذف قيمة
  Future<bool> remove(String key) async {
    if (_prefs == null) await initialize();
    return await _prefs!.remove(key);
  }

  /// مسح جميع القيم
  Future<bool> clear() async {
    if (_prefs == null) await initialize();
    return await _prefs!.clear();
  }

  /// التحقق من وجود مفتاح
  bool containsKey(String key) {
    return _prefs?.containsKey(key) ?? false;
  }
}
