import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:amrdev_win_pos/Screen/WareHouse/warehouse_model.dart';
import '../services/firebase_database_service.dart';
import 'package:amrdev_win_pos/model/category_model.dart';

import '../const.dart';
import '../model/brands_model.dart';
import '../model/product_model.dart';
import '../model/unit_model.dart';

class ProductRepo {
  Future<List<ProductModel>> getAllProduct() async {
    List<ProductModel> productList = [];

    try {
      final userId = await getUserID();
      debugPrint('🔍 User ID للمنتجات: $userId');

      if (userId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات المنتجات');
        return productList;
      }

      final snapshot = await FirebaseDatabaseService.ref(userId).child('المنتجات').orderByKey().get();
      debugPrint('📊 تم الحصول على بيانات المنتجات من Firebase - عدد العناصر: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        productList.add(ProductModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }

      debugPrint('✅ تم جلب ${productList.length} منتج بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المنتجات: $e');
    }

    return productList;
  }

  Future<List<dynamic>> getAllProductByJson({required String searchData}) async {
    List<dynamic> productList = [];
    await FirebaseDatabaseService.ref(await getUserID()).child('المنتجات').orderByKey().get().then((value) {
      for (var element in value.children) {
        if (jsonDecode(jsonEncode(element.value))['productName'].toString().toLowerCase().contains(searchData.toLowerCase())) {
          productList.add(element.value);
        }
      }
    });
    return productList;
  }


  Future<List<dynamic>> getAllProductByJsonWarehouse({required String searchData,required WareHouseModel warehouseId}) async {
    List<dynamic> productList = [];
    await FirebaseDatabaseService.ref(await getUserID()).child('المنتجات').orderByKey().get().then((value) {
      for (var element in value.children) {
        if (jsonDecode(jsonEncode(element.value))['productName'].toString().toLowerCase().contains(searchData.toLowerCase()) &&  ((jsonDecode(jsonEncode(element.value))['warehouseId'] == '' && warehouseId.warehouseName == 'InHouse') ? true : jsonDecode(jsonEncode(element.value))['warehouseId'].toString() == warehouseId.id)) {
          productList.add(element.value);
        }
      }
    });
    return productList;
  }

  Future<List<CategoryModel>> getAllCategory() async {
    List<CategoryModel> categoryList = [];

    try {
      final userId = await getUserID();
      debugPrint('🔍 User ID للفئات: $userId');

      if (userId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات الفئات');
        return categoryList;
      }

      final snapshot = await FirebaseDatabaseService.ref(userId).child('الفئات').orderByKey().get();
      debugPrint('📊 تم الحصول على بيانات الفئات من Firebase - عدد العناصر: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        categoryList.add(CategoryModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }

      debugPrint('✅ تم جلب ${categoryList.length} فئة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات الفئات: $e');
    }

    return categoryList;
  }

  Future<List<BrandsModel>> getAllBrands() async {
    List<BrandsModel> brandList = [];

    try {
      final userId = await getUserID();
      debugPrint('🔍 User ID للعلامات التجارية: $userId');

      if (userId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات العلامات التجارية');
        return brandList;
      }

      final snapshot = await FirebaseDatabaseService.ref(userId).child('العلامات التجارية').orderByKey().get();
      debugPrint('📊 تم الحصول على بيانات العلامات التجارية من Firebase - عدد العناصر: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        brandList.add(BrandsModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }

      debugPrint('✅ تم جلب ${brandList.length} علامة تجارية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات العلامات التجارية: $e');
    }

    return brandList;
  }

  Future<List<UnitModel>> getAllUnits() async {
    List<UnitModel> unitList = [];
    await FirebaseDatabaseService.ref(await getUserID()).child('الوحدات').orderByKey().get().then((value) {
      for (var element in value.children) {
        unitList.add(UnitModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    return unitList;
  }
}
