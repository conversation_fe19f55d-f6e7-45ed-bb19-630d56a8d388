import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:amrdev_win_pos/Screen/WareHouse/warehouse_model.dart';
import 'package:amrdev_win_pos/model/category_model.dart';

import '../const.dart';
import '../model/brands_model.dart';
import '../model/product_model.dart';
import '../model/unit_model.dart';

class ProductRepo {
  Future<List<ProductModel>> getAllProduct() async {
    List<ProductModel> productList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          await getUserID();
        }
      }

      debugPrint('🔍 User ID للمنتجات: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات المنتجات');
        return productList;
      }

      await FirebaseDatabase.instance
          .ref(constUserId)
          .child('Products')
          .orderByKey()
          .get()
          .then((value) {
        debugPrint('📊 تم الحصول على بيانات المنتجات من Firebase');
        debugPrint('📊 snapshot.exists: ${value.exists}');
        debugPrint('📊 عدد العناصر: ${value.children.length}');

        for (var element in value.children) {
          productList.add(ProductModel.fromJson(jsonDecode(jsonEncode(element.value))));
        }
      });

      final productRef = FirebaseDatabase.instance.ref(constUserId).child('Products');
      productRef.keepSynced(true);

      debugPrint('✅ تم جلب ${productList.length} منتج بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المنتجات: $e');
    }

    return productList;
  }

  Future<List<dynamic>> getAllProductByJson({required String searchData}) async {
    List<dynamic> productList = [];
    // التأكد من وجود constUserId
    if (constUserId.isEmpty) {
      await getUserDataFromLocal();
      if (constUserId.isEmpty) {
        await getUserID();
      }
    }

    await FirebaseDatabase.instance.ref(constUserId).child('Products').orderByKey().get().then((value) {
      for (var element in value.children) {
        if (jsonDecode(jsonEncode(element.value))['productName'].toString().toLowerCase().contains(searchData.toLowerCase())) {
          productList.add(element.value);
        }
      }
    });
    return productList;
  }


  Future<List<dynamic>> getAllProductByJsonWarehouse({required String searchData,required WareHouseModel warehouseId}) async {
    List<dynamic> productList = [];
    // التأكد من وجود constUserId
    if (constUserId.isEmpty) {
      await getUserDataFromLocal();
      if (constUserId.isEmpty) {
        await getUserID();
      }
    }

    await FirebaseDatabase.instance.ref(constUserId).child('Products').orderByKey().get().then((value) {
      for (var element in value.children) {
        if (jsonDecode(jsonEncode(element.value))['productName'].toString().toLowerCase().contains(searchData.toLowerCase()) &&  ((jsonDecode(jsonEncode(element.value))['warehouseId'] == '' && warehouseId.warehouseName == 'InHouse') ? true : jsonDecode(jsonEncode(element.value))['warehouseId'].toString() == warehouseId.id)) {
          productList.add(element.value);
        }
      }
    });
    return productList;
  }

  Future<List<CategoryModel>> getAllCategory() async {
    List<CategoryModel> categoryList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          await getUserID();
        }
      }

      debugPrint('🔍 User ID للفئات: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات الفئات');
        return categoryList;
      }

      await FirebaseDatabase.instance
          .ref(constUserId)
          .child('Categories')
          .orderByKey()
          .get()
          .then((value) {
        debugPrint('📊 تم الحصول على بيانات الفئات من Firebase - عدد العناصر: ${value.children.length}');

        for (var element in value.children) {
          categoryList.add(CategoryModel.fromJson(jsonDecode(jsonEncode(element.value))));
        }
      });

      final categoryRef = FirebaseDatabase.instance.ref(constUserId).child('Categories');
      categoryRef.keepSynced(true);

      debugPrint('✅ تم جلب ${categoryList.length} فئة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات الفئات: $e');
    }

    return categoryList;
  }

  Future<List<BrandsModel>> getAllBrands() async {
    List<BrandsModel> brandList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          await getUserID();
        }
      }

      debugPrint('🔍 User ID للعلامات التجارية: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات العلامات التجارية');
        return brandList;
      }

      await FirebaseDatabase.instance
          .ref(constUserId)
          .child('Brands')
          .orderByKey()
          .get()
          .then((value) {
        debugPrint('📊 تم الحصول على بيانات العلامات التجارية من Firebase - عدد العناصر: ${value.children.length}');

        for (var element in value.children) {
          brandList.add(BrandsModel.fromJson(jsonDecode(jsonEncode(element.value))));
        }
      });

      final brandRef = FirebaseDatabase.instance.ref(constUserId).child('Brands');
      brandRef.keepSynced(true);

      debugPrint('✅ تم جلب ${brandList.length} علامة تجارية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات العلامات التجارية: $e');
    }

    return brandList;
  }

  Future<List<UnitModel>> getAllUnits() async {
    List<UnitModel> unitList = [];
    await FirebaseDatabaseService.ref(await getUserID()).child('الوحدات').orderByKey().get().then((value) {
      for (var element in value.children) {
        unitList.add(UnitModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    return unitList;
  }
}
