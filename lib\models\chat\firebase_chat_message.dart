import 'package:mobile_pos/models/chat/chat_message.dart';

/// نموذج رسالة الدردشة في Firebase
class FirebaseChatMessage {
  String? id; // معرف الرسالة في Firebase
  String text; // نص الرسالة
  String senderId; // معرف المرسل
  String senderName; // اسم المرسل
  String recipientId; // معرف المستلم
  String conversationId; // معرف المحادثة
  String messageType; // نوع الرسالة
  String? mediaUrl; // رابط ملف الوسائط
  bool isRead; // هل تمت قراءة الرسالة
  bool isDelivered; // هل تم تسليم الرسالة
  Map<String, dynamic> reactions; // تفاعلات المستخدمين
  String? replyTo; // معرف الرسالة المرد عليها
  bool isDeleted; // هل تم حذف الرسالة
  List<String> deletedFor; // قائمة المستخدمين الذين تم حذف الرسالة لهم
  DateTime? createdAt; // تاريخ إنشاء الرسالة

  // أنواع الرسائل
  static const String typeText = 'text';
  static const String typeImage = 'image';
  static const String typeVoice = 'voice';
  static const String typeFile = 'file';
  static const String typeVideo = 'video';
  static const String typeLocation = 'location';
  static const String typeContact = 'contact';
  static const String typeCall = 'call';

  FirebaseChatMessage({
    this.id,
    this.text = '',
    this.senderId = '',
    this.senderName = '',
    this.recipientId = '',
    this.conversationId = '',
    this.messageType = typeText,
    this.mediaUrl,
    this.isRead = false,
    this.isDelivered = false,
    this.reactions = const {},
    this.replyTo,
    this.isDeleted = false,
    this.deletedFor = const [],
    this.createdAt,
  });

  // إنشاء من Map (Firebase)
  factory FirebaseChatMessage.fromMap(Map<String, dynamic> map, String id) {
    return FirebaseChatMessage(
      id: id,
      text: map['text'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      recipientId: map['recipientId'] ?? '',
      conversationId: map['conversationId'] ?? '',
      messageType: map['messageType'] ?? typeText,
      mediaUrl: map['mediaUrl'],
      isRead: map['isRead'] ?? false,
      isDelivered: map['isDelivered'] ?? false,
      reactions: Map<String, dynamic>.from(map['reactions'] ?? {}),
      replyTo: map['replyTo'],
      isDeleted: map['isDeleted'] ?? false,
      deletedFor: List<String>.from(map['deletedFor'] ?? []),
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
    );
  }

  // تحويل إلى Map (Firebase)
  Map<String, dynamic> toMap() {
    return {
      'text': text,
      'senderId': senderId,
      'senderName': senderName,
      'recipientId': recipientId,
      'conversationId': conversationId,
      'messageType': messageType,
      'mediaUrl': mediaUrl,
      'isRead': isRead,
      'isDelivered': isDelivered,
      'reactions': reactions,
      'replyTo': replyTo,
      'isDeleted': isDeleted,
      'deletedFor': deletedFor,
      'createdAt':
          createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
    };
  }

  // تحويل من ChatMessage إلى FirebaseChatMessage
  static FirebaseChatMessage fromChatMessage(ChatMessage message) {
    return FirebaseChatMessage(
      id: message.objectId,
      text: message.text,
      senderId: message.senderId,
      senderName: message.senderName,
      recipientId: message.recipientId,
      conversationId: message.conversationId,
      messageType: message.messageType,
      mediaUrl: message.mediaFile?.url,
      isRead: message.isRead,
      isDelivered: message.isDelivered,
      reactions: Map<String, dynamic>.from(message.reactions),
      replyTo: message.replyTo,
      isDeleted: message.isDeleted,
      deletedFor: message.deletedFor,
      createdAt: message.createdAt,
    );
  }

  // تحويل إلى ChatMessage
  ChatMessage toChatMessage() {
    final chatMessage = ChatMessage();

    // تعيين البيانات الأساسية
    chatMessage.text = text;
    chatMessage.senderId = senderId;
    chatMessage.senderName = senderName;
    chatMessage.recipientId = recipientId;
    chatMessage.conversationId = conversationId;
    chatMessage.messageType = messageType;
    chatMessage.isRead = isRead;
    chatMessage.isDelivered = isDelivered;
    chatMessage.isDeleted = isDeleted;
    chatMessage.deletedFor = deletedFor;
    chatMessage.replyTo = replyTo;

    // تعيين الوسائط إذا كانت موجودة
    if (mediaUrl != null && mediaUrl!.isNotEmpty) {
      final mediaFile = MediaFile(url: mediaUrl);
      chatMessage.mediaFile = mediaFile;
    }

    // تعيين التفاعلات
    chatMessage.reactions = Map<String, String>.from(
        reactions.map((key, value) => MapEntry(key, value.toString())));

    return chatMessage;
  }
}
