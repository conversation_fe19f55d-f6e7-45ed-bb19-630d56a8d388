import 'package:intl/intl.dart';

/// نموذج لحركة المخزون
class StockMovementModel {
  final String id;
  final String productId;
  final String productName;
  final String warehouseId;
  final String warehouseName;
  final int quantity;
  final String
      type; // "sale", "purchase", "transfer_in", "transfer_out", "adjustment"
  final DateTime date;
  final String referenceId; // رقم الفاتورة أو المستند
  final String notes;

  StockMovementModel({
    required this.id,
    required this.productId,
    required this.productName,
    required this.warehouseId,
    required this.warehouseName,
    required this.quantity,
    required this.type,
    required this.date,
    required this.referenceId,
    this.notes = '',
  });

  /// إنشاء نموذج من بيانات JSON
  factory StockMovementModel.fromJson(Map<String, dynamic> json) {
    return StockMovementModel(
      id: json['id'] ?? '',
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      warehouseId: json['warehouseId'] ?? '',
      warehouseName: json['warehouseName'] ?? '',
      quantity: json['quantity'] ?? 0,
      type: json['type'] ?? '',
      date: json['date'] != null
          ? DateFormat('yyyy-MM-dd').parse(json['date'])
          : DateTime.now(),
      referenceId: json['referenceId'] ?? '',
      notes: json['notes'] ?? '',
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'warehouseId': warehouseId,
      'warehouseName': warehouseName,
      'quantity': quantity,
      'type': type,
      'date': DateFormat('yyyy-MM-dd').format(date),
      'referenceId': referenceId,
      'notes': notes,
    };
  }

  /// الحصول على اسم نوع الحركة بالعربية
  String getTypeInArabic() {
    switch (type) {
      case 'sale':
        return 'بيع';
      case 'purchase':
        return 'شراء';
      case 'transfer_in':
        return 'تحويل وارد';
      case 'transfer_out':
        return 'تحويل صادر';
      case 'adjustment':
        return 'تسوية مخزون';
      case 'initial_stock':
        return 'رصيد بداية المدة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على لون نوع الحركة
  int getTypeColor() {
    switch (type) {
      case 'sale':
        return 0xFFE57373; // أحمر فاتح
      case 'purchase':
        return 0xFF81C784; // أخضر فاتح
      case 'transfer_in':
        return 0xFF64B5F6; // أزرق فاتح
      case 'transfer_out':
        return 0xFFFFB74D; // برتقالي فاتح
      case 'adjustment':
        return 0xFFBA68C8; // بنفسجي فاتح
      default:
        return 0xFF9E9E9E; // رمادي
    }
  }

  /// الحصول على أيقونة نوع الحركة
  String getTypeIcon() {
    switch (type) {
      case 'sale':
        return 'ب'; // بيع
      case 'purchase':
        return 'ش'; // شراء
      case 'transfer_in':
        return 'و'; // وارد
      case 'transfer_out':
        return 'ص'; // صادر
      case 'adjustment':
        return 'ت'; // تسوية
      default:
        return '؟'; // غير معروف
    }
  }
}
