// بسم الله الرحمن الرحيم
// شاشة حركات المخزون - تعرض حركات المخزون للمنتجات

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
// سيتم استبدال هذا لاحقًا بمكون AnimatedLogo
// import '../../../features/splash_screen/widgets/loading_indicator.dart' as new_loading;
import '../models/product_model.dart';
import '../models/stock_movement_model.dart';
import '../services/inventory_service.dart';
import 'product_details_screen.dart';

/// شاشة حركات المخزون
class StockMovementScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة حركات المخزون
  const StockMovementScreen({
    super.key,
    this.productId,
  });

  /// معرف المنتج (اختياري)
  final String? productId;

  @override
  ConsumerState<StockMovementScreen> createState() =>
      _StockMovementScreenState();
}

class _StockMovementScreenState extends ConsumerState<StockMovementScreen> {
  bool _isLoading = false;
  List<StockMovementModel> _stockMovements = [];
  List<ProductModel> _products = [];
  String? _selectedProductId;
  StockMovementType? _selectedType;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _selectedProductId = widget.productId;
    _loadData();
  }

  // تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المنتجات
      final products =
          await ref.read(inventoryServiceProvider).getAllProducts();
      _products = products;

      // تحميل حركات المخزون
      if (_selectedProductId != null) {
        final stockMovements = await ref
            .read(inventoryServiceProvider)
            .getProductStockMovements(_selectedProductId!);
        _stockMovements = _filterMovements(stockMovements);
      } else {
        // في الإصدار الحقيقي، يجب إضافة وظيفة للحصول على جميع حركات المخزون
        _stockMovements = [];
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تصفية حركات المخزون
  List<StockMovementModel> _filterMovements(
      List<StockMovementModel> movements) {
    return movements.where((movement) {
      // تصفية حسب نوع الحركة
      final matchesType =
          _selectedType == null || movement.type == _selectedType;

      // تصفية حسب تاريخ البداية
      final matchesStartDate = _startDate == null ||
          movement.createdAt.isAfter(_startDate!) ||
          movement.createdAt.isAtSameMomentAs(_startDate!);

      // تصفية حسب تاريخ النهاية
      final matchesEndDate = _endDate == null ||
          movement.createdAt.isBefore(_endDate!.add(const Duration(days: 1))) ||
          movement.createdAt.isAtSameMomentAs(_endDate!);

      return matchesType && matchesStartDate && matchesEndDate;
    }).toList();
  }

  // تغيير المنتج المحدد
  void _changeSelectedProduct(String? productId) {
    setState(() {
      _selectedProductId = productId;
    });
    _loadData();
  }

  // تغيير نوع الحركة المحدد
  void _changeSelectedType(StockMovementType? type) {
    setState(() {
      _selectedType = type;
    });
    if (_selectedProductId != null) {
      ref
          .read(inventoryServiceProvider)
          .getProductStockMovements(_selectedProductId!)
          .then((movements) {
        setState(() {
          _stockMovements = _filterMovements(movements);
        });
      });
    }
  }

  // اختيار تاريخ البداية
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
      if (_selectedProductId != null) {
        ref
            .read(inventoryServiceProvider)
            .getProductStockMovements(_selectedProductId!)
            .then((movements) {
          setState(() {
            _stockMovements = _filterMovements(movements);
          });
        });
      }
    }
  }

  // اختيار تاريخ النهاية
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
      if (_selectedProductId != null) {
        ref
            .read(inventoryServiceProvider)
            .getProductStockMovements(_selectedProductId!)
            .then((movements) {
          setState(() {
            _stockMovements = _filterMovements(movements);
          });
        });
      }
    }
  }

  // إعادة تعيين الفلاتر
  void _resetFilters() {
    setState(() {
      _selectedType = null;
      _startDate = null;
      _endDate = null;
    });
    _loadData();
  }

  // فتح تفاصيل المنتج
  void _openProductDetails(String productId) async {
    final product =
        await ref.read(inventoryServiceProvider).getProduct(productId);
    if (product != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProductDetailsScreen(product: product),
        ),
      ).then((_) => _loadData());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حركات المخزون'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                builder: (context) => _buildFilterSheet(),
              );
            },
            tooltip: 'فلترة',
          ),
        ],
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل البيانات...',
            )
          // سيتم استبدال هذا لاحقًا بمكون AnimatedLogo
          /*
          ? const new_loading.FullScreenLoadingIndicator(
              type: new_loading.LoadingIndicatorType.logo,
              message: 'جاري تحميل البيانات...',
            )
          */
          : Column(
              children: [
                // اختيار المنتج
                if (widget.productId == null)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: DropdownButtonFormField<String?>(
                      decoration: const InputDecoration(
                        labelText: 'اختر المنتج',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.inventory),
                      ),
                      value: _selectedProductId,
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('جميع المنتجات'),
                        ),
                        ..._products.map((product) {
                          return DropdownMenuItem<String>(
                            value: product.id,
                            child: Text(product.name),
                          );
                        }),
                      ],
                      onChanged: _changeSelectedProduct,
                    ),
                  ),

                // الفلاتر النشطة
                if (_selectedType != null ||
                    _startDate != null ||
                    _endDate != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Wrap(
                      spacing: 8,
                      children: [
                        if (_selectedType != null)
                          Chip(
                            label:
                                Text(_getStockMovementTypeText(_selectedType!)),
                            deleteIcon: const Icon(Icons.close, size: 16),
                            onDeleted: () {
                              setState(() {
                                _selectedType = null;
                              });
                              _loadData();
                            },
                          ),
                        if (_startDate != null)
                          Chip(
                            label: Text(
                                'من: ${DateFormat('yyyy-MM-dd').format(_startDate!)}'),
                            deleteIcon: const Icon(Icons.close, size: 16),
                            onDeleted: () {
                              setState(() {
                                _startDate = null;
                              });
                              _loadData();
                            },
                          ),
                        if (_endDate != null)
                          Chip(
                            label: Text(
                                'إلى: ${DateFormat('yyyy-MM-dd').format(_endDate!)}'),
                            deleteIcon: const Icon(Icons.close, size: 16),
                            onDeleted: () {
                              setState(() {
                                _endDate = null;
                              });
                              _loadData();
                            },
                          ),
                        ActionChip(
                          label: const Text('إعادة تعيين'),
                          onPressed: _resetFilters,
                        ),
                      ],
                    ),
                  ),

                // قائمة حركات المخزون
                Expanded(
                  child: _selectedProductId == null
                      ? const Center(
                          child: Text('يرجى اختيار منتج لعرض حركات المخزون'),
                        )
                      : _stockMovements.isEmpty
                          ? const Center(
                              child: Text('لا توجد حركات مخزون'),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.all(16.0),
                              itemCount: _stockMovements.length,
                              itemBuilder: (context, index) {
                                final movement = _stockMovements[index];
                                return Card(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            _getStockMovementIcon(
                                                movement.type),
                                            const SizedBox(width: 8),
                                            Text(
                                              _getStockMovementTypeText(
                                                  movement.type),
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const Spacer(),
                                            Text(
                                              DateFormat('yyyy-MM-dd HH:mm')
                                                  .format(movement.createdAt),
                                              style: const TextStyle(
                                                color: AppColors.greyTextColor,
                                                fontSize: 12,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const Divider(),
                                        _buildInfoRow(
                                            'المنتج',
                                            _getProductName(
                                                movement.productId)),
                                        _buildInfoRow('الكمية',
                                            movement.quantity.toString()),
                                        if (movement.previousQuantity != null)
                                          _buildInfoRow(
                                              'الكمية السابقة',
                                              movement.previousQuantity
                                                  .toString()),
                                        if (movement.newQuantity != null)
                                          _buildInfoRow('الكمية الجديدة',
                                              movement.newQuantity.toString()),
                                        if (movement.notes != null &&
                                            movement.notes!.isNotEmpty)
                                          _buildInfoRow(
                                              'ملاحظات', movement.notes!),
                                        if (movement.referenceId != null)
                                          _buildInfoRow('رقم المرجع',
                                              movement.referenceId!),
                                        if (movement.referenceType != null)
                                          _buildInfoRow('نوع المرجع',
                                              movement.referenceType!),
                                        if (movement.fromLocationId != null)
                                          _buildInfoRow('من الموقع',
                                              movement.fromLocationId!),
                                        if (movement.toLocationId != null)
                                          _buildInfoRow('إلى الموقع',
                                              movement.toLocationId!),
                                        if (movement.createdBy != null)
                                          _buildInfoRow(
                                              'بواسطة', movement.createdBy!),

                                        // زر عرض المنتج
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: TextButton(
                                            onPressed: () =>
                                                _openProductDetails(
                                                    movement.productId),
                                            child: const Text('عرض المنتج'),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
              ],
            ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.greyTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء شاشة الفلترة
  Widget _buildFilterSheet() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فلترة حركات المخزون',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // نوع الحركة
          DropdownButtonFormField<StockMovementType?>(
            decoration: const InputDecoration(
              labelText: 'نوع الحركة',
              border: OutlineInputBorder(),
            ),
            value: _selectedType,
            items: [
              const DropdownMenuItem<StockMovementType?>(
                value: null,
                child: Text('جميع الأنواع'),
              ),
              ...StockMovementType.values.map((type) {
                return DropdownMenuItem<StockMovementType>(
                  value: type,
                  child: Text(_getStockMovementTypeText(type)),
                );
              }),
            ],
            onChanged: (value) {
              Navigator.pop(context);
              _changeSelectedType(value);
            },
          ),
          const SizedBox(height: 16),

          // تاريخ البداية
          InkWell(
            onTap: () {
              Navigator.pop(context);
              _selectStartDate(context);
            },
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'تاريخ البداية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.calendar_today),
              ),
              child: Text(
                _startDate != null
                    ? DateFormat('yyyy-MM-dd').format(_startDate!)
                    : 'اختر التاريخ',
              ),
            ),
          ),
          const SizedBox(height: 16),

          // تاريخ النهاية
          InkWell(
            onTap: () {
              Navigator.pop(context);
              _selectEndDate(context);
            },
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'تاريخ النهاية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.calendar_today),
              ),
              child: Text(
                _endDate != null
                    ? DateFormat('yyyy-MM-dd').format(_endDate!)
                    : 'اختر التاريخ',
              ),
            ),
          ),
          const SizedBox(height: 16),

          // زر إعادة تعيين الفلاتر
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _resetFilters();
              },
              child: const Text('إعادة تعيين الفلاتر'),
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على اسم المنتج
  String _getProductName(String productId) {
    final product = _products.firstWhere(
      (product) => product.id == productId,
      orElse: () => ProductModel(
        id: productId,
        name: 'منتج غير معروف',
        barcode: '',
        categoryId: '',
        price: 0,
        cost: 0,
        quantity: 0,
        createdAt: DateTime.now(),
      ),
    );
    return product.name;
  }

  // الحصول على أيقونة حركة المخزون
  Widget _getStockMovementIcon(StockMovementType type) {
    IconData icon;
    Color color;

    switch (type) {
      case StockMovementType.addition:
        icon = Icons.add_circle;
        color = Colors.green;
        break;
      case StockMovementType.withdrawal:
        icon = Icons.remove_circle;
        color = Colors.red;
        break;
      case StockMovementType.adjustment:
        icon = Icons.edit;
        color = Colors.blue;
        break;
      case StockMovementType.transfer:
        icon = Icons.swap_horiz;
        color = Colors.purple;
        break;
      case StockMovementType.sale:
        icon = Icons.shopping_cart;
        color = Colors.orange;
        break;
      case StockMovementType.return_:
        icon = Icons.assignment_return;
        color = Colors.teal;
        break;
      case StockMovementType.damaged:
        icon = Icons.dangerous;
        color = Colors.red;
        break;
      case StockMovementType.inventory:
        icon = Icons.inventory;
        color = Colors.blue;
        break;
    }

    return Icon(
      icon,
      color: color,
      size: 24,
    );
  }

  // الحصول على نص نوع حركة المخزون
  String _getStockMovementTypeText(StockMovementType type) {
    switch (type) {
      case StockMovementType.addition:
        return 'إضافة مخزون';
      case StockMovementType.withdrawal:
        return 'سحب مخزون';
      case StockMovementType.adjustment:
        return 'تعديل مخزون';
      case StockMovementType.transfer:
        return 'نقل مخزون';
      case StockMovementType.sale:
        return 'بيع';
      case StockMovementType.return_:
        return 'مرتجع';
      case StockMovementType.damaged:
        return 'تالف';
      case StockMovementType.inventory:
        return 'جرد';
    }
  }
}
