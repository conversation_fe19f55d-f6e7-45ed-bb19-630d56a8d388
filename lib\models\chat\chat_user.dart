/// نموذج مستخدم الدردشة
class ChatUser {
  String? objectId;
  String userId;
  String name;
  String email;
  String profileImage;
  String deviceToken;
  DateTime lastSeen;
  bool isOnline;
  String phone;
  String role;

  // أسماء الحقول (للتوافق مع الكود القديم)
  static const String keyUserId = 'userId';
  static const String keyName = 'name';
  static const String keyEmail = 'email';
  static const String keyProfileImage = 'profileImage';
  static const String keyDeviceToken = 'deviceToken';
  static const String keyLastSeen = 'lastSeen';
  static const String keyIsOnline = 'isOnline';
  static const String keyPhone = 'phone';
  static const String keyRole = 'role';

  // المُنشئ
  ChatUser({
    this.objectId,
    required this.userId,
    this.name = '',
    this.email = '',
    this.profileImage = '',
    this.deviceToken = '',
    DateTime? lastSeen,
    this.isOnline = false,
    this.phone = '',
    this.role = 'user',
  }) : lastSeen = lastSeen ?? DateTime.now();

  // إنشاء نسخة من الكائن
  ChatUser clone(Map<String, dynamic> map) {
    return ChatUser.fromMap(map);
  }

  // دوال مساعدة للتوافق مع الكود القديم

  // الحصول على قيمة من الخريطة
  T? get<T>(String key) {
    switch (key) {
      case keyUserId:
        return userId as T?;
      case keyName:
        return name as T?;
      case keyEmail:
        return email as T?;
      case keyProfileImage:
        return profileImage as T?;
      case keyDeviceToken:
        return deviceToken as T?;
      case keyLastSeen:
        return lastSeen as T?;
      case keyIsOnline:
        return isOnline as T?;
      case keyPhone:
        return phone as T?;
      case keyRole:
        return role as T?;
      default:
        return null;
    }
  }

  // تعيين قيمة في الخريطة
  void set<T>(String key, T? value) {
    switch (key) {
      case keyUserId:
        userId = value as String;
        break;
      case keyName:
        name = value as String;
        break;
      case keyEmail:
        email = value as String;
        break;
      case keyProfileImage:
        profileImage = value as String;
        break;
      case keyDeviceToken:
        deviceToken = value as String;
        break;
      case keyLastSeen:
        lastSeen = value as DateTime;
        break;
      case keyIsOnline:
        isOnline = value as bool;
        break;
      case keyPhone:
        phone = value as String;
        break;
      case keyRole:
        role = value as String;
        break;
      default:
        break;
    }
  }

  // تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'objectId': objectId,
      'userId': userId,
      'name': name,
      'email': email,
      'profileImage': profileImage,
      'deviceToken': deviceToken,
      'lastSeen': lastSeen.toIso8601String(),
      'isOnline': isOnline,
      'phone': phone,
      'role': role,
    };
  }

  // إنشاء نموذج من Map
  factory ChatUser.fromMap(Map<String, dynamic> map) {
    return ChatUser(
      objectId: map['objectId'],
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      profileImage: map['profileImage'] ?? '',
      deviceToken: map['deviceToken'] ?? '',
      lastSeen: map['lastSeen'] != null
          ? DateTime.parse(map['lastSeen'])
          : DateTime.now(),
      isOnline: map['isOnline'] ?? false,
      phone: map['phone'] ?? '',
      role: map['role'] ?? 'user',
    );
  }

  // تحويل النموذج إلى JSON
  Map<String, dynamic> fromJson(Map<String, dynamic> map) {
    objectId = map['objectId'];
    userId = map['userId'] ?? '';
    name = map['name'] ?? '';
    email = map['email'] ?? '';
    profileImage = map['profileImage'] ?? '';
    deviceToken = map['deviceToken'] ?? '';
    lastSeen = map['lastSeen'] != null
        ? DateTime.parse(map['lastSeen'])
        : DateTime.now();
    isOnline = map['isOnline'] ?? false;
    phone = map['phone'] ?? '';
    role = map['role'] ?? 'user';

    return map;
  }

  // حفظ المستخدم (للتوافق مع الكود القديم)
  Future<ParseResponse> save() async {
    // هذه الدالة للتوافق فقط، لا تقوم بأي شيء
    return ParseResponse(
      success: true,
      results: [this],
      statusCode: 200,
      error: null,
    );
  }
}

// فئة ParseResponse للتوافق مع الكود القديم
class ParseResponse {
  final bool success;
  final List<dynamic>? results;
  final int statusCode;
  final ParseError? error;

  ParseResponse({
    required this.success,
    this.results,
    required this.statusCode,
    this.error,
  });
}

// فئة ParseError للتوافق مع الكود القديم
class ParseError {
  final String message;
  final int code;

  ParseError({
    required this.message,
    required this.code,
  });
}

// فئة QueryBuilder للتوافق مع الكود القديم
class QueryBuilder<T> {
  final T _defaultInstance;
  final Map<String, dynamic> _whereConditions = {};
  final List<String> _orderBy = [];

  QueryBuilder(this._defaultInstance);

  // إضافة شرط تساوي
  QueryBuilder<T> whereEqualTo(String key, dynamic value) {
    _whereConditions['${key}_equals'] = value;
    return this;
  }

  // إضافة شرط عدم تساوي
  QueryBuilder<T> whereNotEqualTo(String key, dynamic value) {
    _whereConditions['${key}_not_equals'] = value;
    return this;
  }

  // إضافة شرط احتواء
  QueryBuilder<T> whereContains(String key, String value) {
    _whereConditions['${key}_contains'] = value;
    return this;
  }

  // إضافة شرط احتواء في قائمة
  QueryBuilder<T> whereContainedIn(String key, List<dynamic> values) {
    _whereConditions['${key}_in'] = values;
    return this;
  }

  // ترتيب تصاعدي
  QueryBuilder<T> orderByAscending(String key) {
    _orderBy.add('${key}_asc');
    return this;
  }

  // ترتيب تنازلي
  QueryBuilder<T> orderByDescending(String key) {
    _orderBy.add('${key}_desc');
    return this;
  }

  // تنفيذ الاستعلام
  Future<ParseResponse> query() async {
    // هذه الدالة للتوافق فقط، تعيد قائمة فارغة
    if (_defaultInstance is ChatUser) {
      // إنشاء مستخدم افتراضي للتوافق مع الكود القديم
      final user = ChatUser(userId: 'dummy_user_id');
      return ParseResponse(
        success: true,
        results: [user],
        statusCode: 200,
        error: null,
      );
    }
    return ParseResponse(
      success: true,
      results: [],
      statusCode: 200,
      error: null,
    );
  }
}
