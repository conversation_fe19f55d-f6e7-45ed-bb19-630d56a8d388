import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/paginated_data_model.dart';

class PaginatedDataNotifier<T> extends StateNotifier<PaginatedData<T>> {
  final Future<List<T>> Function(int page, int limit) fetchData;
  final int pageSize;
  bool _isLoading = false;
  String _searchQuery = '';
  String _filterType = 'الكل';
  
  PaginatedDataNotifier({
    required this.fetchData,
    required this.pageSize,
  }) : super(PaginatedData<T>(items: [], hasMore: true, page: 0)) {
    loadInitialData();
  }
  
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  String get filterType => _filterType;
  
  Future<void> loadInitialData() async {
    if (_isLoading) return;
    
    _isLoading = true;
    try {
      final items = await fetchData(1, pageSize);
      
      state = PaginatedData<T>(
        items: items,
        hasMore: items.length >= pageSize,
        page: 1,
        totalItems: items.length,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات الأولية: $e');
    } finally {
      _isLoading = false;
    }
  }
  
  Future<void> loadMore() async {
    if (_isLoading || !state.hasMore || _searchQuery.isNotEmpty) return;
    
    _isLoading = true;
    try {
      final nextPage = state.page + 1;
      final newItems = await fetchData(nextPage, pageSize);
      
      state = state.copyWith(
        items: [...state.items, ...newItems],
        hasMore: newItems.length >= pageSize,
        page: nextPage,
        totalItems: state.totalItems + newItems.length,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المزيد من البيانات: $e');
    } finally {
      _isLoading = false;
    }
  }
  
  Future<void> refresh() async {
    _searchQuery = '';
    _filterType = 'الكل';
    state = PaginatedData<T>(items: [], hasMore: true, page: 0);
    await loadInitialData();
  }
  
  void setSearchQuery(String query) {
    _searchQuery = query;
  }
  
  void setFilterType(String type) {
    _filterType = type;
  }
  
  // يمكن استخدام هذه الدالة لتصفية البيانات محليًا بناءً على معايير البحث والتصفية
  List<T> getFilteredItems(bool Function(T item) filterFunction) {
    return state.items.where(filterFunction).toList();
  }
}
