import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:intl/intl.dart';

import '../currency.dart';
import '../model/product_model.dart';

class ProductRepo {
  Future<List<ProductModel>> getAllProduct() async {
    List<ProductModel> productList = [];
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('Products')
        .orderByKey()
        .get()
        .then((value) {
      for (var element in value.children) {
        productList
            .add(ProductModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    final productsRef =
        FirebaseDatabase.instance.ref(constUserId).child('Products');
    productsRef.keepSynced(true);
    return productList;
  }

  Future<Map<String, int>> getStockMovements(
      String productId, DateTime fromDate, DateTime toDate) async {
    int totalSales = 0;
    int totalPurchases = 0;
    int totalSalesReturns = 0;
    int totalPurchaseReturns = 0;

    // Get sales transactions
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('Sales')
        .orderByChild('date')
        .startAt(DateFormat('yyyy-MM-dd').format(fromDate))
        .endAt(DateFormat('yyyy-MM-dd').format(toDate))
        .get()
        .then((value) {
      for (var element in value.children) {
        var saleData = jsonDecode(jsonEncode(element.value));
        var products = List<Map<String, dynamic>>.from(saleData['productList']);
        for (var product in products) {
          if (product['productId'] == productId) {
            totalSales += int.parse(product['quantity'].toString());
          }
        }
      }
    });

    // Get purchase transactions
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('Purchase')
        .orderByChild('date')
        .startAt(DateFormat('yyyy-MM-dd').format(fromDate))
        .endAt(DateFormat('yyyy-MM-dd').format(toDate))
        .get()
        .then((value) {
      for (var element in value.children) {
        var purchaseData = jsonDecode(jsonEncode(element.value));
        var products =
            List<Map<String, dynamic>>.from(purchaseData['productList']);
        for (var product in products) {
          if (product['productId'] == productId) {
            totalPurchases += int.parse(product['quantity'].toString());
          }
        }
      }
    });

    // Get sales returns
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('SalesReturn')
        .orderByChild('date')
        .startAt(DateFormat('yyyy-MM-dd').format(fromDate))
        .endAt(DateFormat('yyyy-MM-dd').format(toDate))
        .get()
        .then((value) {
      for (var element in value.children) {
        var returnData = jsonDecode(jsonEncode(element.value));
        var products =
            List<Map<String, dynamic>>.from(returnData['productList']);
        for (var product in products) {
          if (product['productId'] == productId) {
            totalSalesReturns += int.parse(product['quantity'].toString());
          }
        }
      }
    });

    // Get purchase returns
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('PurchaseReturn')
        .orderByChild('date')
        .startAt(DateFormat('yyyy-MM-dd').format(fromDate))
        .endAt(DateFormat('yyyy-MM-dd').format(toDate))
        .get()
        .then((value) {
      for (var element in value.children) {
        var returnData = jsonDecode(jsonEncode(element.value));
        var products =
            List<Map<String, dynamic>>.from(returnData['productList']);
        for (var product in products) {
          if (product['productId'] == productId) {
            totalPurchaseReturns += int.parse(product['quantity'].toString());
          }
        }
      }
    });

    return {
      'sales': totalSales,
      'purchases': totalPurchases,
      'salesReturns': totalSalesReturns,
      'purchaseReturns': totalPurchaseReturns,
    };
  }

  Future<int> getStockAtDate(
      String productId, DateTime date, int currentStock) async {
    var movements = await getStockMovements(productId, date, DateTime.now());
    // Current stock - (purchases - purchase returns) + (sales - sales returns)
    return currentStock -
        (movements['purchases']! - movements['purchaseReturns']!) +
        (movements['sales']! - movements['salesReturns']!);
  }
}
