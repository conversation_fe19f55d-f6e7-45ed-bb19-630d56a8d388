import 'package:mobile_pos/model/notification_model.dart';
import 'package:mobile_pos/models/system_notification_model.dart';

/// نموذج موحد للإشعارات
class UnifiedNotificationModel {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic> data;
  final String? senderName;
  final String type;
  final bool isSystemNotification;

  UnifiedNotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.isRead,
    required this.data,
    this.senderName,
    required this.type,
    required this.isSystemNotification,
  });

  /// إنشاء نموذج موحد من إشعار محلي
  factory UnifiedNotificationModel.fromLocalNotification(
      NotificationModel notification) {
    return UnifiedNotificationModel(
      id: notification.id,
      title: notification.title,
      message: notification.body,
      timestamp: notification.timestamp,
      isRead: notification.isRead,
      data: notification.data,
      type: notification.data['type'] as String? ?? 'general',
      isSystemNotification: false,
    );
  }

  /// إنشاء نموذج موحد من إشعار نظام
  factory UnifiedNotificationModel.fromSystemNotification(
      SystemNotificationModel notification) {
    return UnifiedNotificationModel(
      id: notification.id,
      title: notification.title,
      message: notification.message,
      timestamp: notification.timestamp,
      isRead: notification.isRead,
      data: notification.data,
      senderName: notification.senderName,
      type: notification.type,
      isSystemNotification: true,
    );
  }

  /// نسخ النموذج مع تعديل بعض الخصائص
  UnifiedNotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? data,
    String? senderName,
    String? type,
    bool? isSystemNotification,
  }) {
    return UnifiedNotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      senderName: senderName ?? this.senderName,
      type: type ?? this.type,
      isSystemNotification: isSystemNotification ?? this.isSystemNotification,
    );
  }
}
