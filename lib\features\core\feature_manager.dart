// بسم الله الرحمن الرحيم
// مدير الميزات - المسؤول عن إدارة جميع الميزات في النظام

import 'package:flutter/material.dart';
import 'feature_interface.dart';

/// مدير الميزات - المسؤول عن إدارة جميع الميزات في النظام
class FeatureManager {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من مدير الميزات
  static final FeatureManager _instance = FeatureManager._internal();
  factory FeatureManager() => _instance;
  FeatureManager._internal();

  // قائمة الميزات المسجلة
  final Map<String, FeatureInterface> _features = {};

  // حالة التهيئة
  bool _initialized = false;

  /// تسجيل ميزة جديدة
  void registerFeature(FeatureInterface feature) {
    _features[feature.featureName] = feature;
  }

  /// إلغاء تسجيل ميزة
  void unregisterFeature(String featureName) {
    _features.remove(featureName);
  }

  /// الحصول على ميزة بالاسم
  FeatureInterface? getFeature(String featureName) {
    return _features[featureName];
  }

  /// الحصول على جميع الميزات المسجلة
  List<FeatureInterface> getAllFeatures() {
    return _features.values.toList();
  }

  /// الحصول على جميع الميزات المفعلة
  List<FeatureInterface> getEnabledFeatures() {
    return _features.values.where((feature) => feature.isEnabled).toList();
  }

  /// تهيئة جميع الميزات المفعلة
  Future<void> initialize() async {
    if (_initialized) return;

    for (var feature in getEnabledFeatures()) {
      try {
        await feature.initialize();
      } catch (e) {
        debugPrint('خطأ في تهيئة الميزة ${feature.featureName}: $e');
        // استمر في تهيئة الميزات الأخرى حتى مع وجود خطأ
      }
    }

    _initialized = true;
  }

  /// إيقاف جميع الميزات
  Future<void> dispose() async {
    for (var feature in _features.values) {
      try {
        await feature.dispose();
      } catch (e) {
        debugPrint('خطأ في إيقاف الميزة ${feature.featureName}: $e');
        // استمر في إيقاف الميزات الأخرى حتى مع وجود خطأ
      }
    }
    _initialized = false;
  }

  /// الحصول على جميع مسارات الميزات المفعلة
  Map<String, WidgetBuilder> getAllRoutes() {
    final Map<String, WidgetBuilder> routes = {};

    for (var feature in getEnabledFeatures()) {
      try {
        routes.addAll(feature.getRoutes());
      } catch (e) {
        debugPrint(
            'خطأ في الحصول على مسارات الميزة ${feature.featureName}: $e');
        // استمر في الحصول على مسارات الميزات الأخرى حتى مع وجود خطأ
      }
    }

    return routes;
  }
}
