// // ignore_for_file: unused_import, prefer_final_fields

// import 'package:flutter/material.dart';
// import 'package:mobile_pos/model/product_model.dart';
// import 'package:provider/provider.dart';

// class ProductReportProvider with ChangeNotifier {
//   List<ProductModel> _productReports = [];

//   List<ProductModel> get productReports => _productReports;

//   void fetchProductReports() {
//     // هنا يمكنك إضافة الكود لجلب بيانات تقرير الأصناف
//     notifyListeners();
//   }

//   void addProductReport(ProductModel product) {
//     _productReports.add(product);
//     notifyListeners();
//   }
// }
