import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/sms_template_model.dart';
import 'package:mobile_pos/repository/sms_template_repo.dart';

// Provider for SMS template repository
final smsTemplateRepositoryProvider = Provider<SmsTemplateRepo>((ref) {
  return SmsTemplateRepo();
});

// Provider for SMS templates
final smsTemplateProvider = StreamProvider<SmsTemplateModel>((ref) {
  final smsTemplateRepo = ref.watch(smsTemplateRepositoryProvider);
  return smsTemplateRepo.getSmsTemplates();
});

// Provider for SMS template notifier
final smsTemplateNotifierProvider =
    StateNotifierProvider<SmsTemplateNotifier, AsyncValue<void>>((ref) {
  final smsTemplateRepo = ref.watch(smsTemplateRepositoryProvider);
  return SmsTemplateNotifier(smsTemplateRepo);
});

// Notifier for SMS template operations
class SmsTemplateNotifier extends StateNotifier<AsyncValue<void>> {
  final SmsTemplateRepo _smsTemplateRepo;

  SmsTemplateNotifier(this._smsTemplateRepo)
      : super(const AsyncValue.data(null));

  // Save SMS templates
  Future<void> saveSmsTemplates(SmsTemplateModel templates) async {
    state = const AsyncValue.loading();
    try {
      await _smsTemplateRepo.saveSmsTemplates(templates);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
