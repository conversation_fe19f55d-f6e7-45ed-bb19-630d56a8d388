<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
    </domain-config>

    <!-- إعدادات عامة للأمان مع دعم الصور -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </base-config>

    <!-- دعم خاص لخدمات الصور المفضلة -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">imgur.com</domain>
        <domain includeSubdomains="true">i.imgur.com</domain>
        <domain includeSubdomains="true">gofile.io</domain>
        <domain includeSubdomains="true">store1.gofile.io</domain>
        <domain includeSubdomains="true">store2.gofile.io</domain>
        <domain includeSubdomains="true">store3.gofile.io</domain>
        <domain includeSubdomains="true">store4.gofile.io</domain>
        <domain includeSubdomains="true">store5.gofile.io</domain>
        <domain includeSubdomains="true">images.unsplash.com</domain>
        <domain includeSubdomains="true">via.placeholder.com</domain>
    </domain-config>
</network-security-config>
