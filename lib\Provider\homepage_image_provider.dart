import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../model/homepage_image_model.dart';
import '../repository/home_page_images.dart';

// استخدام مزود واحد للمستودع
final homePageImageRepoProvider = Provider<HomePageImageRepo>((ref) {
  return HomePageImageRepo();
});

// استخدام FutureProvider.autoDispose لتجنب تخزين البيانات القديمة
final homepageImageProvider =
    FutureProvider.autoDispose<List<HomePageImageModel>>((ref) async {
  // الحصول على المستودع من المزود
  final repo = ref.watch(homePageImageRepoProvider);

  try {
    // تعيين مهلة زمنية للحصول على البيانات
    return await repo.getAllHomePageImage().timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        debugPrint('انتهت مهلة الحصول على صور الصفحة الرئيسية في المزود');
        throw TimeoutException('انتهت مهلة الحصول على صور الصفحة الرئيسية');
      },
    );
  } catch (e) {
    debugPrint('خطأ في مزود صور الصفحة الرئيسية: $e');
    // إعادة قائمة فارغة في حالة الخطأ
    return [];
  }
});
