class DeliveryTripModel {
  late String id;
  late String tripDate;
  late String startTime;
  late String endTime;
  late String vehicleId;
  late String vehicleName;
  late String driverId;
  late String driverName;
  late String startMeterReading;
  late String endMeterReading;
  late String distanceTraveled;
  late String fuelConsumption;
  late String fuelCost;
  late String oilCost;
  late String maintenanceCost;
  late String driverSalary;
  late String totalCost;
  late String customerId;
  late String customerName;
  late String deliveryAddress;
  late String invoiceNumber;
  late String status; // 'active', 'completed', 'cancelled'
  late String notes;
  late String createdBy;
  late String createdAt;
  late String updatedAt;

  DeliveryTripModel({
    required this.id,
    required this.tripDate,
    required this.startTime,
    required this.endTime,
    required this.vehicleId,
    required this.vehicleName,
    required this.driverId,
    required this.driverName,
    required this.startMeterReading,
    required this.endMeterReading,
    required this.distanceTraveled,
    required this.fuelConsumption,
    required this.fuelCost,
    required this.oilCost,
    required this.maintenanceCost,
    required this.driverSalary,
    required this.totalCost,
    required this.customerId,
    required this.customerName,
    required this.deliveryAddress,
    required this.invoiceNumber,
    required this.status,
    required this.notes,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  DeliveryTripModel.fromJson(Map<dynamic, dynamic> json) {
    id = json['id']?.toString() ?? '';
    tripDate = json['tripDate']?.toString() ?? '';
    startTime = json['startTime']?.toString() ?? '';
    endTime = json['endTime']?.toString() ?? '';
    vehicleId = json['vehicleId']?.toString() ?? '';
    vehicleName = json['vehicleName']?.toString() ?? '';
    driverId = json['driverId']?.toString() ?? '';
    driverName = json['driverName']?.toString() ?? '';
    startMeterReading = json['startMeterReading']?.toString() ?? '';
    endMeterReading = json['endMeterReading']?.toString() ?? '';
    distanceTraveled = json['distanceTraveled']?.toString() ?? '';
    fuelConsumption = json['fuelConsumption']?.toString() ?? '';
    fuelCost = json['fuelCost']?.toString() ?? '';
    oilCost = json['oilCost']?.toString() ?? '';
    maintenanceCost = json['maintenanceCost']?.toString() ?? '';
    driverSalary = json['driverSalary']?.toString() ?? '';
    totalCost = json['totalCost']?.toString() ?? '';
    customerId = json['customerId']?.toString() ?? '';
    customerName = json['customerName']?.toString() ?? '';
    deliveryAddress = json['deliveryAddress']?.toString() ?? '';
    invoiceNumber = json['invoiceNumber']?.toString() ?? '';
    status = json['status']?.toString() ?? '';
    notes = json['notes']?.toString() ?? '';
    createdBy = json['createdBy']?.toString() ?? '';
    createdAt = json['createdAt']?.toString() ?? '';
    updatedAt = json['updatedAt']?.toString() ?? '';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'id': id,
        'tripDate': tripDate,
        'startTime': startTime,
        'endTime': endTime,
        'vehicleId': vehicleId,
        'vehicleName': vehicleName,
        'driverId': driverId,
        'driverName': driverName,
        'startMeterReading': startMeterReading,
        'endMeterReading': endMeterReading,
        'distanceTraveled': distanceTraveled,
        'fuelConsumption': fuelConsumption,
        'fuelCost': fuelCost,
        'oilCost': oilCost,
        'maintenanceCost': maintenanceCost,
        'driverSalary': driverSalary,
        'totalCost': totalCost,
        'customerId': customerId,
        'customerName': customerName,
        'deliveryAddress': deliveryAddress,
        'invoiceNumber': invoiceNumber,
        'status': status,
        'notes': notes,
        'createdBy': createdBy,
        'createdAt': createdAt,
        'updatedAt': updatedAt,
      };
}
