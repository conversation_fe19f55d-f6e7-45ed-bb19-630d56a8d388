// import 'package:flutter_riverpod/flutter_riverpod.dart';

// import 'package:mobile_pos/Provider/purchase_report_provider.dart';
// import 'package:mobile_pos/Provider/seles_report_provider.dart';

// import '../model/product_movement.dart';

// class ProductMovementNotifier extends StateNotifier<List<ProductMovement>> {
//   final Ref ref;
//   final String productCode;
//   ProductMovementNotifier() : super([]);

//   void fetchProductMovements() {
//     final salesData = ref.watch(salesReportProvider);
//     final purchaseData = ref.watch(purchaseReportProvider);

//     List<ProductMovement> movements = [];

//     salesData.whenData((sales) {
//       for (var sale in sales) {
//         if (sale.productList != null) {
//           for (var product in sale.productList!) {
//             if (product.productCode == productCode) {
//               movements.add(
//                 ProductMovement(
//                   productCode: product.productCode ?? '',
//                   productName: product.productName ?? '',
//                   date: sale.purchaseDate != null
//                       ? DateTime.parse(sale.purchaseDate!)
//                       : DateTime.now(),
//                   type: 'sale',
//                   quantity: product.productStock != null
//                       ? int.tryParse(product.productStock!) ?? 0
//                       : 0,
//                   price: product.productSalePrice != null
//                       ? double.tryParse(product.productSalePrice!) ?? 0.0
//                       : 0.0,
//                   invoiceNumber: sale.invoiceNumber ?? '',
//                 ),
//               );
//             }
//           }
//         }
//       }
//     });
//     purchaseData.whenData((purchases) {
//       for (var purchase in purchases) {
//         // تأكد من وجود الخصائص قبل استخدامها
//         if (purchase.productList != null) {
//           for (var product in purchase.productList!) {
//             if (product.productCode == productCode) {
//               movements.add(
//                 ProductMovement(
//                   productCode: product.productCode ?? '',
//                   productName: product.productName ?? '',
//                   date: purchase.purchaseDate != null
//                       ? DateTime.parse(purchase.purchaseDate!)
//                       : DateTime.now(),
//                   type: 'purchase',
//                   quantity: product.productStock != null
//                       ? int.tryParse(product.productStock!) ?? 0
//                       : 0,
//                   price: product.productPurchasePrice != null
//                       ? double.tryParse(product.productPurchasePrice!) ?? 0.0
//                       : 0.0,
//                   invoiceNumber: purchase.invoiceNumber ?? '',
//                 ),
//               );
//             }
//           }
//         }
//       }
//     });

//     movements.sort((a, b) => b.date.compareTo(a.date));
//     state = movements; // تحديث الحالة
//   }
// }

// final productMovementProvider = StateNotifierProvider.family<
//     ProductMovementNotifier, List<ProductMovement>, String>((ref, productCode) {
//   return ProductMovementNotifier(ref, productCode);
// });
