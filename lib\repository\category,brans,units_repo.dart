// ignore_for_file: file_names

import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/GlobalComponents/Model/category_model.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';

import '../Screens/Products/Model/brands_model.dart';
import '../Screens/Products/Model/unit_model.dart';
import '../currency.dart';

class CategoryRepo {
  Future<List<CategoryModel>> getAllCategory() async {
    List<CategoryModel> categoryList = [];
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('Categories')
        .orderByKey()
        .get()
        .then((value) {
      for (var element in value.children) {
        categoryList
            .add(CategoryModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    // Mantener la referencia sincronizada para futuras operaciones
    FirebaseDatabaseService.getReference('$constUserId/Categories',
        keepSynced: true);
    return categoryList;
  }
}

class BrandsRepo {
  Future<List<BrandsModel>> getAllBrand() async {
    List<BrandsModel> brandsList = [];
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('Brands')
        .orderByKey()
        .get()
        .then((value) {
      for (var element in value.children) {
        brandsList
            .add(BrandsModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    // Mantener la referencia sincronizada para futuras operaciones
    FirebaseDatabaseService.getReference('$constUserId/Brands',
        keepSynced: true);
    return brandsList;
  }
}

class UnitsRepo {
  Future<List<UnitModel>> getAllUnits() async {
    List<UnitModel> unitsList = [];
    await FirebaseDatabase.instance
        .ref(constUserId)
        .child('Units')
        .orderByKey()
        .get()
        .then((value) {
      for (var element in value.children) {
        unitsList
            .add(UnitModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    // Mantener la referencia sincronizada para futuras operaciones
    FirebaseDatabaseService.getReference('$constUserId/Units',
        keepSynced: true);
    return unitsList;
  }
}
