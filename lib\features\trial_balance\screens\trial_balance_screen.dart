// بسم الله الرحمن الرحيم
// شاشة تقرير Trial Balance

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../constant.dart';
import '../models/trial_balance_model.dart';
import '../providers/trial_balance_provider.dart';

class TrialBalanceScreen extends ConsumerStatefulWidget {
  const TrialBalanceScreen({super.key});

  @override
  ConsumerState<TrialBalanceScreen> createState() => _TrialBalanceScreenState();
}

class _TrialBalanceScreenState extends ConsumerState<TrialBalanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _titleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _titleController.text =
        'تقرير ميزان المراجعة - ${DateFormat('yyyy/MM/dd').format(DateTime.now())}';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(trialBalanceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير ميزان المراجعة'),
        backgroundColor: kMainColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'إنشاء تقرير', icon: Icon(Icons.add_chart)),
            Tab(text: 'التقرير الحالي', icon: Icon(Icons.table_chart)),
            Tab(text: 'التقارير المحفوظة', icon: Icon(Icons.folder)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCreateReportTab(state),
          _buildCurrentReportTab(state),
          _buildSavedReportsTab(state),
        ],
      ),
    );
  }

  /// تبويب إنشاء التقرير
  Widget _buildCreateReportTab(TrialBalanceState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان التقرير
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'عنوان التقرير',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.title),
            ),
          ),
          const SizedBox(height: 16),

          // اختيار الفترة الزمنية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الفترة الزمنية',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // الفترات المحددة مسبقاً
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: PresetPeriod.values.map((period) {
                      return ElevatedButton(
                        onPressed: () {
                          ref
                              .read(trialBalanceProvider.notifier)
                              .setPresetPeriod(period);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor.withOpacity(0.1),
                          foregroundColor: kMainColor,
                        ),
                        child: Text(period.arabicName),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),

                  // تواريخ مخصصة
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(context, true),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.calendar_today),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text('من تاريخ',
                                        style: TextStyle(fontSize: 12)),
                                    Text(DateFormat('yyyy/MM/dd')
                                        .format(state.fromDate)),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(context, false),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.calendar_today),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text('إلى تاريخ',
                                        style: TextStyle(fontSize: 12)),
                                    Text(DateFormat('yyyy/MM/dd')
                                        .format(state.toDate)),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // زر إنشاء التقرير
          ElevatedButton.icon(
            onPressed: state.isLoading
                ? null
                : () {
                    ref.read(trialBalanceProvider.notifier).generateReport(
                          title: _titleController.text,
                        );
                    _tabController.animateTo(1);
                  },
            icon: state.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.analytics),
            label: Text(state.isLoading ? 'جاري الإنشاء...' : 'إنشاء التقرير'),
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
            ),
          ),

          // رسالة الخطأ
          if (state.error != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(
                      child: Text(state.error!,
                          style: const TextStyle(color: Colors.red))),
                  IconButton(
                    onPressed: () =>
                        ref.read(trialBalanceProvider.notifier).clearError(),
                    icon: const Icon(Icons.close, color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// تبويب التقرير الحالي
  Widget _buildCurrentReportTab(TrialBalanceState state) {
    if (!state.hasCurrentReport) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا يوجد تقرير حالي',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'قم بإنشاء تقرير جديد من التبويب الأول',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    final report = state.currentReport!;

    return Column(
      children: [
        // معلومات التقرير
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: report.isBalanced
              ? Colors.green.withOpacity(0.1)
              : Colors.red.withOpacity(0.1),
          child: Column(
            children: [
              Text(
                report.reportTitle,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'من ${DateFormat('yyyy/MM/dd').format(report.fromDate)} إلى ${DateFormat('yyyy/MM/dd').format(report.toDate)}',
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildSummaryCard(
                      'إجمالي المدين', report.totalDebits, Colors.blue),
                  _buildSummaryCard(
                      'إجمالي الدائن', report.totalCredits, Colors.green),
                  _buildSummaryCard(
                    'الفرق',
                    report.difference.abs(),
                    report.isBalanced ? Colors.green : Colors.red,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: report.isBalanced ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  report.isBalanced ? 'متوازن ✓' : 'غير متوازن ✗',
                  style: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),

        // جدول التقرير
        Expanded(
          child: _buildTrialBalanceTable(report),
        ),

        // أزرار الإجراءات
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    final success = await ref
                        .read(trialBalanceProvider.notifier)
                        .saveCurrentReport();
                    if (success && mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('تم حفظ التقرير بنجاح')),
                      );
                    }
                  },
                  icon: const Icon(Icons.save),
                  label: const Text('حفظ التقرير'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kMainColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // ميزة الطباعة قيد التطوير
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('ميزة الطباعة قيد التطوير')),
                    );
                  },
                  icon: const Icon(Icons.print),
                  label: const Text('طباعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// تبويب التقارير المحفوظة
  Widget _buildSavedReportsTab(TrialBalanceState state) {
    if (state.savedReports.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد تقارير محفوظة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.savedReports.length,
      itemBuilder: (context, index) {
        final report = state.savedReports[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: report.isBalanced ? Colors.green : Colors.red,
              child: Icon(
                report.isBalanced ? Icons.check : Icons.close,
                color: Colors.white,
              ),
            ),
            title: Text(report.reportTitle),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'من ${DateFormat('yyyy/MM/dd').format(report.fromDate)} إلى ${DateFormat('yyyy/MM/dd').format(report.toDate)}',
                ),
                Text(
                  'تم الإنشاء: ${DateFormat('yyyy/MM/dd HH:mm').format(report.generatedAt)}',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'view',
                  child: const Row(
                    children: [
                      Icon(Icons.visibility),
                      SizedBox(width: 8),
                      Text('عرض'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: const Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) async {
                if (value == 'view') {
                  ref
                      .read(trialBalanceProvider.notifier)
                      .loadSavedReport(report);
                  _tabController.animateTo(1);
                } else if (value == 'delete') {
                  final confirmed = await _showDeleteConfirmation(context);
                  if (confirmed) {
                    final success = await ref
                        .read(trialBalanceProvider.notifier)
                        .deleteReport(report.reportId);
                    if (success && mounted) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('تم حذف التقرير بنجاح')),
                        );
                      }
                    }
                  }
                }
              },
            ),
          ),
        );
      },
    );
  }

  /// بناء جدول ميزان المراجعة
  Widget _buildTrialBalanceTable(TrialBalanceReport report) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columnSpacing: 20,
          headingRowColor:
              MaterialStateProperty.all(kMainColor.withOpacity(0.1)),
          columns: const [
            DataColumn(
                label: Text('رمز الحساب',
                    style: TextStyle(fontWeight: FontWeight.bold))),
            DataColumn(
                label: Text('اسم الحساب',
                    style: TextStyle(fontWeight: FontWeight.bold))),
            DataColumn(
                label: Text('نوع الحساب',
                    style: TextStyle(fontWeight: FontWeight.bold))),
            DataColumn(
                label: Text('مدين',
                    style: TextStyle(fontWeight: FontWeight.bold))),
            DataColumn(
                label: Text('دائن',
                    style: TextStyle(fontWeight: FontWeight.bold))),
          ],
          rows: [
            ...report.items.map((item) => DataRow(
                  cells: [
                    DataCell(Text(item.accountCode)),
                    DataCell(Text(item.accountName)),
                    DataCell(Text(item.accountType)),
                    DataCell(Text(
                      item.debitAmount > 0
                          ? NumberFormat('#,##0.00').format(item.debitAmount)
                          : '-',
                      style: TextStyle(
                          color:
                              item.debitAmount > 0 ? Colors.blue : Colors.grey),
                    )),
                    DataCell(Text(
                      item.creditAmount > 0
                          ? NumberFormat('#,##0.00').format(item.creditAmount)
                          : '-',
                      style: TextStyle(
                          color: item.creditAmount > 0
                              ? Colors.green
                              : Colors.grey),
                    )),
                  ],
                )),
            // صف الإجماليات
            DataRow(
              color: MaterialStateProperty.all(Colors.grey.withOpacity(0.2)),
              cells: [
                const DataCell(Text('')),
                const DataCell(Text('الإجمالي',
                    style: TextStyle(fontWeight: FontWeight.bold))),
                const DataCell(Text('')),
                DataCell(Text(
                  NumberFormat('#,##0.00').format(report.totalDebits),
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, color: Colors.blue),
                )),
                DataCell(Text(
                  NumberFormat('#,##0.00').format(report.totalCredits),
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, color: Colors.green),
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة ملخص
  Widget _buildSummaryCard(String title, double value, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const SizedBox(height: 4),
        Text(
          NumberFormat('#,##0.00').format(value),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final state = ref.read(trialBalanceProvider);
    final initialDate = isFromDate ? state.fromDate : state.toDate;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      if (isFromDate) {
        ref.read(trialBalanceProvider.notifier).updateFromDate(picked);
      } else {
        ref.read(trialBalanceProvider.notifier).updateToDate(picked);
      }
    }
  }

  /// إظهار تأكيد الحذف
  Future<bool> _showDeleteConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذا التقرير؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
