// بسم الله الرحمن الرحيم توكلنا على الله

class ProductReportModel {
  final String productName;
  final String productCode;
  final num soldQuantity; // الكمية المباعة
  final num purchasedQuantity; // الكمية المشتراة
  final num remainingQuantity; // الكمية المتبقية
  final String date;
  final String invoiceNumber;
  final num averagePrice; // متوسط سعر البيع
  final num averagePurchasePrice; // متوسط سعر الشراء
  final int invoiceCount; // عدد الفواتير
  final num totalAmount; // إجمالي المبيعات
  final num totalPurchaseAmount; // إجمالي المشتريات

  ProductReportModel({
    required this.productName,
    required this.productCode,
    required this.soldQuantity,
    required this.purchasedQuantity,
    required this.remainingQuantity,
    required this.date,
    required this.invoiceNumber,
    required this.averagePrice,
    required this.averagePurchasePrice,
    required this.invoiceCount,
    required this.totalAmount,
    required this.totalPurchaseAmount,
  });

  // تحويل البيانات من JSON الى نموذج
  factory ProductReportModel.fromJson(Map<String, dynamic> json) {
    return ProductReportModel(
      productName: json['productName'] ?? '',
      productCode: json['productCode'] ?? '',
      soldQuantity: json['soldQuantity'] ?? 0,
      purchasedQuantity: json['purchasedQuantity'] ?? 0,
      remainingQuantity: json['remainingQuantity'] ?? 0,
      date: json['date'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      averagePrice: json['averagePrice'] ?? 0,
      averagePurchasePrice: json['averagePurchasePrice'] ?? 0,
      invoiceCount: json['invoiceCount'] ?? 0,
      totalAmount: json['totalAmount'] ?? 0,
      totalPurchaseAmount: json['totalPurchaseAmount'] ?? 0,
    );
  }

  // تحويل النموذج الى JSON
  Map<String, dynamic> toJson() {
    return {
      'productName': productName,
      'productCode': productCode,
      'soldQuantity': soldQuantity,
      'purchasedQuantity': purchasedQuantity,
      'remainingQuantity': remainingQuantity,
      'totalAmount': totalAmount,
      'totalPurchaseAmount': totalPurchaseAmount,
      'date': date,
      'invoiceNumber': invoiceNumber,
      'averagePrice': averagePrice,
      'averagePurchasePrice': averagePurchasePrice,
      'invoiceCount': invoiceCount,
    };
  }
}

// الحمد لله رب العالمين
