// مولد ملف PDF لتقرير أداء المندوبين
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/models/sales_performance_model.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// مولد ملف PDF لتقرير أداء المندوبين
class SalesPerformancePdfGenerator {
  /// إنشاء ملف PDF
  Future<void> generatePdf(
    List<SalesPerformanceModel> performanceData,
    DateTime startDate,
    DateTime endDate,
    BuildContext context,
  ) async {
    final pdf = pw.Document();

    // إنشاء الخط العربي
    final arabicFont = await _getArabicFont();

    // إنشاء صفحة الغلاف
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'تقرير أداء المندوبين',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  'الفترة: ${_formatDate(startDate)} - ${_formatDate(endDate)}',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 16,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 40),
                pw.Text(
                  'تاريخ التقرير: ${_formatDate(DateTime.now())}',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 12,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          );
        },
      ),
    );

    // إنشاء صفحة الملخص
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.all(20),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'ملخص الأداء',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'إجمالي المبيعات',
                        myFormat.format(
                            _calculateTotalSalesAmount(performanceData)),
                        'المبلغ',
                      ),
                    ),
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'عدد المبيعات',
                        _calculateTotalSalesCount(performanceData).toString(),
                        'فاتورة',
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'عدد العملاء',
                        _calculateTotalCustomersCount(performanceData)
                            .toString(),
                        'عميل',
                      ),
                    ),
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'متوسط قيمة الطلب',
                        myFormat.format(
                            _calculateAverageOrderValue(performanceData)),
                        'المبلغ',
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 40),
                pw.Text(
                  'مقارنة أداء المندوبين',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 10),
                pw.Table(
                  border: pw.TableBorder.all(),
                  children: [
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey300,
                      ),
                      children: [
                        _buildTableHeader(arabicFont, 'المندوب'),
                        _buildTableHeader(arabicFont, 'عدد المبيعات'),
                        _buildTableHeader(arabicFont, 'إجمالي المبيعات'),
                        _buildTableHeader(arabicFont, 'عدد العملاء'),
                        _buildTableHeader(arabicFont, 'متوسط قيمة الطلب'),
                      ],
                    ),
                    ...performanceData.map(
                      (performance) => pw.TableRow(
                        children: [
                          _buildTableCell(arabicFont, performance.userName),
                          _buildTableCell(
                              arabicFont, performance.totalSales.toString()),
                          _buildTableCell(arabicFont,
                              myFormat.format(performance.totalAmount)),
                          _buildTableCell(arabicFont,
                              performance.totalCustomers.toString()),
                          _buildTableCell(arabicFont,
                              myFormat.format(performance.averageOrderValue)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );

    // إنشاء صفحات تفاصيل المندوبين
    for (var performance in performanceData) {
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'تفاصيل أداء المندوب: ${performance.userName}',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 20),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: _buildSummaryItem(
                          arabicFont,
                          'عدد المبيعات',
                          performance.totalSales.toString(),
                          'فاتورة',
                        ),
                      ),
                      pw.Expanded(
                        child: _buildSummaryItem(
                          arabicFont,
                          'إجمالي المبيعات',
                          myFormat.format(performance.totalAmount),
                          'المبلغ',
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: _buildSummaryItem(
                          arabicFont,
                          'عدد العملاء',
                          performance.totalCustomers.toString(),
                          'عميل',
                        ),
                      ),
                      pw.Expanded(
                        child: _buildSummaryItem(
                          arabicFont,
                          'متوسط قيمة الطلب',
                          myFormat.format(performance.averageOrderValue),
                          'المبلغ',
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 40),
                  pw.Text(
                    'المبيعات حسب فئة المنتج',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 10),
                  pw.Table(
                    border: pw.TableBorder.all(),
                    children: [
                      pw.TableRow(
                        decoration: const pw.BoxDecoration(
                          color: PdfColors.grey300,
                        ),
                        children: [
                          _buildTableHeader(arabicFont, 'الفئة'),
                          _buildTableHeader(arabicFont, 'عدد المبيعات'),
                        ],
                      ),
                      ...performance.productCategorySales.entries.map(
                        (entry) => pw.TableRow(
                          children: [
                            _buildTableCell(arabicFont, entry.key),
                            _buildTableCell(arabicFont, entry.value.toString()),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 40),
                  pw.Text(
                    'قائمة المعاملات',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 10),
                  pw.Table(
                    border: pw.TableBorder.all(),
                    children: [
                      pw.TableRow(
                        decoration: const pw.BoxDecoration(
                          color: PdfColors.grey300,
                        ),
                        children: [
                          _buildTableHeader(arabicFont, 'رقم الفاتورة'),
                          _buildTableHeader(arabicFont, 'التاريخ'),
                          _buildTableHeader(arabicFont, 'العميل'),
                          _buildTableHeader(arabicFont, 'المبلغ'),
                        ],
                      ),
                      ...performance.transactions.map(
                        (transaction) => pw.TableRow(
                          children: [
                            _buildTableCell(
                                arabicFont, transaction.invoiceNumber),
                            _buildTableCell(
                              arabicFont,
                              DateFormat('yyyy/MM/dd').format(
                                  DateTime.parse(transaction.purchaseDate)),
                            ),
                            _buildTableCell(
                                arabicFont, transaction.customerName),
                            _buildTableCell(
                              arabicFont,
                              myFormat.format(
                                  transaction.totalAmount?.toDouble() ?? 0),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      );
    }

    // حفظ الملف
    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/تقرير_أداء_المندوبين_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());

    // فتح الملف
    await OpenFile.open(file.path);

    // عرض رسالة نجاح
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// إنشاء عنصر ملخص
  pw.Widget _buildSummaryItem(
    pw.Font font,
    String title,
    String value,
    String subtitle,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            font: font,
            fontSize: 12,
            color: PdfColors.grey700,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: font,
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.Text(
          subtitle,
          style: pw.TextStyle(
            font: font,
            fontSize: 10,
            color: PdfColors.grey700,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  /// إنشاء عنوان جدول
  pw.Widget _buildTableHeader(pw.Font font, String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: 12,
          fontWeight: pw.FontWeight.bold,
        ),
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// إنشاء خلية جدول
  pw.Widget _buildTableCell(pw.Font font, String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: 10,
        ),
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// الحصول على الخط العربي
  Future<pw.Font> _getArabicFont() async {
    final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    return pw.Font.ttf(fontData.buffer.asByteData());
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  /// حساب إجمالي مبلغ المبيعات
  double _calculateTotalSalesAmount(List<SalesPerformanceModel> data) {
    return data.fold(0, (sum, performance) => sum + performance.totalAmount);
  }

  /// حساب إجمالي عدد المبيعات
  int _calculateTotalSalesCount(List<SalesPerformanceModel> data) {
    return data.fold(0, (sum, performance) => sum + performance.totalSales);
  }

  /// حساب إجمالي عدد العملاء
  int _calculateTotalCustomersCount(List<SalesPerformanceModel> data) {
    final uniqueCustomers = <String>{};

    for (var performance in data) {
      for (var transaction in performance.transactions) {
        if (transaction.customerPhone.isNotEmpty) {
          uniqueCustomers.add(transaction.customerPhone);
        }
      }
    }

    return uniqueCustomers.length;
  }

  /// حساب متوسط قيمة الطلب
  double _calculateAverageOrderValue(List<SalesPerformanceModel> data) {
    final totalSales = _calculateTotalSalesCount(data);
    final totalAmount = _calculateTotalSalesAmount(data);

    return totalSales > 0 ? totalAmount / totalSales : 0;
  }
}
