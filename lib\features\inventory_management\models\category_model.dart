// بسم الله الرحمن الرحيم
// نموذج الفئة - يمثل بيانات فئة المنتجات

import 'dart:convert';

/// نموذج الفئة
class CategoryModel {
  /// ينشئ نموذج الفئة
  const CategoryModel({
    required this.id,
    required this.name,
    required this.createdAt,
    this.description,
    this.imagePath,
    this.parentId,
    this.color,
    this.icon,
    this.isActive = true,
    this.updatedAt,
  });

  /// معرف الفئة
  final String id;

  /// اسم الفئة
  final String name;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// وصف الفئة (اختياري)
  final String? description;

  /// مسار الصورة (اختياري)
  final String? imagePath;

  /// معرف الفئة الأب (اختياري)
  final String? parentId;

  /// لون الفئة (اختياري)
  final String? color;

  /// أيقونة الفئة (اختياري)
  final String? icon;

  /// هل الفئة نشطة؟
  final bool isActive;

  /// تاريخ التحديث (اختياري)
  final DateTime? updatedAt;

  /// نسخ النموذج مع تحديث بعض الحقول
  CategoryModel copyWith({
    String? name,
    String? description,
    String? imagePath,
    String? parentId,
    String? color,
    String? icon,
    bool? isActive,
    DateTime? updatedAt,
  }) {
    return CategoryModel(
      id: id,
      name: name ?? this.name,
      createdAt: createdAt,
      description: description ?? this.description,
      imagePath: imagePath ?? this.imagePath,
      parentId: parentId ?? this.parentId,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isActive: isActive ?? this.isActive,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'description': description,
      'imagePath': imagePath,
      'parentId': parentId,
      'color': color,
      'icon': icon,
      'isActive': isActive,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  /// إنشاء نموذج من Map
  factory CategoryModel.fromMap(Map<String, dynamic> map) {
    return CategoryModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      description: map['description'],
      imagePath: map['imagePath'],
      parentId: map['parentId'],
      color: map['color'],
      icon: map['icon'],
      isActive: map['isActive'] ?? true,
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory CategoryModel.fromJson(String source) =>
      CategoryModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name)';
  }
}
