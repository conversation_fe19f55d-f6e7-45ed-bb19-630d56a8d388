// مستودع بيانات أداء المندوبين
import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/models/sales_performance_model.dart';

/// مستودع بيانات أداء المندوبين
class SalesPerformanceRepo {
  /// الحصول على قائمة المستخدمين
  Future<List<Map<String, dynamic>>> getUsers() async {
    final List<Map<String, dynamic>> users = [];

    try {
      // إضافة المستخدم الرئيسي
      users.add({
        'id': constUserId,
        'name': 'المستخدم الرئيسي',
        'email': await getUserEmail(),
      });

      // الحصول على المستخدمين الفرعيين
      final ref = FirebaseDatabase.instance.ref('$constUserId/User Role');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        for (var child in snapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));

          users.add({
            'id': child.key ?? '',
            'name': data['name'] ?? '',
            'email': data['email'] ?? '',
          });
        }
      }
    } catch (e) {
      // خطأ في الحصول على قائمة المستخدمين
    }

    return users;
  }

  /// الحصول على بريد المستخدم الرئيسي
  Future<String> getUserEmail() async {
    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/Personal Information');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        var data = jsonDecode(jsonEncode(snapshot.value));
        return data['email'] ?? '';
      }
    } catch (e) {
      // خطأ في الحصول على بريد المستخدم الرئيسي
    }

    return '';
  }

  /// الحصول على أهداف المبيعات
  Future<List<SalesTargetModel>> getSalesTargets() async {
    final List<SalesTargetModel> targets = [];

    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/SalesTargets');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        for (var child in snapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));
          targets.add(SalesTargetModel.fromJson(data, child.key!));
        }
      }
    } catch (e) {
      // خطأ في الحصول على أهداف المبيعات
    }

    return targets;
  }

  /// إضافة هدف مبيعات جديد
  Future<void> addSalesTarget(SalesTargetModel target) async {
    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/SalesTargets');
      await ref.push().set(target.toJson());
    } catch (e) {
      // خطأ في إضافة هدف مبيعات
      rethrow;
    }
  }

  /// تحديث هدف مبيعات
  Future<void> updateSalesTarget(SalesTargetModel target) async {
    try {
      final ref = FirebaseDatabase.instance
          .ref('$constUserId/SalesTargets/${target.id}');
      await ref.update(target.toJson());
    } catch (e) {
      // خطأ في تحديث هدف مبيعات
      rethrow;
    }
  }

  /// حذف هدف مبيعات
  Future<void> deleteSalesTarget(String targetId) async {
    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/SalesTargets/$targetId');
      await ref.remove();
    } catch (e) {
      // خطأ في حذف هدف مبيعات
      rethrow;
    }
  }
}
