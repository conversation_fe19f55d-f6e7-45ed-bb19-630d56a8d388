// بسم الله الرحمن الرحيم توكلنا على الله

// ignore_for_file: unused_local_variable, avoid_print

import 'dart:convert';
import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../model/product_report_model.dart';
import 'package:intl/intl.dart'; // إضافة المكتبة الدولية

// انشاء مزود لتقرير المنتجات
final productReportProvider =
    StateNotifierProvider<ProductReportNotifier, List<ProductReportModel>>(
        (ref) {
  return ProductReportNotifier();
});

class ProductReportNotifier extends StateNotifier<List<ProductReportModel>> {
  ProductReportNotifier() : super([]);

  final DatabaseReference _database = FirebaseDatabase.instance.ref();
  StreamSubscription<DatabaseEvent>? _subscription;

  // دالة لجلب بيانات المنتجات من قاعدة البيانات
  Future<void> getProductReport(
      {required DateTime fromDate, required DateTime toDate}) async {
    try {
      // إلغاء الاشتراك السابق إذا كان موجود
      await _subscription?.cancel();
//04e38e519bd6f06ecf8ff3452c94fec0a48400fe0876c5ec89bafc09be8df79b
//************************************************************************************************************
      _subscription = _database.onValue.listen((event) {
        if (event.snapshot.value != null) {
          List<ProductReportModel> productReports = [];
          Map<String, Map<String, dynamic>> productStats = {};
          Map<String, Set<String>> productInvoices =
              {}; // لتتبع الفواتير لكل منتج
          Map<dynamic, dynamic> jsonData =
              event.snapshot.value as Map<dynamic, dynamic>;

          // Process both sales and purchase transactions with improved financial tracking
          jsonData.forEach((key, value) {
            if (value['Daily Transaction'] != null) {
              Map<dynamic, dynamic> transactionMap =
                  value['Daily Transaction'] as Map<dynamic, dynamic>;

              // Initialize daily stats for tracking
              Map<String, Map<String, dynamic>> dailyStats = {};

              transactionMap.forEach((transactionKey, transactionValue) {
                // Process Sales Transactions
                if (transactionValue['saleTransactionModel'] != null) {
                  Map<dynamic, dynamic> saleTransactionModelMap =
                      transactionValue['saleTransactionModel']
                          as Map<dynamic, dynamic>;

                  DateTime transactionDate = DateTime.parse(
                      saleTransactionModelMap['purchaseDate'] ?? '');
                  if ((transactionDate.isAfter(fromDate) ||
                          transactionDate.isAtSameMomentAs(fromDate)) &&
                      (transactionDate.isBefore(toDate) ||
                          transactionDate.isAtSameMomentAs(toDate))) {
                    String invoiceId =
                        transactionKey; // استخدام مفتاح المعاملة كرقم الفاتورة
                    var products = saleTransactionModelMap['productList'];
                    if (products != null) {
                      for (var productJson in products) {
                        // تحويل الـ JSON string إلى Map
                        Map<String, dynamic> product = json.decode(productJson);
                        String productId =
                            product['product_id']?.toString() ?? '';
                        num quantity =
                            num.tryParse(product['quantity'].toString()) ?? 0;
                        num stock =
                            num.tryParse(product['stock'].toString()) ?? 0;
                        num subTotal =
                            num.tryParse(product['sub_total'].toString()) ?? 0;

                        // تحديث إحصائيات المنتج مع تتبع مالي محسن
                        if (!productStats.containsKey(productId)) {
                          productStats[productId] = {
                            'soldQuantity': 0,
                            'purchasedQuantity': 0,
                            'stock': stock,
                            'name': product['product_name'] ?? '',
                            'totalRevenue': 0,
                            'totalPurchaseAmount': 0,
                            'averageSellingPrice': 0,
                            'dailyRevenue': {},
                            'dailySales': {},
                          };
                          productInvoices[productId] =
                              {}; // مجموعة فريدة لأرقام الفواتير
                        }

                        // تحديث الإحصائيات اليومية والكلية
                        String dateKey =
                            DateFormat('yyyy-MM-dd').format(transactionDate);

                        // تحديث المبيعات اليومية
                        if (!productStats[productId]!['dailyRevenue']
                            .containsKey(dateKey)) {
                          productStats[productId]!['dailyRevenue'][dateKey] = 0;
                          productStats[productId]!['dailySales'][dateKey] = 0;
                        }

                        // تحديث المبيعات اليومية بشكل منفصل
                        num currentDailyRevenue =
                            productStats[productId]!['dailyRevenue'][dateKey];
                        productStats[productId]!['dailyRevenue'][dateKey] =
                            currentDailyRevenue + subTotal;

                        num currentDailySales =
                            productStats[productId]!['dailySales'][dateKey];
                        productStats[productId]!['dailySales'][dateKey] =
                            currentDailySales + quantity;

                        // تحديث الإجماليات
                        productStats[productId]!['soldQuantity'] =
                            (productStats[productId]!['soldQuantity'] as num) +
                                quantity;
                        // بنحسب إجمالي قيمة المبيعات = الكمية × سعر الوحدة
                        num totalSaleAmount =
                            quantity * double.parse(subTotal.toString());

                        // بنضيف القيمة للإجمالي
                        productStats[productId]!['totalRevenue'] =
                            (productStats[productId]!['totalRevenue'] as num) +
                                totalSaleAmount;

                        // حساب متوسط سعر البيع
                        num totalRevenue =
                            productStats[productId]!['totalRevenue'] as num;
                        num soldQuantity =
                            productStats[productId]!['soldQuantity'] as num;
                        productStats[productId]!['averageSellingPrice'] =
                            soldQuantity > 0 ? totalRevenue / soldQuantity : 0;

                        // إضافة رقم الفاتورة إلى مجموعة فواتير المنتج
                        productInvoices[productId]!.add(invoiceId);
                      }
                    }
                  }
                }

                // معالجة المشتريات
                if (transactionValue['Purchase Transition'] != null) {
                  Map<dynamic, dynamic> purchaseTransactionMap =
                      transactionValue['Purchase Transition']
                          as Map<dynamic, dynamic>;

                  DateTime transactionDate = DateTime.parse(
                      purchaseTransactionMap['purchaseDate'] ?? '');
                  String dateKey =
                      DateFormat('yyyy-MM-dd').format(transactionDate);

                  var products = purchaseTransactionMap['productList'];
                  if (products != null) {
                    for (var product in products) {
                      String productId =
                          product['productCode']?.toString() ?? '';
                      num quantity =
                          num.tryParse(product['productStock'].toString()) ?? 0;
                      num purchasePrice = num.tryParse(
                              product['productPurchasePrice'].toString()) ??
                          0;
                      num totalAmount = quantity * purchasePrice;

                      if (!productStats.containsKey(productId)) {
                        productStats[productId] = {
                          'soldQuantity': 0,
                          'purchasedQuantity': 0,
                          'stock': quantity,
                          'name': product['productName'] ?? '',
                          'totalRevenue': 0,
                          'totalPurchaseAmount': 0,
                          'averageSellingPrice': 0,
                          'averagePurchasePrice': 0,
                          'dailyRevenue': {},
                          'dailySales': {},
                          'dailyPurchases': {},
                          'dailyPurchaseAmount': {}
                        };
                        productInvoices[productId] = {};
                      }

                      if (!productStats[productId]!['dailyRevenue']
                          .containsKey(dateKey)) {
                        productStats[productId]!['dailyRevenue'][dateKey] = 0;
                        productStats[productId]!['dailySales'][dateKey] = 0;
                      }

                      productStats[productId]!['purchasedQuantity'] =
                          (productStats[productId]!['purchasedQuantity']
                                  as num) +
                              quantity;
                      productStats[productId]!['totalPurchaseAmount'] =
                          (productStats[productId]!['totalPurchaseAmount']
                                  as num) +
                              totalAmount;
                    }
                  }
                }
              });
            }
          });

          // تحويل البيانات المجمعة إلى نماذج
          productStats.forEach((productId, data) {
            num soldQuantity = data['soldQuantity'] as num;
            num purchasedQuantity = data['purchasedQuantity'] as num;
            num totalRevenue = data['totalRevenue'] as num;
            num totalPurchaseAmount = data['totalPurchaseAmount'] as num;
            num averageSellingPrice = data['averageSellingPrice'] as num;

            productReports.add(
              ProductReportModel(
                productName: data['name'].toString(),
                productCode: productId,
                soldQuantity: soldQuantity,
                purchasedQuantity: purchasedQuantity,
                remainingQuantity: (data['stock'] as num) - soldQuantity,
                date: DateFormat('yyyy-MM-dd').format(toDate),
                invoiceNumber: '-',
                averagePrice:
                    soldQuantity > 0 ? totalRevenue / soldQuantity : 0,
                averagePurchasePrice: purchasedQuantity > 0
                    ? totalPurchaseAmount / purchasedQuantity
                    : 0,
                invoiceCount: productInvoices[productId]?.length ?? 0,
                totalAmount: totalRevenue, // إضافة إجمالي المبيعات
                totalPurchaseAmount:
                    totalPurchaseAmount, // إضافة إجمالي المشتريات
              ),
            );
          });

          state = productReports;
        }
      });
    } catch (e) {
      print('حدث خطأ اثناء جلب بيانات تقرير المنتجات: $e');
      rethrow; // إعادة رمي الخطأ للتعامل معه في الواجهة
    }
  }

  // دالة لحساب اجمالي كمية منتج معين
  num calculateTotalQuantity(String productCode) {
    return state
        .where((report) => report.productCode == productCode)
        .fold(0, (sum, report) => sum + report.soldQuantity);
  }

  // دالة للحصول على قائمة المنتجات الفريدة
  List<ProductReportModel> getUniqueProducts() {
    Map<String, ProductReportModel> uniqueProducts = {};

    for (var report in state) {
      if (!uniqueProducts.containsKey(report.productCode)) {
        uniqueProducts[report.productCode] = report;
      }
    }

    return uniqueProducts.values.toList();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}

// الحمد لله رب العالمين
