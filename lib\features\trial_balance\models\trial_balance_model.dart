// بسم الله الرحمن الرحيم
// نموذج بيانات تقرير Trial Balance

/// نموذج عنصر واحد في تقرير Trial Balance
class TrialBalanceItem {
  final String accountName;
  final String accountCode;
  final double debitAmount;
  final double creditAmount;
  final String accountType;
  final String description;

  TrialBalanceItem({
    required this.accountName,
    required this.accountCode,
    required this.debitAmount,
    required this.creditAmount,
    required this.accountType,
    this.description = '',
  });

  /// تحويل من JSON
  factory TrialBalanceItem.fromJson(Map<String, dynamic> json) {
    return TrialBalanceItem(
      accountName: json['accountName'] ?? '',
      accountCode: json['accountCode'] ?? '',
      debitAmount: (json['debitAmount'] ?? 0).toDouble(),
      creditAmount: (json['creditAmount'] ?? 0).toDouble(),
      accountType: json['accountType'] ?? '',
      description: json['description'] ?? '',
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'accountName': accountName,
      'accountCode': accountCode,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'accountType': accountType,
      'description': description,
    };
  }

  /// نسخ مع تعديل
  TrialBalanceItem copyWith({
    String? accountName,
    String? accountCode,
    double? debitAmount,
    double? creditAmount,
    String? accountType,
    String? description,
  }) {
    return TrialBalanceItem(
      accountName: accountName ?? this.accountName,
      accountCode: accountCode ?? this.accountCode,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      accountType: accountType ?? this.accountType,
      description: description ?? this.description,
    );
  }
}

/// نموذج تقرير Trial Balance الكامل
class TrialBalanceReport {
  final String reportId;
  final String reportTitle;
  final DateTime reportDate;
  final DateTime fromDate;
  final DateTime toDate;
  final List<TrialBalanceItem> items;
  final double totalDebits;
  final double totalCredits;
  final bool isBalanced;
  final String generatedBy;
  final DateTime generatedAt;

  TrialBalanceReport({
    required this.reportId,
    required this.reportTitle,
    required this.reportDate,
    required this.fromDate,
    required this.toDate,
    required this.items,
    required this.totalDebits,
    required this.totalCredits,
    required this.isBalanced,
    required this.generatedBy,
    required this.generatedAt,
  });

  /// تحويل من JSON
  factory TrialBalanceReport.fromJson(Map<String, dynamic> json) {
    return TrialBalanceReport(
      reportId: json['reportId'] ?? '',
      reportTitle: json['reportTitle'] ?? '',
      reportDate: DateTime.parse(json['reportDate'] ?? DateTime.now().toIso8601String()),
      fromDate: DateTime.parse(json['fromDate'] ?? DateTime.now().toIso8601String()),
      toDate: DateTime.parse(json['toDate'] ?? DateTime.now().toIso8601String()),
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => TrialBalanceItem.fromJson(item))
          .toList() ?? [],
      totalDebits: (json['totalDebits'] ?? 0).toDouble(),
      totalCredits: (json['totalCredits'] ?? 0).toDouble(),
      isBalanced: json['isBalanced'] ?? false,
      generatedBy: json['generatedBy'] ?? '',
      generatedAt: DateTime.parse(json['generatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'reportTitle': reportTitle,
      'reportDate': reportDate.toIso8601String(),
      'fromDate': fromDate.toIso8601String(),
      'toDate': toDate.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
      'isBalanced': isBalanced,
      'generatedBy': generatedBy,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  /// حساب الفرق بين المدين والدائن
  double get difference => totalDebits - totalCredits;

  /// التحقق من توازن التقرير
  bool get isReportBalanced => (totalDebits - totalCredits).abs() < 0.01;

  /// الحصول على العناصر المدينة فقط
  List<TrialBalanceItem> get debitItems => 
      items.where((item) => item.debitAmount > 0).toList();

  /// الحصول على العناصر الدائنة فقط
  List<TrialBalanceItem> get creditItems => 
      items.where((item) => item.creditAmount > 0).toList();

  /// تجميع العناصر حسب نوع الحساب
  Map<String, List<TrialBalanceItem>> get itemsByAccountType {
    Map<String, List<TrialBalanceItem>> grouped = {};
    for (var item in items) {
      if (!grouped.containsKey(item.accountType)) {
        grouped[item.accountType] = [];
      }
      grouped[item.accountType]!.add(item);
    }
    return grouped;
  }

  /// إنشاء تقرير فارغ
  factory TrialBalanceReport.empty() {
    return TrialBalanceReport(
      reportId: '',
      reportTitle: 'تقرير ميزان المراجعة',
      reportDate: DateTime.now(),
      fromDate: DateTime.now(),
      toDate: DateTime.now(),
      items: [],
      totalDebits: 0,
      totalCredits: 0,
      isBalanced: true,
      generatedBy: '',
      generatedAt: DateTime.now(),
    );
  }
}

/// أنواع الحسابات في ميزان المراجعة
enum AccountType {
  assets('الأصول'),
  liabilities('الخصوم'),
  equity('حقوق الملكية'),
  revenue('الإيرادات'),
  expenses('المصروفات'),
  cash('النقدية'),
  inventory('المخزون'),
  accountsReceivable('حسابات مدينة'),
  accountsPayable('حسابات دائنة');

  const AccountType(this.arabicName);
  final String arabicName;
}

/// فئات الحسابات الرئيسية
class AccountCategory {
  static const String assets = 'الأصول';
  static const String liabilities = 'الخصوم';
  static const String equity = 'حقوق الملكية';
  static const String revenue = 'الإيرادات';
  static const String expenses = 'المصروفات';
  
  static List<String> get allCategories => [
    assets,
    liabilities,
    equity,
    revenue,
    expenses,
  ];
}
