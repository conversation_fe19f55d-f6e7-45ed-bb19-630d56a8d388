// بسم الله الرحمن الرحيم
// خدمة تنبيهات المخزون - مسؤولة عن إدارة تنبيهات المخزون

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../models/product_model.dart';
import 'inventory_service.dart';

/// خدمة تنبيهات المخزون
class StockAlertService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة تنبيهات المخزون
  static final StockAlertService _instance = StockAlertService._internal();
  factory StockAlertService() => _instance;
  StockAlertService._internal();

  // خدمة المخزون
  final InventoryService _inventoryService = InventoryService();

  // مثيل الإشعارات المحلية
  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  // مؤقت فحص المخزون
  Timer? _stockCheckTimer;

  // حالة التهيئة
  bool _isInitialized = false;

  /// تهيئة خدمة تنبيهات المخزون
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة الإشعارات المحلية
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const InitializationSettings initializationSettings =
          InitializationSettings(android: initializationSettingsAndroid);
      await _notifications.initialize(initializationSettings);

      // بدء مؤقت فحص المخزون
      _startStockCheckTimer();

      _isInitialized = true;
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة تنبيهات المخزون: $e');
    }
  }

  /// بدء مؤقت فحص المخزون
  void _startStockCheckTimer() {
    // فحص المخزون كل ساعة
    _stockCheckTimer = Timer.periodic(const Duration(hours: 1), (_) {
      checkLowStockProducts();
    });

    // فحص المخزون فورًا
    checkLowStockProducts();
  }

  /// فحص المنتجات منخفضة المخزون
  Future<void> checkLowStockProducts() async {
    try {
      final lowStockProducts = await _inventoryService.getLowStockProducts();
      final outOfStockProducts = await _inventoryService.getOutOfStockProducts();

      if (lowStockProducts.isNotEmpty) {
        _showLowStockNotification(lowStockProducts);
      }

      if (outOfStockProducts.isNotEmpty) {
        _showOutOfStockNotification(outOfStockProducts);
      }
    } catch (e) {
      debugPrint('خطأ في فحص المنتجات منخفضة المخزون: $e');
    }
  }

  /// عرض إشعار المنتجات منخفضة المخزون
  Future<void> _showLowStockNotification(List<ProductModel> products) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'low_stock_channel',
        'Low Stock Alerts',
        channelDescription: 'Notifications for low stock products',
        importance: Importance.high,
        priority: Priority.high,
      );
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _notifications.show(
        1,
        'تنبيه: منتجات منخفضة المخزون',
        'يوجد ${products.length} منتج منخفض المخزون',
        platformChannelSpecifics,
        payload: 'low_stock',
      );
    } catch (e) {
      debugPrint('خطأ في عرض إشعار المنتجات منخفضة المخزون: $e');
    }
  }

  /// عرض إشعار المنتجات التي نفذت من المخزون
  Future<void> _showOutOfStockNotification(List<ProductModel> products) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'out_of_stock_channel',
        'Out of Stock Alerts',
        channelDescription: 'Notifications for out of stock products',
        importance: Importance.high,
        priority: Priority.high,
      );
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _notifications.show(
        2,
        'تنبيه: منتجات نفذت من المخزون',
        'يوجد ${products.length} منتج نفذ من المخزون',
        platformChannelSpecifics,
        payload: 'out_of_stock',
      );
    } catch (e) {
      debugPrint('خطأ في عرض إشعار المنتجات التي نفذت من المخزون: $e');
    }
  }

  /// عرض إشعار منتج منخفض المخزون
  Future<void> showProductLowStockNotification(ProductModel product) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'product_low_stock_channel',
        'Product Low Stock Alerts',
        channelDescription: 'Notifications for specific product low stock',
        importance: Importance.high,
        priority: Priority.high,
      );
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _notifications.show(
        3,
        'تنبيه: منتج منخفض المخزون',
        'المنتج ${product.name} منخفض المخزون (${product.quantity} ${product.unit})',
        platformChannelSpecifics,
        payload: 'product_low_stock_${product.id}',
      );
    } catch (e) {
      debugPrint('خطأ في عرض إشعار منتج منخفض المخزون: $e');
    }
  }

  /// عرض إشعار منتج نفذ من المخزون
  Future<void> showProductOutOfStockNotification(ProductModel product) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'product_out_of_stock_channel',
        'Product Out of Stock Alerts',
        channelDescription: 'Notifications for specific product out of stock',
        importance: Importance.high,
        priority: Priority.high,
      );
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _notifications.show(
        4,
        'تنبيه: منتج نفذ من المخزون',
        'المنتج ${product.name} نفذ من المخزون',
        platformChannelSpecifics,
        payload: 'product_out_of_stock_${product.id}',
      );
    } catch (e) {
      debugPrint('خطأ في عرض إشعار منتج نفذ من المخزون: $e');
    }
  }

  /// التحقق من حالة المنتج وعرض الإشعارات المناسبة
  Future<void> checkProductStockStatus(ProductModel product) async {
    try {
      if (product.status == ProductStatus.lowStock) {
        await showProductLowStockNotification(product);
      } else if (product.status == ProductStatus.outOfStock) {
        await showProductOutOfStockNotification(product);
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة المنتج: $e');
    }
  }

  /// إيقاف خدمة تنبيهات المخزون
  void dispose() {
    _stockCheckTimer?.cancel();
  }
}

/// مزود خدمة تنبيهات المخزون
final stockAlertServiceProvider = Provider<StockAlertService>((ref) {
  return StockAlertService();
});

/// مزود تهيئة خدمة تنبيهات المخزون
final stockAlertServiceInitProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(stockAlertServiceProvider);
  return await service.initialize();
});
