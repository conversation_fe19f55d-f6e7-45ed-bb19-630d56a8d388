// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:firebase_database/firebase_database.dart';
// import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
// import 'package:mobile_pos/const_commas.dart';
// import 'dart:convert';
// import 'package:speech_to_text/speech_to_text.dart' as stt;
// import 'package:google_fonts/google_fonts.dart';

// //import '../../model/customer_model.dart';
// import '../../model/due_transaction_model.dart';
// import '../../Provider/customer_provider.dart';
// import '../../Provider/due_transaction_provider.dart';
// import '../../constant.dart';
// import '../../currency.dart';

// class AccountSettlementScreen extends ConsumerStatefulWidget {
//   const AccountSettlementScreen({super.key});

//   @override
//   _AccountSettlementScreenState createState() =>
//       _AccountSettlementScreenState();
// }

// class _AccountSettlementScreenState
//     extends ConsumerState<AccountSettlementScreen> {
//   late stt.SpeechToText _speech;
//   bool _isListening = false;
//   String _searchText = '';

//   @override
//   void initState() {
//     super.initState();
//     _speech = stt.SpeechToText();
//   }

//   void _listen() async {
//     if (!_isListening) {
//       bool available = await _speech.initialize(
//         onStatus: (val) => print('onStatus: $val'),
//         onError: (val) => print('onError: $val'),
//       );
//       if (available) {
//         setState(() => _isListening = true);
//         _speech.listen(
//           onResult: (val) => setState(() {
//             _searchText = val.recognizedWords;
//             _isListening = false;
//           }),
//         );
//       }
//     } else {
//       setState(() => _isListening = false);
//       _speech.stop();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: kMainColor,
//       resizeToAvoidBottomInset: true,
//       appBar: AppBar(
//         backgroundColor: kMainColor,
//         title: Text(
//           'تسوية الحساب',
//           style: GoogleFonts.poppins(
//             color: Colors.white,
//           ),
//         ),
//         centerTitle: true,
//         iconTheme: const IconThemeData(color: Colors.white),
//         elevation: 0.0,
//       ),
//       body: Container(
//         alignment: Alignment.topCenter,
//         decoration: const BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.only(
//                 topRight: Radius.circular(30), topLeft: Radius.circular(30))),
//         child: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             children: [
//               TextField(
//                 onChanged: (value) {
//                   setState(() {
//                     _searchText = value;
//                   });
//                 },
//                 decoration: InputDecoration(
//                   labelText: 'Search',
//                   prefixIcon: Icon(Icons.search),
//                   suffixIcon: IconButton(
//                     icon: Icon(_isListening ? Icons.mic : Icons.mic_none),
//                     onPressed: _listen,
//                   ),
//                   border: OutlineInputBorder(
//                     borderRadius: BorderRadius.circular(8.0),
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 16.0),
//               Expanded(
//                 child: Consumer(builder: (context, ref, _) {
//                   final customerProviderRef = ref.watch(customerProvider);
//                   final dueTransactionProviderRef =
//                       ref.watch(dueTransactionProvider);

//                   return customerProviderRef.when(data: (customers) {
//                     return dueTransactionProviderRef.when(
//                         data: (dueTransactions) {
//                       final filteredCustomers = customers.where((customer) {
//                         return customer.customerName
//                             .toLowerCase()
//                             .contains(_searchText.toLowerCase());
//                       }).toList();

//                       return ListView.builder(
//                         itemCount: filteredCustomers.length,
//                         itemBuilder: (context, index) {
//                           final customer = filteredCustomers[index];
//                           double totalDue = 0;

//                           for (var transaction in dueTransactions) {
//                             if (transaction.customerPhone ==
//                                 customer.phoneNumber) {
//                               totalDue += transaction.totalDue!.toDouble();
//                             }
//                           }

//                           return Card(
//                             margin: const EdgeInsets.symmetric(vertical: 8.0),
//                             child: ListTile(
//                               title: Text(
//                                 customer.customerName,
//                                 style: TextStyle(fontWeight: FontWeight.bold),
//                               ),
//                               subtitle: Text(
//                                   'Remaining Balance: $currency${myFormat.format(totalDue)}'),
//                               trailing: IconButton(
//                                 icon: const Icon(Icons.edit),
//                                 onPressed: () {
//                                   _showEditDialog(context, customer, totalDue);
//                                 },
//                               ),
//                             ),
//                           );
//                         },
//                       );
//                     }, error: (e, stack) {
//                       return Center(child: Text(e.toString()));
//                     }, loading: () {
//                       return const Center(child: CircularProgressIndicator());
//                     });
//                   }, error: (e, stack) {
//                     return Center(child: Text(e.toString()));
//                   }, loading: () {
//                     return const Center(child: CircularProgressIndicator());
//                   });
//                 }),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   void _showEditDialog(
//       BuildContext context, CustomerModel customer, double totalDue) {
//     final TextEditingController controller =
//         TextEditingController(text: totalDue.toString());

//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: Text('Edit Remaining Balance for ${customer.customerName}'),
//           content: TextField(
//             controller: controller,
//             keyboardType: TextInputType.number,
//             decoration: const InputDecoration(labelText: 'Remaining Balance'),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: const Text('Cancel'),
//             ),
//             TextButton(
//               onPressed: () {
//                 final newBalance = double.parse(controller.text);
//                 _updateRemainingBalance(customer, newBalance);
//                 Navigator.of(context).pop();
//               },
//               child: const Text('Save'),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   void _updateRemainingBalance(CustomerModel customer, double newBalance) {
//     final ref = FirebaseDatabase.instance
//         .ref('$constUserId/Customers/${customer.phoneNumber}');
//     ref.update({'remainedBalance': newBalance});
//   }
// }
