import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/user_log_service.dart';

/// تنفيذ سجلات المستخدم في وظائف المصادقة
class AuthLogImplementation {
  /// تسجيل عملية تسجيل الدخول
  static Future<void> logUserLogin({
    required String email,
    String? userId,
    String? userName,
    WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.userLogin,
        description: 'تم تسجيل الدخول بنجاح',
        data: {
          'email': email,
          'timestamp': DateTime.now().toIso8601String(),
          'deviceInfo': Platform.operatingSystem,
        },
        userId: userId,
        userName: userName,
      );
      
      debugPrint('تم تسجيل نشاط تسجيل الدخول بنجاح');
    } catch (e) {
      debugPrint('خطأ في تسجيل نشاط تسجيل الدخول: $e');
    }
  }

  /// تسجيل عملية تسجيل الخروج
  static Future<void> logUserLogout({
    required String email,
    String? userId,
    String? userName,
    WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.userLogout,
        description: 'تم تسجيل الخروج بنجاح',
        data: {
          'email': email,
          'timestamp': DateTime.now().toIso8601String(),
          'deviceInfo': Platform.operatingSystem,
        },
        userId: userId,
        userName: userName,
      );
      
      debugPrint('تم تسجيل نشاط تسجيل الخروج بنجاح');
    } catch (e) {
      debugPrint('خطأ في تسجيل نشاط تسجيل الخروج: $e');
    }
  }

  /// تسجيل عملية إنشاء حساب جديد
  static Future<void> logUserCreated({
    required String email,
    String? userId,
    String? userName,
    WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.userCreated,
        description: 'تم إنشاء حساب جديد بنجاح',
        data: {
          'email': email,
          'timestamp': DateTime.now().toIso8601String(),
          'deviceInfo': Platform.operatingSystem,
        },
        userId: userId,
        userName: userName,
      );
      
      debugPrint('تم تسجيل نشاط إنشاء حساب جديد بنجاح');
    } catch (e) {
      debugPrint('خطأ في تسجيل نشاط إنشاء حساب جديد: $e');
    }
  }

  /// تسجيل عملية تغيير كلمة المرور
  static Future<void> logPasswordChanged({
    required String email,
    String? userId,
    String? userName,
    WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.userUpdated,
        description: 'تم تغيير كلمة المرور بنجاح',
        data: {
          'email': email,
          'timestamp': DateTime.now().toIso8601String(),
          'deviceInfo': Platform.operatingSystem,
          'action': 'password_change',
        },
        userId: userId,
        userName: userName,
      );
      
      debugPrint('تم تسجيل نشاط تغيير كلمة المرور بنجاح');
    } catch (e) {
      debugPrint('خطأ في تسجيل نشاط تغيير كلمة المرور: $e');
    }
  }
}
