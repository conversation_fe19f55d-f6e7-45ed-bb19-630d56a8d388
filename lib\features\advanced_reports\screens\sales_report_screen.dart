// بسم الله الرحمن الرحيم
// شاشة تقرير المبيعات - تعرض تقرير المبيعات

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/report_model.dart';
import '../services/report_service.dart';
import '../services/export_service.dart';
import '../widgets/report_chart.dart';
import '../widgets/report_table.dart';
import '../widgets/report_filter.dart';

/// شاشة تقرير المبيعات
class SalesReportScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة تقرير المبيعات
  const SalesReportScreen({
    super.key,
    this.report,
  });

  /// التقرير (اختياري)
  final ReportModel? report;

  @override
  ConsumerState<SalesReportScreen> createState() => _SalesReportScreenState();
}

class _SalesReportScreenState extends ConsumerState<SalesReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  ReportModel? _report;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  ReportParameters _parameters = const ReportParameters();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _report = widget.report;

    if (_report != null) {
      _titleController.text = _report!.title;
      _descriptionController.text = _report!.description ?? '';
      _parameters = ReportParameters.fromMap(_report!.parameters);
    } else {
      _titleController.text =
          'تقرير المبيعات ${DateFormat('yyyy-MM-dd').format(DateTime.now())}';
    }

    if (_report == null) {
      _generateReport();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  // توليد التقرير
  Future<void> _generateReport() async {
    if (_titleController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال عنوان التقرير')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reportService = ref.read(reportServiceProvider);

      if (_report == null) {
        // إنشاء تقرير جديد
        _report = await reportService.createReport(
          title: _titleController.text,
          type: ReportType.sales,
          period: ReportPeriod.custom,
          description: _descriptionController.text,
          parameters: _parameters.toMap(),
        );
      } else {
        // تحديث التقرير الحالي
        // في الإصدار الحقيقي، يجب إضافة وظيفة لتحديث التقرير
      }

      setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تصدير التقرير
  Future<void> _exportReport(ExportFormat format) async {
    if (_report == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إنشاء التقرير أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final exportService = ref.read(exportServiceProvider);
      final filePath = await exportService.exportReport(
        report: _report!,
        format: format,
      );

      if (filePath != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم تصدير التقرير إلى: $filePath')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل في تصدير التقرير')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // مشاركة التقرير
  Future<void> _shareReport() async {
    if (_report == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إنشاء التقرير أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final exportService = ref.read(exportServiceProvider);
      await exportService.shareReport(
        report: _report!,
        format: ExportFormat.pdf,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تحديث معلمات التقرير
  void _updateParameters(ReportParameters parameters) {
    setState(() {
      _parameters = parameters;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير المبيعات'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateReport,
            tooltip: 'تحديث التقرير',
          ),
          PopupMenuButton<ExportFormat>(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير التقرير',
            onSelected: _exportReport,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: ExportFormat.pdf,
                child: Text('تصدير كـ PDF'),
              ),
              const PopupMenuItem(
                value: ExportFormat.excel,
                child: Text('تصدير كـ Excel'),
              ),
              const PopupMenuItem(
                value: ExportFormat.csv,
                child: Text('تصدير كـ CSV'),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareReport,
            tooltip: 'مشاركة التقرير',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'ملخص'),
            Tab(text: 'رسم بياني'),
            Tab(text: 'جدول'),
          ],
        ),
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل التقرير...',
            )
          : Column(
              children: [
                // معلومات التقرير
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      TextField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان التقرير',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف التقرير (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),

                // فلتر التقرير
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: ReportFilter(
                    initialParameters: _parameters,
                    onFilterChanged: _updateParameters,
                    availableCategories: const [
                      'إلكترونيات',
                      'ملابس',
                      'أدوات منزلية',
                      'مستلزمات مكتبية'
                    ],
                    availableProducts: const [
                      'هاتف ذكي',
                      'لابتوب',
                      'سماعات',
                      'شاحن',
                      'حافظة هاتف'
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // محتوى التقرير
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // علامة التبويب الأولى: ملخص
                      _buildSummaryTab(),

                      // علامة التبويب الثانية: رسم بياني
                      _buildChartTab(),

                      // علامة التبويب الثالثة: جدول
                      _buildTableTab(),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _generateReport,
        tooltip: 'إنشاء التقرير',
        child: const Icon(Icons.save),
      ),
    );
  }

  // بناء علامة تبويب الملخص
  Widget _buildSummaryTab() {
    if (_report == null) {
      return const Center(
        child: Text('يرجى إنشاء التقرير أولاً'),
      );
    }

    final data = _report!.data;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الملخص
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي المبيعات',
                  value: '${data['totalSales']} جنيه',
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  title: 'عدد العناصر',
                  value: '${data['totalItems']}',
                  icon: Icons.shopping_cart,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'متوسط المبيعات',
                  value: '${data['averageSale']} جنيه',
                  icon: Icons.trending_up,
                  color: Colors.purple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  title: 'المنتج الأكثر مبيعًا',
                  value: '${data['topSellingProduct']}',
                  icon: Icons.star,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // رسم بياني مصغر
          const Text(
            'المبيعات اليومية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: ReportChart(
              data: _report!.chartData,
              type: ChartType.line,
              xAxisTitle: 'التاريخ',
              yAxisTitle: 'المبيعات (جنيه)',
            ),
          ),
          const SizedBox(height: 24),

          // جدول مصغر
          const Text(
            'أفضل المنتجات مبيعًا',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: ReportTable(
              data: _report!.tableData,
              columnLabels: const {
                'product': 'المنتج',
                'quantity': 'الكمية',
                'total': 'الإجمالي (جنيه)',
              },
              showPagination: false,
            ),
          ),
        ],
      ),
    );
  }

  // بناء علامة تبويب الرسم البياني
  Widget _buildChartTab() {
    if (_report == null) {
      return const Center(
        child: Text('يرجى إنشاء التقرير أولاً'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // أزرار تبديل نوع الرسم البياني
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SegmentedButton<ChartType>(
                segments: const [
                  ButtonSegment<ChartType>(
                    value: ChartType.line,
                    label: Text('خطي'),
                    icon: Icon(Icons.show_chart),
                  ),
                  ButtonSegment<ChartType>(
                    value: ChartType.bar,
                    label: Text('شريطي'),
                    icon: Icon(Icons.bar_chart),
                  ),
                  ButtonSegment<ChartType>(
                    value: ChartType.pie,
                    label: Text('دائري'),
                    icon: Icon(Icons.pie_chart),
                  ),
                ],
                selected: {_selectedChartType},
                onSelectionChanged: (Set<ChartType> newSelection) {
                  setState(() {
                    _selectedChartType = newSelection.first;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الرسم البياني
          Expanded(
            child: ReportChart(
              data: _report!.chartData,
              type: _selectedChartType,
              title: 'المبيعات اليومية',
              xAxisTitle: 'التاريخ',
              yAxisTitle: 'المبيعات (جنيه)',
            ),
          ),
        ],
      ),
    );
  }

  // نوع الرسم البياني المحدد
  ChartType _selectedChartType = ChartType.line;

  // بناء علامة تبويب الجدول
  Widget _buildTableTab() {
    if (_report == null) {
      return const Center(
        child: Text('يرجى إنشاء التقرير أولاً'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ReportTable(
        data: _report!.tableData,
        title: 'بيانات المبيعات',
        columnLabels: const {
          'product': 'المنتج',
          'quantity': 'الكمية',
          'total': 'الإجمالي (جنيه)',
        },
        sortable: true,
        showActions: true,
      ),
    );
  }

  // بناء بطاقة ملخص
  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.greyTextColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
