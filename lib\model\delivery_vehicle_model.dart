class DeliveryVehicleModel {
  late String id;
  late String vehicleName;
  late String vehicleType; // 'car', 'motorcycle', 'truck'
  late String plateNumber;
  late String model;
  late String year;
  late String fuelType; // 'gasoline', 'diesel'
  late String fuelConsumptionRate; // كم/لتر
  late String oilChangeInterval; // كم
  late String lastOilChange; // قراءة العداد عند آخر تغيير زيت
  late String maintenanceCostPerKm;
  late String driverSalaryPerKm;
  late String isActive;
  late String notes;
  late String createdAt;
  late String updatedAt;

  DeliveryVehicleModel({
    required this.id,
    required this.vehicleName,
    required this.vehicleType,
    required this.plateNumber,
    required this.model,
    required this.year,
    required this.fuelType,
    required this.fuelConsumptionRate,
    required this.oilChangeInterval,
    required this.lastOilChange,
    required this.maintenanceCostPerKm,
    required this.driverSalaryPerKm,
    required this.isActive,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  DeliveryVehicleModel.fromJson(Map<dynamic, dynamic> json) {
    id = json['id']?.toString() ?? '';
    vehicleName = json['vehicleName']?.toString() ?? '';
    vehicleType = json['vehicleType']?.toString() ?? '';
    plateNumber = json['plateNumber']?.toString() ?? '';
    model = json['model']?.toString() ?? '';
    year = json['year']?.toString() ?? '';
    fuelType = json['fuelType']?.toString() ?? '';
    fuelConsumptionRate = json['fuelConsumptionRate']?.toString() ?? '';
    oilChangeInterval = json['oilChangeInterval']?.toString() ?? '';
    lastOilChange = json['lastOilChange']?.toString() ?? '';
    maintenanceCostPerKm = json['maintenanceCostPerKm']?.toString() ?? '';
    driverSalaryPerKm = json['driverSalaryPerKm']?.toString() ?? '';
    isActive = json['isActive']?.toString() ?? 'true';
    notes = json['notes']?.toString() ?? '';
    createdAt = json['createdAt']?.toString() ?? '';
    updatedAt = json['updatedAt']?.toString() ?? '';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'id': id,
        'vehicleName': vehicleName,
        'vehicleType': vehicleType,
        'plateNumber': plateNumber,
        'model': model,
        'year': year,
        'fuelType': fuelType,
        'fuelConsumptionRate': fuelConsumptionRate,
        'oilChangeInterval': oilChangeInterval,
        'lastOilChange': lastOilChange,
        'maintenanceCostPerKm': maintenanceCostPerKm,
        'driverSalaryPerKm': driverSalaryPerKm,
        'isActive': isActive,
        'notes': notes,
        'createdAt': createdAt,
        'updatedAt': updatedAt,
      };
}
