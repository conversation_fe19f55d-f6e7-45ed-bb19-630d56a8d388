// نموذج مؤشرات الدقة والثقة في البيانات
class DataAccuracyModel {
  final double accuracyPercentage;
  final int totalItems;
  final int inconsistentItems;
  final String reportType;
  final DateTime checkDate;
  final List<InconsistencyItem> inconsistencyItems;
  
  DataAccuracyModel({
    required this.accuracyPercentage,
    required this.totalItems,
    required this.inconsistentItems,
    required this.reportType,
    required this.checkDate,
    required this.inconsistencyItems,
  });
  
  factory DataAccuracyModel.fromJson(Map<String, dynamic> json) {
    List<InconsistencyItem> items = [];
    
    if (json['inconsistencyItems'] != null) {
      for (var item in json['inconsistencyItems']) {
        items.add(InconsistencyItem.fromJson(item));
      }
    }
    
    return DataAccuracyModel(
      accuracyPercentage: json['accuracyPercentage'] ?? 0.0,
      totalItems: json['totalItems'] ?? 0,
      inconsistentItems: json['inconsistentItems'] ?? 0,
      reportType: json['reportType'] ?? '',
      checkDate: DateTime.parse(json['checkDate'] ?? DateTime.now().toString()),
      inconsistencyItems: items,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'accuracyPercentage': accuracyPercentage,
      'totalItems': totalItems,
      'inconsistentItems': inconsistentItems,
      'reportType': reportType,
      'checkDate': checkDate.toString(),
      'inconsistencyItems': inconsistencyItems.map((item) => item.toJson()).toList(),
    };
  }
  
  /// الحصول على مستوى الثقة في البيانات
  String getConfidenceLevel() {
    if (accuracyPercentage >= 95) {
      return 'عالي';
    } else if (accuracyPercentage >= 80) {
      return 'متوسط';
    } else {
      return 'منخفض';
    }
  }
  
  /// الحصول على لون مستوى الثقة
  int getConfidenceLevelColor() {
    if (accuracyPercentage >= 95) {
      return 0xFF4CAF50; // أخضر
    } else if (accuracyPercentage >= 80) {
      return 0xFFFFC107; // أصفر
    } else {
      return 0xFFF44336; // أحمر
    }
  }
}

/// نموذج عنصر عدم اتساق البيانات
class InconsistencyItem {
  final String itemId;
  final String itemName;
  final String inconsistencyType;
  final String details;
  final DateTime date;
  
  InconsistencyItem({
    required this.itemId,
    required this.itemName,
    required this.inconsistencyType,
    required this.details,
    required this.date,
  });
  
  factory InconsistencyItem.fromJson(Map<String, dynamic> json) {
    return InconsistencyItem(
      itemId: json['itemId'] ?? '',
      itemName: json['itemName'] ?? '',
      inconsistencyType: json['inconsistencyType'] ?? '',
      details: json['details'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'itemName': itemName,
      'inconsistencyType': inconsistencyType,
      'details': details,
      'date': date.toString(),
    };
  }
}
