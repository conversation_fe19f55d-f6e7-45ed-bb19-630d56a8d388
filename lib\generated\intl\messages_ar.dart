// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "ADDSALE": MessageLookupByLibrary.simpleMessage("إضافة بيع"),
        "CATEGORY": MessageLookupByLibrary.simpleMessage("الفئة"),
        "INVOICE": MessageLookupByLibrary.simpleMessage("فاتورة"),
        "MOBIPOS": MessageLookupByLibrary.simpleMessage("Pos Saas"),
        "POSSale": MessageLookupByLibrary.simpleMessage("بيع نقطة البيع"),
        "PRICE": MessageLookupByLibrary.simpleMessage("السعر"),
        "PRODUCTNAME": MessageLookupByLibrary.simpleMessage("اسم المنتج"),
        "PosSaasLoginPanel": MessageLookupByLibrary.simpleMessage(
            "لوحة تسجيل الدخول لنظام Pos Saas"),
        "QTY": MessageLookupByLibrary.simpleMessage("الكمية"),
        "Quantity": MessageLookupByLibrary.simpleMessage("الكمية*"),
        "STATUS": MessageLookupByLibrary.simpleMessage("الحالة"),
        "TOTALVALUE": MessageLookupByLibrary.simpleMessage("القيمة الإجمالية"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("عنوان المستخدم"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "accountName": MessageLookupByLibrary.simpleMessage("اسم الحساب"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("رقم الحساب"),
        "action": MessageLookupByLibrary.simpleMessage("عمل"),
        "add": MessageLookupByLibrary.simpleMessage("إضافة"),
        "addBrand": MessageLookupByLibrary.simpleMessage("إضافة علامة تجارية"),
        "addCategory": MessageLookupByLibrary.simpleMessage("إضافة فئة"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("إضافة عميل"),
        "addDescription": MessageLookupByLibrary.simpleMessage("إضافة وصف...."),
        "addDucument": MessageLookupByLibrary.simpleMessage("إضافة مستندات"),
        "addItem": MessageLookupByLibrary.simpleMessage("إضافة عنصر"),
        "addItemCategory":
            MessageLookupByLibrary.simpleMessage("إضافة فئة العنصر"),
        "addNew": MessageLookupByLibrary.simpleMessage("إضافة جديد"),
        "inventorySales": MessageLookupByLibrary.simpleMessage("مبيعات المخزون"),
        "addNewUser": MessageLookupByLibrary.simpleMessage("إضافة مستخدم جديد"),
        "addProduct": MessageLookupByLibrary.simpleMessage("إضافة منتج"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("تمت الإضافة بنجاح"),
        "addSupplier": MessageLookupByLibrary.simpleMessage("إضافة مورد"),
        "addUnit": MessageLookupByLibrary.simpleMessage("إضافة وحدة"),
        "addUpdateExpenseList":
            MessageLookupByLibrary.simpleMessage("إضافة/تحديث قائمة المصروفات"),
        "addUpdateIncomeList":
            MessageLookupByLibrary.simpleMessage("إضافة / تحديث قائمة الدخل"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("إضافة دور المستخدم"),
        "addingSerialNumber":
            MessageLookupByLibrary.simpleMessage("إضافة رقم تسلسلي؟"),
        "address": MessageLookupByLibrary.simpleMessage("عنوان"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "allBasicFeatures":
            MessageLookupByLibrary.simpleMessage("جميع الميزات الأساسية"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("هل لديك حساب بالفعل؟"),
        "amount": MessageLookupByLibrary.simpleMessage("المبلغ"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("دعم تطبيق Android و iOS"),
        "areYouWantToCreateThisQuation": MessageLookupByLibrary.simpleMessage(
            "هل تريد إنشاء هذه Quotation؟"),
        "areYouWantToDeleteThisCustomer":
            MessageLookupByLibrary.simpleMessage("هل تريد حذف هذا العميل؟"),
        "areYouWantToDeleteThisProduct":
            MessageLookupByLibrary.simpleMessage("هل ترغب في حذف هذا المنتج؟"),
        "areYouWantToDeleteThisQuotion": MessageLookupByLibrary.simpleMessage(
            "هل تريد حذف هذه ال quotation؟"),
        "areYouWantToReturnThisSale":
            MessageLookupByLibrary.simpleMessage("هل تريد إرجاع هذه المبيعات؟"),
        "balance": MessageLookupByLibrary.simpleMessage("الرصيد"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("عملة الحساب البنكي"),
        "bankAccounts":
            MessageLookupByLibrary.simpleMessage("الحسابات البنكية"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("معلومات البنك"),
        "bankName": MessageLookupByLibrary.simpleMessage("اسم البنك"),
        "between": MessageLookupByLibrary.simpleMessage("بين"),
        "billTo": MessageLookupByLibrary.simpleMessage("فاتورة إلى:"),
        "branchName": MessageLookupByLibrary.simpleMessage("اسم الفرع"),
        "brand": MessageLookupByLibrary.simpleMessage("علامة تجارية"),
        "brandName":
            MessageLookupByLibrary.simpleMessage("اسم العلامة التجارية"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("تصنيف الأعمال"),
        "buy": MessageLookupByLibrary.simpleMessage("شراء"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("شراء خطة مميزة"),
        "buySms": MessageLookupByLibrary.simpleMessage("شراء الرسائل النصية"),
        "calculator": MessageLookupByLibrary.simpleMessage("آلة حاسبة:"),
        "camera": MessageLookupByLibrary.simpleMessage("كاميرا"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "capacity": MessageLookupByLibrary.simpleMessage("سعة"),
        "cashAndBank": MessageLookupByLibrary.simpleMessage("النقد والبنك"),
        "cashInHand": MessageLookupByLibrary.simpleMessage("النقد في اليد"),
        "categories": MessageLookupByLibrary.simpleMessage("الفئات"),
        "category": MessageLookupByLibrary.simpleMessage("الفئة"),
        "categoryName": MessageLookupByLibrary.simpleMessage("اسم الفئة"),
        "changeAmount": MessageLookupByLibrary.simpleMessage("تغيير المبلغ"),
        "changeableAmount":
            MessageLookupByLibrary.simpleMessage("المبلغ القابل للتغيير"),
        "checkWarranty": MessageLookupByLibrary.simpleMessage("تحقق من الضمان"),
        "choseAplan": MessageLookupByLibrary.simpleMessage("اختر خطة"),
        "collectDue": MessageLookupByLibrary.simpleMessage("تحصيل المستحقات >"),
        "color": MessageLookupByLibrary.simpleMessage("لون"),
        "comapnyName": MessageLookupByLibrary.simpleMessage("اسم الشركة"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("عنوان الشركة"),
        "companyDescription":
            MessageLookupByLibrary.simpleMessage("وصف الشركة"),
        "companyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "عنوان البريد الإلكتروني للشركة"),
        "companyName": MessageLookupByLibrary.simpleMessage("اسم الشركة"),
        "companyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("رقم هاتف الشركة"),
        "companyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("عنوان URL لموقع الشركة"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("تأكيد كلمة المرور"),
        "continu": MessageLookupByLibrary.simpleMessage("استمر"),
        "convertToSale": MessageLookupByLibrary.simpleMessage("تحويل إلى بيع"),
        "create": MessageLookupByLibrary.simpleMessage("إنشاء"),
        "createPayment": MessageLookupByLibrary.simpleMessage("إنشاء دفعة"),
        "createdBy": MessageLookupByLibrary.simpleMessage("تم الإنشاء بواسطة"),
        "creativeHub": MessageLookupByLibrary.simpleMessage("مركز الإبداع"),
        "currency": MessageLookupByLibrary.simpleMessage("العملة"),
        "currentPlan": MessageLookupByLibrary.simpleMessage("خطة حالية"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("تخصيص فواتيرك"),
        "customer": MessageLookupByLibrary.simpleMessage("العميل"),
        "customerDue": MessageLookupByLibrary.simpleMessage("مستحق العميل"),
        "customerInvoices":
            MessageLookupByLibrary.simpleMessage("فواتير العملاء"),
        "customerList": MessageLookupByLibrary.simpleMessage("قائمة العملاء"),
        "customerName": MessageLookupByLibrary.simpleMessage("اسم العميل"),
        "customerOfTheMonth":
            MessageLookupByLibrary.simpleMessage("عميل الشهر"),
        "customerType": MessageLookupByLibrary.simpleMessage("نوع العميل"),
        "customerWalkIncostomer":
            MessageLookupByLibrary.simpleMessage("العميل: عميل قادم"),
        "customers": MessageLookupByLibrary.simpleMessage("العملاء"),
        "dailyCollection": MessageLookupByLibrary.simpleMessage("الجمع اليومي"),
        "dailySales": MessageLookupByLibrary.simpleMessage("المبيعات اليومية"),
        "dailyTransaction":
            MessageLookupByLibrary.simpleMessage("المعاملة اليومية"),
        "dashBoard": MessageLookupByLibrary.simpleMessage("لوحة القيادة"),
        "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
        "dateTime": MessageLookupByLibrary.simpleMessage("التاريخ والوقت"),
        "dealer": MessageLookupByLibrary.simpleMessage("تاجر"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("سعر التاجر"),
        "delete": MessageLookupByLibrary.simpleMessage("حذف"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("رسوم التوصيل"),
        "description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "details": MessageLookupByLibrary.simpleMessage("التفاصيل>"),
        "discount": MessageLookupByLibrary.simpleMessage("خصم"),
        "discountPrice": MessageLookupByLibrary.simpleMessage("سعر الخصم"),
        "downloadPDF": MessageLookupByLibrary.simpleMessage("تنزيل PDF"),
        "due": MessageLookupByLibrary.simpleMessage("مستحق"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("المبلغ المستحق"),
        "dueAmountWillShowHere": MessageLookupByLibrary.simpleMessage(
            "سيتم عرض المبلغ المستحق هنا إذا كان متاحًا"),
        "dueCollection": MessageLookupByLibrary.simpleMessage("جمع الديون"),
        "dueList": MessageLookupByLibrary.simpleMessage("قائمة المستحقات"),
        "dueTransaction":
            MessageLookupByLibrary.simpleMessage("المعاملة المستحقة"),
        "edit": MessageLookupByLibrary.simpleMessage("تعديل"),
        "editOrAddSerial":
            MessageLookupByLibrary.simpleMessage("تعديل / إضافة رقم تسلسلي:"),
        "editYourProfile":
            MessageLookupByLibrary.simpleMessage("تعديل ملفك الشخصي"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("أدخل المبلغ"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم العلامة التجارية"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم الفئة"),
        "enterCompanyDesciption":
            MessageLookupByLibrary.simpleMessage("أدخل وصف الشركة"),
        "enterCompanyEmailAddress": MessageLookupByLibrary.simpleMessage(
            "أدخل عنوان البريد الإلكتروني للشركة"),
        "enterCompanyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم هاتف الشركة"),
        "enterCompanyWebsiteUrl":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان URL لموقع الشركة"),
        "enterCustomerName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم العميل"),
        "enterDealePrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر التاجر"),
        "enterDiscountPrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر الخصم"),
        "enterExpanseCategory":
            MessageLookupByLibrary.simpleMessage("أدخل فئة المصروفات"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("أدخل تاريخ المصروفات"),
        "enterIncomeCategory":
            MessageLookupByLibrary.simpleMessage("أدخل فئة الدخل"),
        "enterIncomeDate":
            MessageLookupByLibrary.simpleMessage("أدخل تاريخ الدخل"),
        "enterManufacturerName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم الشركة المصنعة"),
        "enterName": MessageLookupByLibrary.simpleMessage("أدخل الاسم"),
        "enterNames": MessageLookupByLibrary.simpleMessage("أدخل الاسم"),
        "enterNote": MessageLookupByLibrary.simpleMessage("أدخل ملاحظة"),
        "enterOpeningBalance":
            MessageLookupByLibrary.simpleMessage("أدخل رصيد الافتتاح"),
        "enterPaidAmount":
            MessageLookupByLibrary.simpleMessage("أدخل المبلغ المدفوع"),
        "enterPassword":
            MessageLookupByLibrary.simpleMessage("أدخل كلمة المرور"),
        "enterPayingAmount":
            MessageLookupByLibrary.simpleMessage("أدخل مبلغ الدفع"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("أدخل السعر"),
        "enterProductCapacity":
            MessageLookupByLibrary.simpleMessage("أدخل سعة المنتج"),
        "enterProductCode":
            MessageLookupByLibrary.simpleMessage("أدخل رمز المنتج"),
        "enterProductColor":
            MessageLookupByLibrary.simpleMessage("أدخل لون المنتج"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم المنتج"),
        "enterProductQuantity":
            MessageLookupByLibrary.simpleMessage("أدخل كمية المنتج"),
        "enterProductSize":
            MessageLookupByLibrary.simpleMessage("أدخل حجم المنتج"),
        "enterProductType":
            MessageLookupByLibrary.simpleMessage("أدخل نوع المنتج"),
        "enterProductUnit":
            MessageLookupByLibrary.simpleMessage("أدخل وحدة المنتج"),
        "enterProductWeight":
            MessageLookupByLibrary.simpleMessage("أدخل وزن المنتج"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر الشراء"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم المرجع"),
        "enterSalePrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر البيع"),
        "enterSerialNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم السلسلة"),
        "enterSmsContent":
            MessageLookupByLibrary.simpleMessage("أدخل محتوى الرسالة"),
        "enterStockAmount":
            MessageLookupByLibrary.simpleMessage("أدخل كمية المخزون"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("أدخل معرف المعاملة"),
        "enterUnitName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم الوحدة"),
        "enterUserRoleName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم دور المستخدم"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان المستخدم"),
        "enterWarranty": MessageLookupByLibrary.simpleMessage("أدخل الضمان"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر الجملة"),
        "enterYOurAmount":
            MessageLookupByLibrary.simpleMessage("أدخل المبلغ الخاص بك"),
        "enterYourAddress": MessageLookupByLibrary.simpleMessage("أدخل عنوانك"),
        "enterYourCompanyAddress":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان شركتك"),
        "enterYourCompanyName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم شركتك"),
        "enterYourCompanyNames":
            MessageLookupByLibrary.simpleMessage("أدخل اسم شركتك"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان بريدك الإلكتروني"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("أدخل كلمة المرور الخاصة بك"),
        "enterYourPasswordAgain": MessageLookupByLibrary.simpleMessage(
            "أدخل كلمة المرور الخاصة بك مرة أخرى"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم هاتفك"),
        "enterYourShopName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم متجرك"),
        "entercategoryName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم الفئة"),
        "expense": MessageLookupByLibrary.simpleMessage("مصروف"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("تاريخ المصروفات"),
        "expenseDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل المصروفات"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("المصروفات"),
        "expensecategoryList":
            MessageLookupByLibrary.simpleMessage("قائمة فئات المصروفات"),
        "expenses": MessageLookupByLibrary.simpleMessage("المصروفات"),
        "fivePurchase": MessageLookupByLibrary.simpleMessage(
            "أفضل خمس منتجات تم شراؤها خلال الشهر"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("للاستخدام غير المحدود"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("هل نسيت كلمة المرور؟"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("نسخ احتياطي مجاني للبيانات"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("تحديث مجاني مدى الحياة"),
        "freePackage": MessageLookupByLibrary.simpleMessage("حزمة مجانية"),
        "freePlan": MessageLookupByLibrary.simpleMessage("خطة مجانية"),
        "getStarted": MessageLookupByLibrary.simpleMessage("ابدأ"),
        "govermentId": MessageLookupByLibrary.simpleMessage("هوية حكومية"),
        "grandTotal": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "hold": MessageLookupByLibrary.simpleMessage("احتفظ"),
        "holdNumber": MessageLookupByLibrary.simpleMessage("رقم الحجز"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("التحقق من الهوية"),
        "inc": MessageLookupByLibrary.simpleMessage("الدخل"),
        "income": MessageLookupByLibrary.simpleMessage("الدخل"),
        "incomeCategory": MessageLookupByLibrary.simpleMessage("فئة الدخل"),
        "incomeCategoryList":
            MessageLookupByLibrary.simpleMessage("قائمة فئات الدخل"),
        "incomeDate": MessageLookupByLibrary.simpleMessage("تاريخ الدخل"),
        "incomeDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الدخل"),
        "incomeFor": MessageLookupByLibrary.simpleMessage("للدخل"),
        "incomeList": MessageLookupByLibrary.simpleMessage("قائمة الدخل"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("زيادة المخزون"),
        "instantPrivacy": MessageLookupByLibrary.simpleMessage("خصوصية فورية"),
        "invoice": MessageLookupByLibrary.simpleMessage("فاتورة"),
        "invoiceCo": MessageLookupByLibrary.simpleMessage("فاتورة:"),
        "invoiceHint": MessageLookupByLibrary.simpleMessage("رقم الفاتورة..."),
        "invoiceNo": MessageLookupByLibrary.simpleMessage("رقم الفاتورة"),
        "item": MessageLookupByLibrary.simpleMessage("عنصر"),
        "itemName": MessageLookupByLibrary.simpleMessage("اسم العنصر"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("التحقق من KYC"),
        "ledgeDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل الميزانية العمومية"),
        "ledger": MessageLookupByLibrary.simpleMessage("دفتر الأستاذ"),
        "left": MessageLookupByLibrary.simpleMessage("يسار"),
        "loanAccounts": MessageLookupByLibrary.simpleMessage("حسابات القروض"),
        "logOut": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "logoPositionInInvoice":
            MessageLookupByLibrary.simpleMessage("موضع الشعار في الفاتورة؟"),
        "loss": MessageLookupByLibrary.simpleMessage("الخسارة"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("خسارة / ربح"),
        "lossminus": MessageLookupByLibrary.simpleMessage("خسارة (-)"),
        "lowStock": MessageLookupByLibrary.simpleMessage("مخزون منخفض"),
        "lowStocks": MessageLookupByLibrary.simpleMessage("مخزونات منخفضة"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "اترك انطباعًا دائمًا على عملائك من خلال الفواتير المميزة بعلامتك التجارية. توفر ترقية غير محدودة ميزة فريدة من نوعها لتخصيص الفواتير الخاصة بك، مما يضيف لمسة احترافية تعزز هويتك العلامية وتعزز ولاءة العملاء."),
        "manufacturer": MessageLookupByLibrary.simpleMessage("الشركة المصنعة"),
        "mobiPosLoginPanel":
            MessageLookupByLibrary.simpleMessage("لوحة تسجيل دخول Pos Saas"),
        "mobiPosSignUpPane":
            MessageLookupByLibrary.simpleMessage("لوحة تسجيل Pos Saas"),
        "mobilePlusDesktop":
            MessageLookupByLibrary.simpleMessage("تطبيق الجوال\n+\nسطح المكتب"),
        "moneyReciept": MessageLookupByLibrary.simpleMessage("إيصال مالي"),
        "nam": MessageLookupByLibrary.simpleMessage("الاسم*"),
        "name": MessageLookupByLibrary.simpleMessage("الاسم"),
        "nameCodeOrCateogry":
            MessageLookupByLibrary.simpleMessage("الاسم أو الرمز أو الفئة"),
        "newCusotmers": MessageLookupByLibrary.simpleMessage("عملاء جدد"),
        "newCustomers": MessageLookupByLibrary.simpleMessage("عملاء جدد"),
        "newIncome": MessageLookupByLibrary.simpleMessage("دخل جديد"),
        "no": MessageLookupByLibrary.simpleMessage("لا"),
        "noConnection": MessageLookupByLibrary.simpleMessage("لا يوجد اتصال"),
        "noCustomerFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على أي عميل"),
        "noDueTransantionFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على أي معاملة مستحقة"),
        "noExpenseCategoryFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على أي فئة مصروفات"),
        "noIncomeCategoryFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على أي فئة دخل"),
        "noIncomeFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على أي دخل"),
        "noInvoiceFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على فاتورة"),
        "noProductFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على منتج"),
        "noPurchaseTransactionFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على معاملة شراء"),
        "noQuotionFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على quotation"),
        "noReportFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على أي تقرير"),
        "noSaleTransaactionFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على معاملة بيع"),
        "noSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على أي رقم تسلسلي"),
        "noSupplierFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على مورد"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على أي معاملة"),
        "noUserFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على مستخدم"),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على دور المستخدم"),
        "nosSerialNumberFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على أي رقم تسلسلي"),
        "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "openCheques": MessageLookupByLibrary.simpleMessage("الشيكات المفتوحة"),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("الرصيد الافتتاحي"),
        "orDragAndDropPng":
            MessageLookupByLibrary.simpleMessage("أو اسحب وأفلت PNG و JPG"),
        "orders": MessageLookupByLibrary.simpleMessage("الطلبات"),
        "other": MessageLookupByLibrary.simpleMessage("آخر"),
        "otherIncome": MessageLookupByLibrary.simpleMessage("دخل آخر"),
        "packageFeature": MessageLookupByLibrary.simpleMessage("ميزة الحزمة"),
        "paid": MessageLookupByLibrary.simpleMessage("مدفوع"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("المبلغ المدفوع"),
        "partyName": MessageLookupByLibrary.simpleMessage("اسم العميل"),
        "partyType": MessageLookupByLibrary.simpleMessage("نوع العميل"),
        "password": MessageLookupByLibrary.simpleMessage("كلمه السر"),
        "payCash": MessageLookupByLibrary.simpleMessage("الدفع نقدًا"),
        "payable": MessageLookupByLibrary.simpleMessage("مستحق الدفع"),
        "payingAmount": MessageLookupByLibrary.simpleMessage("المبلغ المدفوع"),
        "payment": MessageLookupByLibrary.simpleMessage("دفعة"),
        "paymentIn": MessageLookupByLibrary.simpleMessage("الدفع الداخل"),
        "paymentOut": MessageLookupByLibrary.simpleMessage("الدفع الخارج"),
        "paymentType": MessageLookupByLibrary.simpleMessage("نوع الدفع"),
        "paymentTypes": MessageLookupByLibrary.simpleMessage("نوع الدفع"),
        "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
        "phoneVerification":
            MessageLookupByLibrary.simpleMessage("التحقق من الهاتف"),
        "pleaseAddASale":
            MessageLookupByLibrary.simpleMessage("يرجى إضافة مبيعات"),
        "pleaseAddCustomer":
            MessageLookupByLibrary.simpleMessage("يرجى إضافة عميل"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "الرجاء التحقق من اتصالك بالإنترنت"),
        "pleaseDownloadOurMobileApp": MessageLookupByLibrary.simpleMessage(
            "يرجى تنزيل تطبيقنا المحمول والاشتراك في حزمة لاستخدام الإصدار المكتبي"),
        "pleaseEnterProductStock":
            MessageLookupByLibrary.simpleMessage("الرجاء إدخال مخزون المنتج"),
        "pleaseEnterValidData":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال بيانات صالحة"),
        "pleaseSelectACustomer":
            MessageLookupByLibrary.simpleMessage("يرجى تحديد عميل"),
        "pleaseentervaliddata":
            MessageLookupByLibrary.simpleMessage("الرجاء إدخال بيانات صالحة"),
        "posSaasSingUpPanel":
            MessageLookupByLibrary.simpleMessage("لوحة التسجيل لنظام Pos Saas"),
        "practies": MessageLookupByLibrary.simpleMessage("تمارين"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("دعم عملاء مميز"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("خطة مميزة"),
        "preview": MessageLookupByLibrary.simpleMessage("معاينة"),
        "previousDue": MessageLookupByLibrary.simpleMessage("السابق مستحق:"),
        "price": MessageLookupByLibrary.simpleMessage("السعر"),
        "print": MessageLookupByLibrary.simpleMessage("طباعة"),
        "printInvoice": MessageLookupByLibrary.simpleMessage("طباعة الفاتورة"),
        "printPdf": MessageLookupByLibrary.simpleMessage("طباعة PDF"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
        "product": MessageLookupByLibrary.simpleMessage("منتج"),
        "productCategory": MessageLookupByLibrary.simpleMessage("فئة المنتج"),
        "productCod": MessageLookupByLibrary.simpleMessage("رمز المنتج*"),
        "productColor": MessageLookupByLibrary.simpleMessage("لون المنتج"),
        "productList": MessageLookupByLibrary.simpleMessage("قائمة المنتجات"),
        "productNam": MessageLookupByLibrary.simpleMessage("اسم المنتج*"),
        "productName": MessageLookupByLibrary.simpleMessage("اسم المنتج"),
        "productSize": MessageLookupByLibrary.simpleMessage("حجم المنتج"),
        "productStock": MessageLookupByLibrary.simpleMessage("مخزون المنتج"),
        "productType": MessageLookupByLibrary.simpleMessage("نوع المنتج"),
        "productUnit": MessageLookupByLibrary.simpleMessage("وحدة المنتج"),
        "productWaranty": MessageLookupByLibrary.simpleMessage("ضمان المنتج"),
        "productWeight": MessageLookupByLibrary.simpleMessage("وزن المنتج"),
        "productcapacity": MessageLookupByLibrary.simpleMessage("سعة المنتج"),
        "prof": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("تحرير الملف الشخصي"),
        "profit": MessageLookupByLibrary.simpleMessage("الربح"),
        "profitMinus": MessageLookupByLibrary.simpleMessage("خسارة (-)"),
        "profitPlus": MessageLookupByLibrary.simpleMessage("ربح (+)"),
        "purchase": MessageLookupByLibrary.simpleMessage("شراء"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("قائمة المشتريات"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("شراء خطة مميزة"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("سعر الشراء"),
        "purchaseTransaction":
            MessageLookupByLibrary.simpleMessage("معاملة الشراء"),
        "quantity": MessageLookupByLibrary.simpleMessage("كمية"),
        "quotation": MessageLookupByLibrary.simpleMessage("عرض السعر"),
        "quotationList": MessageLookupByLibrary.simpleMessage("قائمة الأسعار"),
        "recentSale": MessageLookupByLibrary.simpleMessage("المبيعات الحديثة"),
        "recivedAmount": MessageLookupByLibrary.simpleMessage("المبلغ المستلم"),
        "referenceNo": MessageLookupByLibrary.simpleMessage("رقم المرجع"),
        "referenceNumber": MessageLookupByLibrary.simpleMessage("رقم المرجع"),
        "registration": MessageLookupByLibrary.simpleMessage("التسجيل"),
        "remaining": MessageLookupByLibrary.simpleMessage("المتبقي: "),
        "remainingBalance":
            MessageLookupByLibrary.simpleMessage("الرصيد المتبقي"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("المستحق المتبقي"),
        "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
        "resetYourPassword": MessageLookupByLibrary.simpleMessage(
            "إعادة تعيين كلمة المرور الخاصة بك"),
        "retailer": MessageLookupByLibrary.simpleMessage("تاجر تجزئة"),
        "revenue": MessageLookupByLibrary.simpleMessage("الإيرادات"),
        "right": MessageLookupByLibrary.simpleMessage("يمين"),
        "sAmount": MessageLookupByLibrary.simpleMessage("مبلغ المبيعات"),
        "safegurardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "حماية بيانات عملك بسهولة. تتضمن ترقية Pos Saas POS Unlimited لدينا نسخة احتياطية مجانية للبيانات، مما يضمن حماية معلوماتك القيمة من أي أحداث غير متوقعة. ركز على ما يهم حقًا - نمو عملك."),
        "sale": MessageLookupByLibrary.simpleMessage("البيع"),
        "saleAmount": MessageLookupByLibrary.simpleMessage("مبلغ المبيعات"),
        "saleDetails": MessageLookupByLibrary.simpleMessage("تفاصيل المبيعات"),
        "saleList": MessageLookupByLibrary.simpleMessage("قائمة المبيعات"),
        "salePrice": MessageLookupByLibrary.simpleMessage("سعر البيع"),
        "salePrices": MessageLookupByLibrary.simpleMessage("سعر البيع*"),
        "saleReturn": MessageLookupByLibrary.simpleMessage("إرجاع المبيعات"),
        "saleTransaction": MessageLookupByLibrary.simpleMessage("معاملة البيع"),
        "saleTransactionQuatationHistory": MessageLookupByLibrary.simpleMessage(
            "معاملات البيع (تاريخ مبيعات Quotation)"),
        "sales": MessageLookupByLibrary.simpleMessage("مبيعات"),
        "salesList": MessageLookupByLibrary.simpleMessage("قائمة المبيعات"),
        "saveAndPublish": MessageLookupByLibrary.simpleMessage("حفظ ونشر"),
        "saveAndPublished": MessageLookupByLibrary.simpleMessage("حفظ ونشر"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "search": MessageLookupByLibrary.simpleMessage("بحث......."),
        "searchAnyThing":
            MessageLookupByLibrary.simpleMessage("البحث عن أي شيء..."),
        "searchByInvoice":
            MessageLookupByLibrary.simpleMessage("البحث عن طريق الفاتورة...."),
        "searchByInvoiceOrName": MessageLookupByLibrary.simpleMessage(
            "البحث عن طريق الفاتورة أو الاسم"),
        "searchByName": MessageLookupByLibrary.simpleMessage("بحث بالاسم"),
        "searchByNameOrPhone":
            MessageLookupByLibrary.simpleMessage("البحث بالاسم أو الهاتف..."),
        "searchSerialNumber":
            MessageLookupByLibrary.simpleMessage("بحث رقم السلسلة"),
        "selectParties": MessageLookupByLibrary.simpleMessage("حدد الأطراف"),
        "selectProductBrand":
            MessageLookupByLibrary.simpleMessage("حدد علامة تجارية للمنتج"),
        "selectSerialNumber":
            MessageLookupByLibrary.simpleMessage("حدد رقم السلسلة"),
        "selectVariations":
            MessageLookupByLibrary.simpleMessage("حدد الاختلافات:"),
        "selectWarrantyTime":
            MessageLookupByLibrary.simpleMessage("حدد وقت الضمان"),
        "selectYourLanguage": MessageLookupByLibrary.simpleMessage("اختر لغتك"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("إرسال رسالة"),
        "serialNumber": MessageLookupByLibrary.simpleMessage("رقم السلسلة"),
        "serialNumbers": MessageLookupByLibrary.simpleMessage("رقم التسلسل"),
        "serviceCharge": MessageLookupByLibrary.simpleMessage("رسوم الخدمة"),
        "setting": MessageLookupByLibrary.simpleMessage("إعداد"),
        "share": MessageLookupByLibrary.simpleMessage("المشاركة"),
        "shipingOrOther": MessageLookupByLibrary.simpleMessage("الشحن/آخر"),
        "shopName": MessageLookupByLibrary.simpleMessage("اسم المحل"),
        "shopOpeningBalance":
            MessageLookupByLibrary.simpleMessage("رصيد فتح المحل"),
        "show": MessageLookupByLibrary.simpleMessage("عرض>"),
        "showLogoInInvoice":
            MessageLookupByLibrary.simpleMessage("إظهار الشعار في الفاتورة؟"),
        "shpingOrServices":
            MessageLookupByLibrary.simpleMessage("الشحن / الخدمات"),
        "size": MessageLookupByLibrary.simpleMessage("حجم"),
        "statistic": MessageLookupByLibrary.simpleMessage("إحصائيات"),
        "status": MessageLookupByLibrary.simpleMessage("الحالة"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "ابق على طليعة التطورات التكنولوجية دون تكاليف إضافية. تأكد من أن لديك دائمًا أحدث الأدوات والميزات بين يديك من خلال ترقية Pos Saas POS Unlimited الخاصة بنا، مما يضمن أن تبقى عملك حديثًا."),
        "stayAtTheForeFrontOfTechnological": MessageLookupByLibrary.simpleMessage(
            "ابق على طليعة التقنيات دون أي تكاليف إضافية. تأكد من أن لديك دائمًا أحدث الأدوات والميزات بين يديك من خلال ترقية Pos Sass POS Unlimited الخاصة بنا، مما يضمن أن تبقى عملك حديثًا."),
        "stock": MessageLookupByLibrary.simpleMessage("مخزون"),
        "stockInventory": MessageLookupByLibrary.simpleMessage("جرد المخزون"),
        "stockReport": MessageLookupByLibrary.simpleMessage("تقرير المخزون"),
        "stockValue": MessageLookupByLibrary.simpleMessage("قيمة المخزون"),
        "stockValues": MessageLookupByLibrary.simpleMessage("قيمة المخزون"),
        "subTotal": MessageLookupByLibrary.simpleMessage("الإجمالي الفرعي"),
        "subciption": MessageLookupByLibrary.simpleMessage("الاشتراك"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "supplier": MessageLookupByLibrary.simpleMessage("المورد"),
        "supplierDue": MessageLookupByLibrary.simpleMessage("مستحق المورد"),
        "supplierInvoice":
            MessageLookupByLibrary.simpleMessage("فاتورة المورد"),
        "supplierList": MessageLookupByLibrary.simpleMessage("قائمة الموردين"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("رمز SWIFT"),
        "tSale": MessageLookupByLibrary.simpleMessage("إجمالي المبيعات"),
        "takeADriveLisense": MessageLookupByLibrary.simpleMessage(
            "خذ صورة لرخصة القيادة أو بطاقة الهوية الوطنية أو جواز السفر"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("شروط الاستخدام"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "الاسم يقول كل شيء. مع Pos Saas POS Unlimited، لا يوجد قيود على استخدامك. سواء كنت تعالج عددًا قليلًا من المعاملات أو تواجه اندفاعًا من العملاء، يمكنك التشغيل بثقة، علمًا بأنك لا تقتصر عن حدود."),
        "thisCustmerHasNoDue":
            MessageLookupByLibrary.simpleMessage("هذا العميل ليس لديه مستحقات"),
        "thisCustomerHavepreviousDue": MessageLookupByLibrary.simpleMessage(
            "هذا العميل لديه مستحقات سابقة"),
        "to": MessageLookupByLibrary.simpleMessage("إلى"),
        "topSellingProduct":
            MessageLookupByLibrary.simpleMessage("أعلى منتج مبيعًا"),
        "total": MessageLookupByLibrary.simpleMessage("مجموع"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("المبلغ الإجمالي"),
        "totalDiscount": MessageLookupByLibrary.simpleMessage("إجمالي الخصم"),
        "totalDue": MessageLookupByLibrary.simpleMessage("المستحقات الإجمالية"),
        "totalDues": MessageLookupByLibrary.simpleMessage("المستحقات الكلية"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("المصروفات الإجمالية"),
        "totalIncome": MessageLookupByLibrary.simpleMessage("إجمالي الدخل"),
        "totalItem2":
            MessageLookupByLibrary.simpleMessage("العدد الكلي للعناصر: 2"),
        "totalLoss": MessageLookupByLibrary.simpleMessage("إجمالي الخسارة"),
        "totalPaid": MessageLookupByLibrary.simpleMessage("الإجمالي المدفوع"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("مستحق الدفع الإجمالي"),
        "totalPaymentOut":
            MessageLookupByLibrary.simpleMessage("إجمالي الدفع الخارج"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("السعر الإجمالي"),
        "totalProduct": MessageLookupByLibrary.simpleMessage("إجمالي المنتج"),
        "totalProfit": MessageLookupByLibrary.simpleMessage("إجمالي الربح"),
        "totalPurchase":
            MessageLookupByLibrary.simpleMessage("إجمالي المشتريات"),
        "totalReturnAmount":
            MessageLookupByLibrary.simpleMessage("إجمالي مبلغ الإرجاع"),
        "totalReturns": MessageLookupByLibrary.simpleMessage("إجمالي العوائد"),
        "totalSale": MessageLookupByLibrary.simpleMessage("إجمالي المبيعات"),
        "totalSales": MessageLookupByLibrary.simpleMessage("إجمالي المبيعات"),
        "totalVat":
            MessageLookupByLibrary.simpleMessage("إجمالي ضريبة القيمة المضافة"),
        "totalpaymentIn":
            MessageLookupByLibrary.simpleMessage("إجمالي الدفع الداخل"),
        "transaction": MessageLookupByLibrary.simpleMessage("المعاملة"),
        "transactionId": MessageLookupByLibrary.simpleMessage("معرف المعاملة"),
        "transactionReport":
            MessageLookupByLibrary.simpleMessage("تقرير المعاملة"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("حاول مرة أخرى"),
        "type": MessageLookupByLibrary.simpleMessage("اكتب"),
        "unPaid": MessageLookupByLibrary.simpleMessage("غير مدفوع"),
        "unit": MessageLookupByLibrary.simpleMessage("وحدة"),
        "unitName": MessageLookupByLibrary.simpleMessage("اسم الوحدة"),
        "unitPrice": MessageLookupByLibrary.simpleMessage("سعر الوحدة"),
        "unlimited": MessageLookupByLibrary.simpleMessage("غير محدود"),
        "unlimitedInvoice":
            MessageLookupByLibrary.simpleMessage("فواتير غير محدودة"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("استخدام غير محدود"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "استفد من الإمكانيات الكاملة لـ Pos Saas POS من خلال جلسات تدريب شخصية يقودها فريق الخبراء لدينا. من الأساسيات إلى التقنيات المتقدمة، نحن نضمن أنك ملم بشكل جيد في استخدام كل جانب من جوانب النظام لتحسين عمليات عملك."),
        "updateNow": MessageLookupByLibrary.simpleMessage("تحديث الآن"),
        "updateYourPlanFirst": MessageLookupByLibrary.simpleMessage(
            "قم بتحديث خطتك أولاً \\ n الحد الأقصى للبيع قد انتهى."),
        "upgradeOnMobileApp":
            MessageLookupByLibrary.simpleMessage("الترقية على تطبيق الجوال"),
        "uploadAImage": MessageLookupByLibrary.simpleMessage("تحميل صورة"),
        "uploadAnInvoiceLogo":
            MessageLookupByLibrary.simpleMessage("تحميل شعار الفاتورة"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("تحميل وثيقة"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("تحميل ملف"),
        "userName": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
        "userRole": MessageLookupByLibrary.simpleMessage("دور المستخدم"),
        "userRoleName":
            MessageLookupByLibrary.simpleMessage("اسم دور المستخدم"),
        "userTitle": MessageLookupByLibrary.simpleMessage("عنوان المستخدم"),
        "vatOrgst": MessageLookupByLibrary.simpleMessage(
            "ضريبة القيمة المضافة / ضريبة السلع والخدمات"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("تحقق من رقم الهاتف"),
        "view": MessageLookupByLibrary.simpleMessage("رأي"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("عميل قادم"),
        "warranty": MessageLookupByLibrary.simpleMessage("ضمان"),
        "warrantys": MessageLookupByLibrary.simpleMessage("الضمانات"),
        "weNeedToRegisterYourPhone": MessageLookupByLibrary.simpleMessage(
            "نحن بحاجة إلى تسجيل هاتفك قبل البدء!"),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "نحن نفهم أهمية العمليات السلسة. لذلك، دعمنا على مدار الساعة متاح لمساعدتك، سواء كانت استفسارات سريعة أو مخاوف شاملة. اتصل بنا في أي وقت وأي مكان عبر المكالمة أو WhatsApp لتجربة خدمة العملاء غير المضاهى."),
        "wholeSaleprice": MessageLookupByLibrary.simpleMessage("سعر الجملة"),
        "wholeSeller": MessageLookupByLibrary.simpleMessage("تاجر جملة"),
        "wholesale": MessageLookupByLibrary.simpleMessage("الجملة"),
        "wight": MessageLookupByLibrary.simpleMessage("وزن"),
        "yesReturn": MessageLookupByLibrary.simpleMessage("نعم إرجاع"),
        "youHaveToRelogin": MessageLookupByLibrary.simpleMessage(
            "يجب عليك إعادة تسجيل الدخول إلى حسابك."),
        "youNeedToIdentityVerifySms": MessageLookupByLibrary.simpleMessage(
            "تحتاج إلى التحقق من هويتك قبل شراء الرسائل"),
        "yourAllSaleList":
            MessageLookupByLibrary.simpleMessage("قائمة جميع مبيعاتك"),
        "yourAllSales": MessageLookupByLibrary.simpleMessage("جميع مبيعاتك"),
        "yourAreUsing": MessageLookupByLibrary.simpleMessage("أنت تستخدم"),
        "yourDueSales":
            MessageLookupByLibrary.simpleMessage("مبيعاتك المستحقة"),
        "yourNeedToIdentityVerify": MessageLookupByLibrary.simpleMessage(
            "تحتاج إلى التحقق من هويتك قبل شراء الرسائل"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("حزمتك"),
        "yourPaymentIsCancelled":
            MessageLookupByLibrary.simpleMessage("تم إلغاء دفعتك"),
        "yourPaymentIsSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم دفعك بنجاح"),
        "yourPaymentIscancelled":
            MessageLookupByLibrary.simpleMessage("تم إلغاء دفعتك")
      };
}
