// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "MRP": MessageLookupByLibrary.simpleMessage("السعر الأقصى للبيع"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("عنوان المستخدم"),
        "YourPackageWillExpireTodayPleasePurchaseagain":
            MessageLookupByLibrary.simpleMessage(
                "باقتك هتنتهي النهاردة\n\nيرجى الشراء مرة تانية"),
        "aTheSystemIsProvided": MessageLookupByLibrary.simpleMessage(
            "(أ) النظام ده متوفر بس عشان يسهل عمليات نقاط البيع والأنشطة المتعلقة بشغلك."),
        "aToUseTheSystem": MessageLookupByLibrary.simpleMessage(
            "(أ) عشان تستخدم النظام، ممكن يطلب منك تعمل حساب. انت موافق تقدم معلومات دقيقة وحديثة وكاملة أثناء التسجيل وتحدث المعلومات دي عشان تفضل دقيقة وكاملة."),
        "aboutApp": MessageLookupByLibrary.simpleMessage("عن التطبيق"),
        "acceptanceOfTerms":
            MessageLookupByLibrary.simpleMessage("قبول الشروط"),
        "accountName": MessageLookupByLibrary.simpleMessage("اسم الحساب"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("رقم الحساب"),
        "accountRegistration":
            MessageLookupByLibrary.simpleMessage("تسجيل الحساب"),
        "addBrand": MessageLookupByLibrary.simpleMessage("إضافة علامة تجارية"),
        "addCategory": MessageLookupByLibrary.simpleMessage("إضافة فئة"),
        "addContact": MessageLookupByLibrary.simpleMessage("إضافة جهة اتصال"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("إضافة عميل"),
        "addDelivery": MessageLookupByLibrary.simpleMessage("إضافة توصيل"),
        "addDescription": MessageLookupByLibrary.simpleMessage("إضافة وصف"),
        "addDocumentId":
            MessageLookupByLibrary.simpleMessage("إضافة رقم الوثيقة"),
        "addDucument": MessageLookupByLibrary.simpleMessage("إضافة وثيقة"),
        "addExpense": MessageLookupByLibrary.simpleMessage("إضافة مصروف"),
        "addExpenseCategory":
            MessageLookupByLibrary.simpleMessage("إضافة فئة المصروف"),
        "addItems": MessageLookupByLibrary.simpleMessage("إضافة منتجات"),
        "addNewAddress":
            MessageLookupByLibrary.simpleMessage("إضافة عنوان جديد"),
        "addNewProduct":
            MessageLookupByLibrary.simpleMessage("إضافة منتج جديد"),
        "addNote": MessageLookupByLibrary.simpleMessage("إضافة ملاحظة"),
        "addPurchase": MessageLookupByLibrary.simpleMessage("إضافة شراء"),
        "addSales": MessageLookupByLibrary.simpleMessage("إضافة مبيعات"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("تمت الإضافة بنجاح"),
        "addUnit": MessageLookupByLibrary.simpleMessage("إضافة وحدة"),
        "addUserRole":
            MessageLookupByLibrary.simpleMessage("إضافة صلاحيات المستخدم"),
        "address": MessageLookupByLibrary.simpleMessage("العنوان"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "allBusinessSolution":
            MessageLookupByLibrary.simpleMessage("كل حلول الأعمال"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("عندك حساب بالفعل؟"),
        "amount": MessageLookupByLibrary.simpleMessage("المبلغ"),
        "androidIOSAppSupport": MessageLookupByLibrary.simpleMessage(
            "دعم تطبيق الأندرويد وآي أو إس"),
        "apply": MessageLookupByLibrary.simpleMessage("تطبيق"),
        "areYourSureDeleteThisUser": MessageLookupByLibrary.simpleMessage(
            "متأكد انك عايز تحذف المستخدم ده؟"),
        "bYouHaveResponsiveFor": MessageLookupByLibrary.simpleMessage(
            "(ب) انت مسؤول عن الحفاظ على سرية حسابك وكلمة المرور وتقييد الوصول لحسابك. انت تتحمل المسؤولية عن كل الأنشطة اللي بتحصل تحت حسابك."),
        "bYouMustBeAtLeastYearsOld": MessageLookupByLibrary.simpleMessage(
            "(ب) لازم تكون على الأقل 18 سنة أو السن القانوني للبلوغ في منطقتك عشان تستخدم النظام."),
        "backSide": MessageLookupByLibrary.simpleMessage("الجانب الخلفي"),
        "backToHome":
            MessageLookupByLibrary.simpleMessage("الرجوع للصفحة الرئيسية"),
        "balance": MessageLookupByLibrary.simpleMessage("الرصيد"),
        "egypt": MessageLookupByLibrary.simpleMessage("مصر"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("عملة الحساب البنكي"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("معلومات البنك"),
        "bankName": MessageLookupByLibrary.simpleMessage("اسم البنك"),
        "billTo": MessageLookupByLibrary.simpleMessage("فاتورة إلى"),
        "branchName": MessageLookupByLibrary.simpleMessage("اسم الفرع"),
        "brand": MessageLookupByLibrary.simpleMessage("العلامة التجارية"),
        "brandName":
            MessageLookupByLibrary.simpleMessage("اسم العلامة التجارية"),
        "businessCategory": MessageLookupByLibrary.simpleMessage("فئة العمل"),
        "buy": MessageLookupByLibrary.simpleMessage("شراء"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("شراء الباقة المميزة"),
        "buySms": MessageLookupByLibrary.simpleMessage("شراء رسائل نصية"),
        "byAccessingOrUsingThePointOfSales": MessageLookupByLibrary.simpleMessage(
            "باستخدامك لنظام نقاط البيع (POS) اللي بتوفره [اسم شركتك] (الشركة)، انت موافق على الالتزام بالشروط والأحكام دي. لو مش موافق، متستخدمش النظام."),
        "cYouHaveResponsiveForEnsuring": MessageLookupByLibrary.simpleMessage(
            "(ج) انت مسؤول عن التأكد ان وصولك واستخدامك للنظام متوافق مع كل القوانين واللوائح المعمول بيها."),
        "cacel": MessageLookupByLibrary.simpleMessage("خروج"),
        "call": MessageLookupByLibrary.simpleMessage("اتصال"),
        "camera": MessageLookupByLibrary.simpleMessage("الكاميرا"),
        "capacity": MessageLookupByLibrary.simpleMessage("السعة"),
        "cash": MessageLookupByLibrary.simpleMessage("نقدًا"),
        "categories": MessageLookupByLibrary.simpleMessage("الفئات"),
        "category": MessageLookupByLibrary.simpleMessage("الفئة"),
        "cateogryName": MessageLookupByLibrary.simpleMessage("اسم الفئة"),
        "change": MessageLookupByLibrary.simpleMessage("تغيير"),
        "changePassword":
            MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
        "checkEmail":
            MessageLookupByLibrary.simpleMessage("تحقق من البريد الإلكتروني"),
        "choseACustomer": MessageLookupByLibrary.simpleMessage("اختر عميل"),
        "choseASupplier": MessageLookupByLibrary.simpleMessage("اختر مورد"),
        "choseYourFeature": MessageLookupByLibrary.simpleMessage("اختر ميزاتك"),
        "clarence": MessageLookupByLibrary.simpleMessage("كلارنس"),
        "clickToConnect": MessageLookupByLibrary.simpleMessage("انقر للاتصال"),
        "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
        "color": MessageLookupByLibrary.simpleMessage("اللون"),
        "companyAddress": MessageLookupByLibrary.simpleMessage("عنوان الشركة"),
        "companyAndShopName":
            MessageLookupByLibrary.simpleMessage("اسم الشركة والمتجر"),
        "completeTransaction":
            MessageLookupByLibrary.simpleMessage("اكتمال العملية"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("تأكيد كلمة المرور"),
        "congratulations": MessageLookupByLibrary.simpleMessage("تهانينا"),
        "contactUs": MessageLookupByLibrary.simpleMessage("اتصل بنا"),
        "continu": MessageLookupByLibrary.simpleMessage("متابعة"),
        "country": MessageLookupByLibrary.simpleMessage("البلد"),
        "create": MessageLookupByLibrary.simpleMessage("إنشاء"),
        "createAFreeAccounts":
            MessageLookupByLibrary.simpleMessage("إنشاء حساب مجاني"),
        "currency": MessageLookupByLibrary.simpleMessage("العملة"),
        "currentStock": MessageLookupByLibrary.simpleMessage("المخزون الحالي"),
        "customInvoiceBranding": MessageLookupByLibrary.simpleMessage(
            "تخصيص فواتير العلامة التجارية"),
        "customer": MessageLookupByLibrary.simpleMessage("العميل"),
        "customerDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل العميل"),
        "customerName": MessageLookupByLibrary.simpleMessage("اسم العميل"),
        "dailyTransaciton":
            MessageLookupByLibrary.simpleMessage("المعاملات اليومية"),
        "dashBoardOverView": MessageLookupByLibrary.simpleMessage(
            "  نظرة عامة على لوحة التحكم     "),
        "accountSettlement": MessageLookupByLibrary.simpleMessage("تسوية"),
        "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
        "dealer": MessageLookupByLibrary.simpleMessage("تاجر"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("سعر التاجر"),
        "delete": MessageLookupByLibrary.simpleMessage("حذف"),
        "deliveryAddress":
            MessageLookupByLibrary.simpleMessage("عنوان التوصيل"),
        "deliveryCharge": MessageLookupByLibrary.simpleMessage("رسوم التوصيل"),
        "describtion": MessageLookupByLibrary.simpleMessage("الوصف"),
        "discount": MessageLookupByLibrary.simpleMessage("خصم"),
        "doNotDistrub": MessageLookupByLibrary.simpleMessage("عدم الإزعاج"),
        "due": MessageLookupByLibrary.simpleMessage("آجل"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("المبلغ المستحق: "),
        "dueCollection": MessageLookupByLibrary.simpleMessage("جمع المستحقات"),
        "dueCollectionReports":
            MessageLookupByLibrary.simpleMessage("تقارير جمع المستحقات"),
        "dueList": MessageLookupByLibrary.simpleMessage("قائمة المستحقات"),
        "dueReports": MessageLookupByLibrary.simpleMessage("تقرير المستحقات"),
        "easyToUseMobilePos": MessageLookupByLibrary.simpleMessage(
            "نظام نقاط البيع المحمول سهل الاستخدام"),
        "edit": MessageLookupByLibrary.simpleMessage("تعديل"),
        "editPurchaseInvoice":
            MessageLookupByLibrary.simpleMessage("تعديل فاتورة الشراء"),
        "editSalesInvoice":
            MessageLookupByLibrary.simpleMessage("تعديل فاتورة المبيعات"),
        "editSocailMedia": MessageLookupByLibrary.simpleMessage(
            "تعديل وسائل التواصل الاجتماعي"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "emailAddress":
            MessageLookupByLibrary.simpleMessage("عنوان البريد الإلكتروني"),
        "endDate": MessageLookupByLibrary.simpleMessage("تاريخ الانتهاء"),
        "enterAddress": MessageLookupByLibrary.simpleMessage("أدخل العنوان"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("أدخل المبلغ"),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم العلامة التجارية"),
        "enterCapacity": MessageLookupByLibrary.simpleMessage("أدخل السعة"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم الفئة"),
        "enterColor": MessageLookupByLibrary.simpleMessage("أدخل اللون"),
        "enterDealerPrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر التاجر"),
        "enterDiscount": MessageLookupByLibrary.simpleMessage("أدخل الخصم"),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("أدخل تاريخ المصروف"),
        "enterFullAddress":
            MessageLookupByLibrary.simpleMessage("أدخل العنوان بالكامل"),
        "enterInvoiceNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم الفاتورة"),
        "enterManufacturer":
            MessageLookupByLibrary.simpleMessage("أدخل اسم المصنع"),
        "enterMessageContent":
            MessageLookupByLibrary.simpleMessage("أدخل محتوى الرسالة"),
        "enterMrpOrRetailerPirce":
            MessageLookupByLibrary.simpleMessage("أدخل السعر الأقصى للبيع"),
        "enterName": MessageLookupByLibrary.simpleMessage("أدخل الاسم"),
        "enterNote": MessageLookupByLibrary.simpleMessage("أدخل ملاحظة"),
        "enterPartyName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم الطرف"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم الهاتف"),
        "enterProductCodeOrScan":
            MessageLookupByLibrary.simpleMessage("أدخل كود المنتج أو امسح"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("أدخل اسم المنتج"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر الشراء"),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("أدخل الرقم المرجعي"),
        "enterSize": MessageLookupByLibrary.simpleMessage("أدخل الحجم"),
        "enterStocks":
            MessageLookupByLibrary.simpleMessage("أدخل الكمية المتوفرة"),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("أدخل معرف المعاملة"),
        "enterType": MessageLookupByLibrary.simpleMessage("أدخل النوع"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان المستخدم"),
        "enterWeight": MessageLookupByLibrary.simpleMessage("أدخل الوزن"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("أدخل سعر الجملة"),
        "enterYourDescriptionHere":
            MessageLookupByLibrary.simpleMessage("أدخل وصفك هنا"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان بريدك الإلكتروني"),
        "enterYourFeedBackTitle":
            MessageLookupByLibrary.simpleMessage("أدخل عنوان ملاحظاتك"),
        "enterYourMobileNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم موبايلك"),
        "enterYourName": MessageLookupByLibrary.simpleMessage("أدخل اسمك"),
        "enterYourNumber": MessageLookupByLibrary.simpleMessage("أدخل رقمك"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("أدخل كلمة مرورك"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("أدخل رقم تليفونك"),
        "enterYourTransactionId":
            MessageLookupByLibrary.simpleMessage("أدخل معرف العملية بتاعك"),
        "cashBox": MessageLookupByLibrary.simpleMessage("الخزنة"),
        "expense": MessageLookupByLibrary.simpleMessage("مصروف"),
        "expenseCategory": MessageLookupByLibrary.simpleMessage("فئة المصروف"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("تاريخ المصروف"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("المصروف لـ"),
        "expenseReport":
            MessageLookupByLibrary.simpleMessage("تقرير المصروفات"),
        "fashion": MessageLookupByLibrary.simpleMessage("مصروف"),
        "featureAreTheImportant": MessageLookupByLibrary.simpleMessage(
            "الميزات هي الجزء المهم اللي بيخلي AmrDev POS مختلف عن الحلول التقليدية."),
        "feedBack": MessageLookupByLibrary.simpleMessage("ملاحظات"),
        "firstName": MessageLookupByLibrary.simpleMessage("الاسم الأول"),
        "fontSide": MessageLookupByLibrary.simpleMessage("الجانب الأمامي"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("للاستخدام غير المحدود"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور"),
        "forgotPasswords":
            MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور؟"),
        "formDate": MessageLookupByLibrary.simpleMessage("من تاريخ"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("نسخ احتياطي للبيانات مجاني"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("تحديث مدى الحياة مجانًا"),
        "freePacakge": MessageLookupByLibrary.simpleMessage("الباقة المجانية"),
        "freePlan": MessageLookupByLibrary.simpleMessage("الباقة المجانية"),
        "gallary": MessageLookupByLibrary.simpleMessage("المعرض"),
        "getOtp": MessageLookupByLibrary.simpleMessage("احصل على رمز التحقق"),
        "govermentId": MessageLookupByLibrary.simpleMessage("بطاقة حكومية"),
        "guest": MessageLookupByLibrary.simpleMessage("زائر"),
        "havenotAnAccounts":
            MessageLookupByLibrary.simpleMessage("خدمة العملاء ***********"),
        "history": MessageLookupByLibrary.simpleMessage("التاريخ"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "howWeProtectYourInformation":
            MessageLookupByLibrary.simpleMessage("كيف نحمي معلوماتك"),
        "howWeUseYourInformation":
            MessageLookupByLibrary.simpleMessage("كيف نستخدم معلوماتك"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("التحقق من الهوية"),
        "image": MessageLookupByLibrary.simpleMessage("صورة"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("زيادة المخزون"),
        "invNo": MessageLookupByLibrary.simpleMessage("رقم الفاتورة"),
        "invoiceNumber": MessageLookupByLibrary.simpleMessage("رقم الفاتورة"),
        "invoiceSetting":
            MessageLookupByLibrary.simpleMessage("إعدادات الفاتورة"),
        "itemAdded": MessageLookupByLibrary.simpleMessage("تمت إضافة المنتج"),
        "kg": MessageLookupByLibrary.simpleMessage("كيلوغرام"),
        "kycVerification": MessageLookupByLibrary.simpleMessage("تحقق KYC"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "lastName": MessageLookupByLibrary.simpleMessage("الكنية"),
        "ledger": MessageLookupByLibrary.simpleMessage("دفتر الحسابات"),
        "lifeTimePurchase":
            MessageLookupByLibrary.simpleMessage("شراء مدى الحياة"),
        "link": MessageLookupByLibrary.simpleMessage("رابط"),
        "linkedIn": MessageLookupByLibrary.simpleMessage("لينكد إن"),
        "logIn": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "logOUt": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "logo": MessageLookupByLibrary.simpleMessage("شعار"),
        "loss": MessageLookupByLibrary.simpleMessage("خسارة"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("خسارة / ربح"),
        "lossOrProfitDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل الخسارة / الربح"),
        "maan": MessageLookupByLibrary.simpleMessage("AmrDev"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "اترك انطباع دائم على عملائك من خلال فواتير مخصصة بعلامتك التجارية. الترقية الغير محدودة بتقدم ميزة فريدة لتخصيص فواتيرك، مما يضيف لمسة احترافية تعزز هويتك العلامية وتزيد ولاء العملاء."),
        "manageYourBussinessWith":
            MessageLookupByLibrary.simpleMessage("إدارة عملك باستخدام"),
        "masterCard": MessageLookupByLibrary.simpleMessage("بطاقة ماستر"),
        "menufeturer": MessageLookupByLibrary.simpleMessage("المصنع"),
        "message": MessageLookupByLibrary.simpleMessage("رسالة"),
        "messageHistory": MessageLookupByLibrary.simpleMessage("سجل الرسائل"),
        "mobiPosAppIsFree": MessageLookupByLibrary.simpleMessage(
            "تطبيق AmrDev POS مجاني وسهل الاستخدام. في الواقع، إنه واحد من أفضل أنظمة POS في جميع أنحاء العالم."),
        "mobiPosIsaCompleteBusinesSolution": MessageLookupByLibrary.simpleMessage(
            "AmrDev POS هو حلاً كاملاً للأعمال مع المخزون والحساب والمبيعات والمصاريف والخسارة / الربح."),
        "monthly": MessageLookupByLibrary.simpleMessage("شهريًا"),
        "moreInfo": MessageLookupByLibrary.simpleMessage("مزيد من المعلومات"),
        "name": MessageLookupByLibrary.simpleMessage("أدخل اسمك"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "noConnection": MessageLookupByLibrary.simpleMessage("لا يوجد اتصال"),
        "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
        "noDataAvailable":
            MessageLookupByLibrary.simpleMessage("لا تتوفر بيانات"),
        "noHistoryFound": MessageLookupByLibrary.simpleMessage("لا توجد سجلات"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("لا توجد عمليات"),
        "noUserFoundForThatEmail": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على مستخدم بهذا البريد الإلكتروني."),
        "noUserRoleFound": MessageLookupByLibrary.simpleMessage(
            "لم يتم العثور على صلاحيات المستخدم"),
        "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
        "notification": MessageLookupByLibrary.simpleMessage("إشعار"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "onboardOne": MessageLookupByLibrary.simpleMessage(
            "نظام نقاط البيع (POS) يحتوي على العديد من الوظائف، بما في ذلك تتبع المبيعات وإدارة المخزون."),
        "onboardThree": MessageLookupByLibrary.simpleMessage(
            "يساعد هذا النظام على تحسين عملياتك لصالح عملائك."),
        "onboardTwo": MessageLookupByLibrary.simpleMessage(
            "يجب أن يبسط نظام POS لدينا العمليات اليومية تلقائيًا، مما يجعل التنقل سهلاً."),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("الرصيد الافتتاحي"),
        "order": MessageLookupByLibrary.simpleMessage("الطلبيات"),
        "otp": MessageLookupByLibrary.simpleMessage("إغلاق"),
        "pacakge": MessageLookupByLibrary.simpleMessage("حزمة"),
        "packageFeatures": MessageLookupByLibrary.simpleMessage("ميزات الباقة"),
        "paid": MessageLookupByLibrary.simpleMessage("المدفوع"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("المبلغ المدفوع"),
        "parties": MessageLookupByLibrary.simpleMessage("العملاء / الموردين"),
        "taxs": MessageLookupByLibrary.simpleMessage("الضرائب"),
        "partiesList":
            MessageLookupByLibrary.simpleMessage("قائمة العملاء والموردين"),
        "partyName": MessageLookupByLibrary.simpleMessage("اسم الحفلة"),
        "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
        "payCash": MessageLookupByLibrary.simpleMessage("الدفع نقدًا"),
        "payWithBkash": MessageLookupByLibrary.simpleMessage("الدفع عبر bkash"),
        "payWithPaypal":
            MessageLookupByLibrary.simpleMessage("الدفع عبر Paypal"),
        "payeeName": MessageLookupByLibrary.simpleMessage("اسم المستفيد"),
        "payeeNumber": MessageLookupByLibrary.simpleMessage("رقم المستفيد"),
        "payment": MessageLookupByLibrary.simpleMessage("الدفع"),
        "paymentAmount": MessageLookupByLibrary.simpleMessage("مبلغ الدفع"),
        "paymentComplete": MessageLookupByLibrary.simpleMessage("اكتمال الدفع"),
        "paymentInstructions":
            MessageLookupByLibrary.simpleMessage("تعليمات الدفع:"),
        "paymentType": MessageLookupByLibrary.simpleMessage("نوع الدفع"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
        "pickEndDate":
            MessageLookupByLibrary.simpleMessage("اختر تاريخ الانتهاء"),
        "pickStartDate":
            MessageLookupByLibrary.simpleMessage("اختر تاريخ البدء"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "الرجاء التحقق من اتصالك بالإنترنت"),
        "pleaseConnectYourBluttothPrinter":
            MessageLookupByLibrary.simpleMessage(
                "يرجى توصيل طابعة Bluetooth الخاصة بك"),
        "pleaseEnterAConfirmPassword":
            MessageLookupByLibrary.simpleMessage("يرجى إدخال كلمة مرور تأكيد"),
        "pleaseEnterAPassword":
            MessageLookupByLibrary.simpleMessage("الرجاء إدخال كلمة مرور"),
        "pleaseEnterTheEmailAddressBelowToRecive":
            MessageLookupByLibrary.simpleMessage(
                "الرجاء إدخال عنوان بريدك الإلكتروني أدناه لتلقي رابط إعادة تعيين كلمة المرور."),
        "powerdedByMobiPos":
            MessageLookupByLibrary.simpleMessage("Amr Sayed Test POS"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("دعم العملاء المميز"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("الباقة المميزة"),
        "previousPayAmounts":
            MessageLookupByLibrary.simpleMessage("المبالغ المدفوعة السابقة"),
        "price": MessageLookupByLibrary.simpleMessage("السعر"),
        "print": MessageLookupByLibrary.simpleMessage("طباعة"),
        "printingOption":
            MessageLookupByLibrary.simpleMessage("خيارات الطباعة"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
        "product": MessageLookupByLibrary.simpleMessage("منتج"),
        "productCode": MessageLookupByLibrary.simpleMessage("رمز المنتج"),
        "productList": MessageLookupByLibrary.simpleMessage("قائمة المنتجات"),
        "productName": MessageLookupByLibrary.simpleMessage("اسم المنتج"),
        "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
        "profileEdit":
            MessageLookupByLibrary.simpleMessage("تحرير الملف الشخصي"),
        "profit": MessageLookupByLibrary.simpleMessage("ربح"),
        "promo": MessageLookupByLibrary.simpleMessage("ترويج"),
        "promoCode": MessageLookupByLibrary.simpleMessage("كود الخصم"),
        "purchase": MessageLookupByLibrary.simpleMessage("المشترايات"),
        "purchaseAlarm": MessageLookupByLibrary.simpleMessage("إنذار الشراء"),
        "purchaseConfirmed":
            MessageLookupByLibrary.simpleMessage("تأكيد الشراء"),
        "purchaseDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل الشراء"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("قائمة الشراء"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("شراء باقة المميزة"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("سعر الشراء"),
        "purchaseRepoet": MessageLookupByLibrary.simpleMessage("تقرير الشراء"),
        "purchaseReports":
            MessageLookupByLibrary.simpleMessage("تقارير الشراء"),
        "purchaseReportss":
            MessageLookupByLibrary.simpleMessage("تقارير الشراء"),
        "qty": MessageLookupByLibrary.simpleMessage("الكمية"),
        "quantity": MessageLookupByLibrary.simpleMessage("الكمية"),
        "recentTransactions":
            MessageLookupByLibrary.simpleMessage("المعاملات الأخ,,"),
        "recivedThePin":
            MessageLookupByLibrary.simpleMessage("استلام الرمز السري"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("الرقم المرجعي"),
        "register": MessageLookupByLibrary.simpleMessage("تسجيل"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("المستحق المتبقي"),
        "reports": MessageLookupByLibrary.simpleMessage("تقارير"),
        "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
        "resendOtp":
            MessageLookupByLibrary.simpleMessage("إعادة إرسال رمز التحقق: "),
        "retailer": MessageLookupByLibrary.simpleMessage("بائع التجزئة"),
        "retur": MessageLookupByLibrary.simpleMessage("إرجاع"),
        "returnAMount": MessageLookupByLibrary.simpleMessage("مبلغ الإرجاع"),
        "returnAmount": MessageLookupByLibrary.simpleMessage("مبلغ الإرجاع"),
        "revenue": MessageLookupByLibrary.simpleMessage("الإيرادات"),
        "safeGuardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "حماية بيانات عملك بسهولة. تتضمن ترقية AmrDev POS POS Unlimited نسخ احتياطي للبيانات مجانيًا ، مما يضمن حماية معلوماتك القيمة من أي أحداث غير متوقعة. ركز على ما يهم حقًا - نمو عملك."),
        "saleDetails": MessageLookupByLibrary.simpleMessage("تفاصيل البيع"),
        "salePrice": MessageLookupByLibrary.simpleMessage("سعر البيع"),
        "saleReports": MessageLookupByLibrary.simpleMessage("تقرير المبيعات"),
        "saleReportss": MessageLookupByLibrary.simpleMessage("تقارير البيع"),
        "sales": MessageLookupByLibrary.simpleMessage("المبيعات"),
        "salesAndPurchaseReports":
            MessageLookupByLibrary.simpleMessage("تقارير المبيعات والشراء"),
        "salesList": MessageLookupByLibrary.simpleMessage("قائمة المبيعات"),
        "save": MessageLookupByLibrary.simpleMessage("حفظ"),
        "saveAndPublish": MessageLookupByLibrary.simpleMessage("حفظ ونشر"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "search": MessageLookupByLibrary.simpleMessage("بحث"),
        "seeAllPromoCode":
            MessageLookupByLibrary.simpleMessage("عرض جميع كودات الخصم"),
        "select": MessageLookupByLibrary.simpleMessage("اختر"),
        "selectContacts":
            MessageLookupByLibrary.simpleMessage("اختر جهات الاتصال"),
        "selectvariations":
            MessageLookupByLibrary.simpleMessage("اختر التغييرات:"),
        "send": MessageLookupByLibrary.simpleMessage("إرسال"),
        "sendEmail":
            MessageLookupByLibrary.simpleMessage("إرسال بريد إلكتروني"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("إرسال الرسالة"),
        "sendResetLink":
            MessageLookupByLibrary.simpleMessage("إرسال رابط إعادة التعيين"),
        "sendSms": MessageLookupByLibrary.simpleMessage("إرسال رسالة نصية"),
        "sendSmsw": MessageLookupByLibrary.simpleMessage("إرسال رسالة نصية؟"),
        "sendYOurEmail":
            MessageLookupByLibrary.simpleMessage("أرسل بريدك الإلكتروني"),
        "setUpYourProfile":
            MessageLookupByLibrary.simpleMessage("إعداد ملفك الشخصي"),
        "setting": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
        "size": MessageLookupByLibrary.simpleMessage("الحجم"),
        "skip": MessageLookupByLibrary.simpleMessage("تخطى"),
        "sms": MessageLookupByLibrary.simpleMessage("رسائل نصية"),
        "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البدء"),
        "startNewSale":
            MessageLookupByLibrary.simpleMessage("بدء عملية بيع جديدة"),
        "startTypingToSearch":
            MessageLookupByLibrary.simpleMessage("ابدأ الكتابة للبحث"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "ابق على مقدمة التطورات التكنولوجية دون تكاليف إضافية. تأكد من أن لديك دائمًا أحدث الأدوات والميزات بيديك من خلال ترقية AmrDev POS POS Unlimited ، مما يضمن أن تظل عملك متطورًا."),
        "stockList": MessageLookupByLibrary.simpleMessage("قائمة المخزون"),
        "stocks": MessageLookupByLibrary.simpleMessage("المخزون"),
        "subTotal": MessageLookupByLibrary.simpleMessage("المجموع الفرعي"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "subscription": MessageLookupByLibrary.simpleMessage("اشتراك"),
        "supplier": MessageLookupByLibrary.simpleMessage("مورد"),
        "supplierName": MessageLookupByLibrary.simpleMessage("اسم المورد"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("رمز SWIFT"),
        "takeADriveruser": MessageLookupByLibrary.simpleMessage(
            "قم بإحضار رخصة القيادة أو بطاقة الهوية الوطنية أو صورة جواز السفر"),
        "takeaNidCardToCheckYourInformation":
            MessageLookupByLibrary.simpleMessage(
                "احضر بطاقة الهوية للتحقق من معلوماتك"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("شروط الاستخدام"),
        "thankYOuForYourDuePayment":
            MessageLookupByLibrary.simpleMessage("شكرًا لك على دفعتك المستحقة"),
        "thankYouForYourPurchase":
            MessageLookupByLibrary.simpleMessage("شكرًا لشرائك"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "الاسم يقول كل شيء. مع AmrDev POS POS Unlimited ، ليس هناك حد لاستخدامك. سواء كنت تقوم بمعالجة عدد قليل من العمليات أو تواجه اندفاعًا من العملاء ، يمكنك العمل بثقة ، علمًا أنك لا تقتصر عن الحدود."),
        "theUserWillBe": MessageLookupByLibrary.simpleMessage(
            "سيتم حذف المستخدم وسيتم حذف جميع البيانات من حسابك. هل أنت متأكد من حذف هذا؟"),
        "thirdPartyServices":
            MessageLookupByLibrary.simpleMessage("خدمات الجهات الخارجية"),
        "title": MessageLookupByLibrary.simpleMessage("العنوان"),
        "toDate": MessageLookupByLibrary.simpleMessage("إلى تاريخ"),
        "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("المبلغ الإجمالي"),
        "totalDue": MessageLookupByLibrary.simpleMessage("إجمالي المستحق"),
        "totalExpense":
            MessageLookupByLibrary.simpleMessage("إجمالي المصروفات"),
        "totalPayable":
            MessageLookupByLibrary.simpleMessage("الإجمالي المستحق"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("السعر الإجمالي"),
        "totalSale": MessageLookupByLibrary.simpleMessage("إجمالي المبيعات"),
        "totalStock": MessageLookupByLibrary.simpleMessage("إجمالي المخزون"),
        "totalVat":
            MessageLookupByLibrary.simpleMessage("إجمالي القيمة المضافة"),
        "totals": MessageLookupByLibrary.simpleMessage("الإجمالي:"),
        "transaction": MessageLookupByLibrary.simpleMessage("عملية"),
        "transactionId": MessageLookupByLibrary.simpleMessage("معرف العملية"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("حاول مرة أخرى"),
        "twitter": MessageLookupByLibrary.simpleMessage("تويتر"),
        "type": MessageLookupByLibrary.simpleMessage("النوع"),
        "unitName": MessageLookupByLibrary.simpleMessage("اسم الوحدة"),
        "units": MessageLookupByLibrary.simpleMessage("الوحدات"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("استخدام غير محدود"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "فتح الإمكانيات الكاملة لنظام AmrDev POS POS من خلال جلسات تدريب شخصية تقودها فريق الخبراء لدينا. من الأساسيات إلى التقنيات المتقدمة ، نحن نضمن أنك ملم بجميع جوانب النظام لتحسين عمليات عملك."),
        "update": MessageLookupByLibrary.simpleMessage("تحديث"),
        "updateContact": MessageLookupByLibrary.simpleMessage("تحديث الاتصال"),
        "updateNow": MessageLookupByLibrary.simpleMessage("تحديث الآن"),
        "updateProduct": MessageLookupByLibrary.simpleMessage("تحديث المنتج"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("تحديث ملفك الشخصي"),
        "updateYourProfileToConnect": MessageLookupByLibrary.simpleMessage(
            "قم بتحديث ملفك الشخصي لربط طبيبك بانطباع أفضل"),
        "updateYourProfiletoConnectTOCusomter":
            MessageLookupByLibrary.simpleMessage(
                "قم بتحديث ملفك الشخصي للاتصال بعملائك بانطباع أفضل"),
        "uploadDocument": MessageLookupByLibrary.simpleMessage("تحميل المستند"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("تحميل ملف"),
        "upplier": MessageLookupByLibrary.simpleMessage("المورد"),
        "useMobiPos": MessageLookupByLibrary.simpleMessage("استخدم AmrDev POS"),
        "useOfTheSystem":
            MessageLookupByLibrary.simpleMessage("استخدام النظام"),
        "userRole": MessageLookupByLibrary.simpleMessage("صلاحيات المستخدم"),
        "userRoleDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل صلاحيات المستخدم"),
        "userTitle": MessageLookupByLibrary.simpleMessage("عنوان المستخدم"),
        "verifyOtp":
            MessageLookupByLibrary.simpleMessage("التحقق من رمز التحقق"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("التحقق من رقم الهاتف"),
        "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
        "walkInCustomer": MessageLookupByLibrary.simpleMessage("عميل جديد "),
        "weHaveSendAnEmailwithInstructions": MessageLookupByLibrary.simpleMessage(
            "لقد أرسلنا رسالة بريد إلكتروني مع تعليمات حول كيفية إعادة تعيين كلمة المرور إلى:"),
        "weMayUseThirdPartyServicesToSupport": MessageLookupByLibrary.simpleMessage(
            "نحن قد نستخدم خدمات الجهات الخارجية لدعم وظائف تطبيقنا، مثل مزودي التحليلات ومعالجي الدفع. قد تقوم هذه الخدمات من الجهات الخارجية بجمع معلومات عنك عند استخدامك لتطبيقنا. يرجى ملاحظة أننا غير مسؤولين عن ممارسات الخصوصية لهذه الخدمات من الجهات الخارجية."),
        "weTakeIndustryStandard": MessageLookupByLibrary.simpleMessage(
            "نحن نتخذ تدابير قياسية في الصناعة لحماية معلوماتك الشخصية، بما في ذلك التشفير والتخزين الآمن. نحن أيضًا نقيد الوصول إلى معلوماتك للشخصيات المفوضة فقط."),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            "نحن نفهم أهمية العمليات السلسة. لهذا السبب ، يتوفر دعمنا على مدار الساعة لمساعدتك ، سواء كانت استفسارات سريعة أم مخاوف شاملة. اتصل بنا في أي وقت وفي أي مكان عبر المكالمة أو واتساب لتجربة خدمة العملاء غير المسبوقة."),
        "weUseYourPersonalInformation": MessageLookupByLibrary.simpleMessage(
            "نستخدم معلوماتك الشخصية لتوفير أفضل تجربة ممكنة على تطبيقنا، بما في ذلك تخصيص توصيات المحتوى الخاصة بك، وربطك بالخبراء، وتحسين وظائف تطبيقاتنا. قد نستخدم أيضًا معلوماتك للتواصل معك بشأن التحديثات أو الترويجات أو المعلومات الأخرى ذات الصلة بتطبيقنا."),
        "weight": MessageLookupByLibrary.simpleMessage("الوزن"),
        "whatsNew": MessageLookupByLibrary.simpleMessage("الجديد"),
        "wholSeller": MessageLookupByLibrary.simpleMessage("تاجر جملة"),
        "wholeSalePrice": MessageLookupByLibrary.simpleMessage("سعر الجملة"),
        "writeYourMessageHere":
            MessageLookupByLibrary.simpleMessage("اكتب رسالتك هنا"),
        "wrongPasswordProvidedforThatUser":
            MessageLookupByLibrary.simpleMessage(
                "تم تقديم كلمة مرور خاطئة لهذا المستخدم."),
        "yearly": MessageLookupByLibrary.simpleMessage("سنويًا"),
        "yesDeleteForever":
            MessageLookupByLibrary.simpleMessage("نعم، احذف بشكل دائم"),
        "youAreUsing": MessageLookupByLibrary.simpleMessage("أنت تستخدم"),
        "youHaveGotAnEmail": MessageLookupByLibrary.simpleMessage(
            "لقد تلقيت رسالة بريد إلكتروني"),
        "youHaveSuccefulyLogin": MessageLookupByLibrary.simpleMessage(
            "لقد قمت بتسجيل الدخول بنجاح إلى حسابك. ابق مع AmrDev POS ."),
        "youHaveToReLogin": MessageLookupByLibrary.simpleMessage(
            "يجب عليك إعادة تسجيل الدخول إلى حسابك."),
        "youNeedToIdentityVerifyBeforeYouBuying":
            MessageLookupByLibrary.simpleMessage(
                "تحتاج إلى التحقق من الهوية قبل شرائك للرسائل القصيرة"),
        "yourMessageRemains":
            MessageLookupByLibrary.simpleMessage("رسالتك تبقى"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("باقتك"),
        "yourPackageWillExpireinDay": MessageLookupByLibrary.simpleMessage(
            "سوف تنتهي صلاحية باقتك خلال 5 أيام")
      };
}
