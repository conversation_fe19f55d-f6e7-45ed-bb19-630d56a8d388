import 'package:firebase_database/firebase_database.dart';

/// نموذج رسالة الدردشة
class ChatMessageModel {
  final String id;
  final String senderId;
  final String senderName;
  final String message;
  final int timestamp;
  final String messageType; // text, voice
  final String? voiceUrl;
  final bool isRead;

  ChatMessageModel({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
    this.messageType = 'text',
    this.voiceUrl,
    this.isRead = false,
  });

  /// إنشاء نموذج من JSON
  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'] ?? '',
      senderId: json['senderId'] ?? '',
      senderName: json['senderName'] ?? '',
      message: json['message'] ?? '',
      timestamp: json['timestamp'] ?? 0,
      messageType: json['messageType'] ?? 'text',
      voiceUrl: json['voiceUrl'],
      isRead: json['isRead'] ?? false,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'message': message,
      'timestamp': timestamp,
      'messageType': messageType,
      'voiceUrl': voiceUrl,
      'isRead': isRead,
    };
  }

  /// إنشاء نموذج من DataSnapshot
  factory ChatMessageModel.fromSnapshot(DataSnapshot snapshot) {
    final data = Map<String, dynamic>.from(
        snapshot.value as Map<dynamic, dynamic>? ?? {});
    data['id'] = snapshot.key ?? '';
    return ChatMessageModel.fromJson(data);
  }

  /// إنشاء نسخة معدلة من النموذج
  ChatMessageModel copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? message,
    int? timestamp,
    String? messageType,
    String? voiceUrl,
    bool? isRead,
  }) {
    return ChatMessageModel(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      messageType: messageType ?? this.messageType,
      voiceUrl: voiceUrl ?? this.voiceUrl,
      isRead: isRead ?? this.isRead,
    );
  }
}
