import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/user_log_service.dart';

/// تنفيذ سجلات المستخدم في وظائف المخزون
class InventoryLogImplementation {
  /// تسجيل إضافة مخزون
  static Future<void> logInventoryAdded({
    required String productCode,
    required String productName,
    required int quantity,
    required String warehouseName,
    required WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.inventoryAdded,
        description: 'تم إضافة $quantity وحدة من المنتج $productName إلى مخزن $warehouseName',
        data: {
          'productCode': productCode,
          'productName': productName,
          'quantity': quantity,
          'warehouseName': warehouseName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل إضافة المخزون: $e');
    }
  }

  /// تسجيل تحديث مخزون
  static Future<void> logInventoryUpdated({
    required String productCode,
    required String productName,
    required int oldQuantity,
    required int newQuantity,
    required String warehouseName,
    required WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.inventoryUpdated,
        description: 'تم تحديث مخزون المنتج $productName من $oldQuantity إلى $newQuantity في مخزن $warehouseName',
        data: {
          'productCode': productCode,
          'productName': productName,
          'oldQuantity': oldQuantity,
          'newQuantity': newQuantity,
          'warehouseName': warehouseName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل تحديث المخزون: $e');
    }
  }

  /// تسجيل إزالة مخزون
  static Future<void> logInventoryRemoved({
    required String productCode,
    required String productName,
    required int quantity,
    required String warehouseName,
    required WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.inventoryRemoved,
        description: 'تم إزالة $quantity وحدة من المنتج $productName من مخزن $warehouseName',
        data: {
          'productCode': productCode,
          'productName': productName,
          'quantity': quantity,
          'warehouseName': warehouseName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل إزالة المخزون: $e');
    }
  }

  /// تسجيل تعديل مخزون
  static Future<void> logInventoryAdjusted({
    required String productCode,
    required String productName,
    required int adjustmentQuantity,
    required String reason,
    required String warehouseName,
    required WidgetRef? ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      final adjustmentType = adjustmentQuantity >= 0 ? 'زيادة' : 'نقص';
      final absQuantity = adjustmentQuantity.abs();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.inventoryAdjusted,
        description: 'تم تعديل مخزون المنتج $productName بـ $adjustmentType $absQuantity وحدة في مخزن $warehouseName',
        data: {
          'productCode': productCode,
          'productName': productName,
          'adjustmentQuantity': adjustmentQuantity,
          'reason': reason,
          'warehouseName': warehouseName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل تعديل المخزون: $e');
    }
  }
}
