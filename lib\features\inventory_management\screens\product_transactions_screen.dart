// بسم الله الرحمن الرحيم
// شاشة معاملات المنتج - تعرض معاملات البيع والشراء للمنتج

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/product_transaction_model.dart';
import '../services/product_transaction_service.dart';

/// شاشة معاملات المنتج
class ProductTransactionsScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة معاملات المنتج
  const ProductTransactionsScreen({
    super.key,
    required this.productCode,
    required this.productName,
    this.transactionType,
  });

  /// كود المنتج
  final String productCode;

  /// اسم المنتج
  final String productName;

  /// نوع المعاملة (اختياري)
  final TransactionType? transactionType;

  @override
  ConsumerState<ProductTransactionsScreen> createState() =>
      _ProductTransactionsScreenState();
}

class _ProductTransactionsScreenState
    extends ConsumerState<ProductTransactionsScreen> {
  bool _isLoading = false;
  List<ProductTransactionModel> _transactions = [];
  TransactionType? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.transactionType;
    _loadTransactions();
  }

  // تحميل المعاملات
  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ProductTransactionModel> transactions;
      
      if (_selectedType == TransactionType.sale) {
        transactions = await ref
            .read(productTransactionServiceProvider)
            .getProductSalesTransactions(widget.productCode);
      } else if (_selectedType == TransactionType.purchase) {
        transactions = await ref
            .read(productTransactionServiceProvider)
            .getProductPurchaseTransactions(widget.productCode);
      } else {
        transactions = await ref
            .read(productTransactionServiceProvider)
            .getAllProductTransactions(widget.productCode);
      }

      _transactions = transactions;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تصفية المعاملات حسب النوع
  void _filterByType(TransactionType? type) {
    setState(() {
      _selectedType = type;
    });
    _loadTransactions();
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(
      symbol: 'جنيه',
      decimalDigits: 2,
    );

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاملات المنتج',
              style: const TextStyle(fontSize: 18),
            ),
            Text(
              widget.productName,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.greyTextColor,
              ),
            ),
          ],
        ),
        centerTitle: false,
        actions: [
          PopupMenuButton<TransactionType?>(
            icon: const Icon(Icons.filter_list),
            onSelected: _filterByType,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: null,
                child: Text('جميع المعاملات'),
              ),
              const PopupMenuItem(
                value: TransactionType.sale,
                child: Text('المبيعات فقط'),
              ),
              const PopupMenuItem(
                value: TransactionType.purchase,
                child: Text('المشتريات فقط'),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل المعاملات...',
            )
          : _transactions.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 64,
                        color: AppColors.greyTextColor,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد معاملات',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.greyTextColor,
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // إحصائيات سريعة
                    Container(
                      padding: const EdgeInsets.all(16),
                      color: AppColors.lightGreyColor,
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildSummaryCard(
                              'إجمالي المعاملات',
                              '${_transactions.length}',
                              Icons.receipt,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildSummaryCard(
                              'إجمالي الكمية',
                              '${_transactions.fold<int>(0, (sum, t) => sum + t.quantity)}',
                              Icons.inventory,
                              Colors.green,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildSummaryCard(
                              'إجمالي القيمة',
                              currencyFormat.format(_transactions.fold<double>(
                                  0, (sum, t) => sum + t.totalAmount)),
                              Icons.monetization_on,
                              Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // قائمة المعاملات
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _transactions.length,
                        itemBuilder: (context, index) {
                          final transaction = _transactions[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getTransactionColor(
                                    transaction.transactionType),
                                child: Icon(
                                  _getTransactionIcon(transaction.transactionType),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(
                                'فاتورة ${transaction.invoiceNumber}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${transaction.customerName} • ${DateFormat('yyyy-MM-dd').format(transaction.date)}',
                                  ),
                                  Text(
                                    'الكمية: ${transaction.quantity} • السعر: ${currencyFormat.format(transaction.unitPrice)}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    currencyFormat.format(transaction.totalAmount),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: _getTransactionColor(
                                          transaction.transactionType),
                                    ),
                                  ),
                                  Text(
                                    _getTransactionTypeText(
                                        transaction.transactionType),
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: AppColors.greyTextColor,
                                    ),
                                  ),
                                ],
                              ),
                              onTap: () {
                                _showTransactionDetails(transaction);
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
    );
  }

  // بناء بطاقة ملخص
  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 10,
              color: AppColors.greyTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // الحصول على لون المعاملة
  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Colors.green;
      case TransactionType.purchase:
        return Colors.blue;
      case TransactionType.saleReturn:
        return Colors.orange;
      case TransactionType.purchaseReturn:
        return Colors.purple;
    }
  }

  // الحصول على أيقونة المعاملة
  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Icons.point_of_sale;
      case TransactionType.purchase:
        return Icons.shopping_cart;
      case TransactionType.saleReturn:
        return Icons.assignment_return;
      case TransactionType.purchaseReturn:
        return Icons.keyboard_return;
    }
  }

  // الحصول على نص نوع المعاملة
  String _getTransactionTypeText(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return 'بيع';
      case TransactionType.purchase:
        return 'شراء';
      case TransactionType.saleReturn:
        return 'مرتجع بيع';
      case TransactionType.purchaseReturn:
        return 'مرتجع شراء';
    }
  }

  // عرض تفاصيل المعاملة
  void _showTransactionDetails(ProductTransactionModel transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المعاملة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم الفاتورة: ${transaction.invoiceNumber}'),
            Text('النوع: ${_getTransactionTypeText(transaction.transactionType)}'),
            Text('العميل/المورد: ${transaction.customerName}'),
            if (transaction.customerPhone != null)
              Text('الهاتف: ${transaction.customerPhone}'),
            Text('التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(transaction.date)}'),
            Text('الكمية: ${transaction.quantity}'),
            Text('سعر الوحدة: ${NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2).format(transaction.unitPrice)}'),
            Text('المبلغ الإجمالي: ${NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2).format(transaction.totalAmount)}'),
            if (transaction.paymentType != null)
              Text('طريقة الدفع: ${transaction.paymentType}'),
            if (transaction.notes != null && transaction.notes!.isNotEmpty)
              Text('ملاحظات: ${transaction.notes}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
