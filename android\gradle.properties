org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -Dfile.encoding=UTF-8
android.useAndroidX=true
android.enableJetifier=true
android.enableR8=true

# إعدادات محسنة للبناء
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# إعدادات Android محسنة
android.nonTransitiveRClass=false
android.nonFinalResIds=false
android.suppressUnsupportedCompileSdk=36

# إعدادات Kotlin محسنة
kotlin.incremental=true
kotlin.daemon.jvmargs=-Xmx2G
kotlin.compiler.execution.strategy=in-process

# إخفاء التحذيرات
org.gradle.warning.mode=none
org.gradle.logging.level=lifecycle
android.suppressUnsupportedOptionWarnings=true
android.suppressUnsupportedCompileSdk=36

# إعدادات إضافية لتحسين الأداء
org.gradle.workers.max=4
android.builder.sdkDownload=true
