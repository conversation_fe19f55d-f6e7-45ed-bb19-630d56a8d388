// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "MRP": MessageLookupByLibrary.simpleMessage("MRP"),
        "UserTitle": MessageLookupByLibrary.simpleMessage("User Title"),
        "YourPackageWillExpireTodayPleasePurchaseagain":
            MessageLookupByLibrary.simpleMessage(
                "Your Package Will Expire Today\n\nPlease Purchase again"),
        "aTheSystemIsProvided": MessageLookupByLibrary.simpleMessage(
            "(a) The System is provided solely for the\npurpose of facilitating point of sale\ntransactions andrelatedactivities in your\nbusiness."),
        "aToUseTheSystem": MessageLookupByLibrary.simpleMessage(
            "(a) To use the System, you may be\nrequired to create an account. You\nagree to provide accurate, current, and\ncomplete information during\nthe registration process and toupdate\nsuchinformationto keep itaccurate\nand complete."),
        "aboutApp": MessageLookupByLibrary.simpleMessage("About App"),
        "acceptanceOfTerms":
            MessageLookupByLibrary.simpleMessage("Acceptance of Terms"),
        "accountName": MessageLookupByLibrary.simpleMessage("Account Name"),
        "accountNumber": MessageLookupByLibrary.simpleMessage("Account Number"),
        "accountRegistration":
            MessageLookupByLibrary.simpleMessage("Account Registration"),
        "addBrand": MessageLookupByLibrary.simpleMessage("Add Brand"),
        "addCategory": MessageLookupByLibrary.simpleMessage("Add Category"),
        "addContact": MessageLookupByLibrary.simpleMessage("Add Contact"),
        "addCustomer": MessageLookupByLibrary.simpleMessage("Add Customer"),
        "addDelivery": MessageLookupByLibrary.simpleMessage("Add Delivery"),
        "addDescription": MessageLookupByLibrary.simpleMessage("Add note"),
        "addDocumentId":
            MessageLookupByLibrary.simpleMessage("Add Document Id"),
        "addDucument": MessageLookupByLibrary.simpleMessage("Add Document"),
        "addExpense": MessageLookupByLibrary.simpleMessage("Add Expense"),
        "addExpenseCategory":
            MessageLookupByLibrary.simpleMessage("Add Expense Category"),
        "addItems": MessageLookupByLibrary.simpleMessage("Add Items"),
        "addNewAddress":
            MessageLookupByLibrary.simpleMessage("Add New Address"),
        "addNewProduct": MessageLookupByLibrary.simpleMessage("Add Product"),
        "addNote": MessageLookupByLibrary.simpleMessage("Add Note"),
        "addPurchase": MessageLookupByLibrary.simpleMessage("Add Purchase"),
        "addSales": MessageLookupByLibrary.simpleMessage("Add Sales"),
        "addSuccessful":
            MessageLookupByLibrary.simpleMessage("Added Successful"),
        "addUnit": MessageLookupByLibrary.simpleMessage("Add Unit"),
        "addUserRole": MessageLookupByLibrary.simpleMessage("Add User Role"),
        "address": MessageLookupByLibrary.simpleMessage("Address"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "allBusinessSolution":
            MessageLookupByLibrary.simpleMessage("All business solutions"),
        "alreadyHaveAnAccounts":
            MessageLookupByLibrary.simpleMessage("Already Have An Accounts"),
        "amount": MessageLookupByLibrary.simpleMessage("Amount"),
        "androidIOSAppSupport":
            MessageLookupByLibrary.simpleMessage("Android & iOS App Support"),
        "apply": MessageLookupByLibrary.simpleMessage("Apply"),
        "areYourSureDeleteThisUser": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this user?"),
        "bYouHaveResponsiveFor": MessageLookupByLibrary.simpleMessage(
            "(b) You are responsible for\nmaintainingthe confidentiality of your\naccount and password andfor restricting\naccess to your account. You accept\nresponsibility for all activities that\noccur under your account."),
        "bYouMustBeAtLeastYearsOld": MessageLookupByLibrary.simpleMessage(
            "(b) You must be at least 18 years old or the\nlegal age of majority in your jurisdiction to\nuse the System."),
        "backSide": MessageLookupByLibrary.simpleMessage("Back side"),
        "backToHome": MessageLookupByLibrary.simpleMessage("Back To Home"),
        "balance": MessageLookupByLibrary.simpleMessage("Balance"),
        "egypt": MessageLookupByLibrary.simpleMessage("Egypt"),
        "bankAccountingCurrecny":
            MessageLookupByLibrary.simpleMessage("Bank Account Currency"),
        "bankInformation":
            MessageLookupByLibrary.simpleMessage("Bank Information"),
        "bankName": MessageLookupByLibrary.simpleMessage("Bank Name"),
        "billTo": MessageLookupByLibrary.simpleMessage("Bill To"),
        "branchName": MessageLookupByLibrary.simpleMessage("Branch Name"),
        "brand": MessageLookupByLibrary.simpleMessage("Brand"),
        "brandName": MessageLookupByLibrary.simpleMessage("Brand Name"),
        "businessCategory":
            MessageLookupByLibrary.simpleMessage("Business Category"),
        "buy": MessageLookupByLibrary.simpleMessage("Buy"),
        "buyPremiumPlan":
            MessageLookupByLibrary.simpleMessage("Buy Premium Plan"),
        "buySms": MessageLookupByLibrary.simpleMessage("Buy Sms"),
        "byAccessingOrUsingThePointOfSales": MessageLookupByLibrary.simpleMessage(
            "By accessing or using the Point of Sale (POS) System (the \"System\") provided by [Your Company Name] (\"Company\"), you agree to be bound by these Terms of Use. If you do not agree to these Terms of Use, do not use the System."),
        "cYouHaveResponsiveForEnsuring": MessageLookupByLibrary.simpleMessage(
            "(c) You are responsible for ensuring that your\naccess to and use of the System is in\ncompliance with all applicable laws and\nregulations."),
        "cacel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "call": MessageLookupByLibrary.simpleMessage("Call"),
        "camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "capacity": MessageLookupByLibrary.simpleMessage("Capacity"),
        "cash": MessageLookupByLibrary.simpleMessage("Cash"),
        "categories": MessageLookupByLibrary.simpleMessage("Categories"),
        "category": MessageLookupByLibrary.simpleMessage("Category"),
        "cateogryName": MessageLookupByLibrary.simpleMessage("Category Name"),
        "change": MessageLookupByLibrary.simpleMessage("Change?"),
        "changePassword":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "checkEmail": MessageLookupByLibrary.simpleMessage("Check Email"),
        "choseACustomer":
            MessageLookupByLibrary.simpleMessage("Chose a Customer"),
        "choseASupplier":
            MessageLookupByLibrary.simpleMessage("Chose a Supplier"),
        "choseYourFeature":
            MessageLookupByLibrary.simpleMessage("Choose your features"),
        "clarence": MessageLookupByLibrary.simpleMessage("Clarence"),
        "clickToConnect":
            MessageLookupByLibrary.simpleMessage("Click to connect"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "color": MessageLookupByLibrary.simpleMessage("Color"),
        "companyAddress":
            MessageLookupByLibrary.simpleMessage("Company Address"),
        "companyAndShopName":
            MessageLookupByLibrary.simpleMessage("Company & Shop Name"),
        "completeTransaction":
            MessageLookupByLibrary.simpleMessage("Complete Transaction"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirm Password"),
        "congratulations":
            MessageLookupByLibrary.simpleMessage("Congratulations"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
        "continu": MessageLookupByLibrary.simpleMessage("Continue"),
        "country": MessageLookupByLibrary.simpleMessage("Country"),
        "create": MessageLookupByLibrary.simpleMessage("Create"),
        "createAFreeAccounts":
            MessageLookupByLibrary.simpleMessage("Create a Free Account"),
        "currency": MessageLookupByLibrary.simpleMessage("Currency"),
        "currentStock": MessageLookupByLibrary.simpleMessage("Current Stock"),
        "customInvoiceBranding":
            MessageLookupByLibrary.simpleMessage("Custom Invoice Branding"),
        "customer": MessageLookupByLibrary.simpleMessage("Customer"),
        "customerDetails":
            MessageLookupByLibrary.simpleMessage("Customer Details"),
        "customerName": MessageLookupByLibrary.simpleMessage("Customer Name"),
        "dailyTransaciton":
            MessageLookupByLibrary.simpleMessage("Daily Transaction"),
        "dashBoardOverView":
            MessageLookupByLibrary.simpleMessage("Dashboard Overview"),
        "accountSettlement": MessageLookupByLibrary.simpleMessage("Settlement"),
        "date": MessageLookupByLibrary.simpleMessage("Date"),
        "dealer": MessageLookupByLibrary.simpleMessage("Dealer"),
        "dealerPrice": MessageLookupByLibrary.simpleMessage("Dealer Price"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "deliveryAddress":
            MessageLookupByLibrary.simpleMessage("Delivery Address"),
        "deliveryCharge":
            MessageLookupByLibrary.simpleMessage("Delivery Charge"),
        "describtion": MessageLookupByLibrary.simpleMessage("Description"),
        "discount": MessageLookupByLibrary.simpleMessage("Discount"),
        "doNotDistrub": MessageLookupByLibrary.simpleMessage("Do not disturb"),
        "due": MessageLookupByLibrary.simpleMessage("Due"),
        "dueAmount": MessageLookupByLibrary.simpleMessage("Due Amount: "),
        "dueCollection": MessageLookupByLibrary.simpleMessage("Due Collection"),
        "dueCollectionReports":
            MessageLookupByLibrary.simpleMessage("Due Collection Reports"),
        "dueList": MessageLookupByLibrary.simpleMessage("Due List"),
        "dueReports": MessageLookupByLibrary.simpleMessage("Due Report"),
        "easyToUseMobilePos":
            MessageLookupByLibrary.simpleMessage("Easy to use mobile pos"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editPurchaseInvoice":
            MessageLookupByLibrary.simpleMessage("Edit Purchase Invoice"),
        "editSalesInvoice":
            MessageLookupByLibrary.simpleMessage("Edit Sales Invoice"),
        "editSocailMedia":
            MessageLookupByLibrary.simpleMessage("Edit Social Media"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailAddress": MessageLookupByLibrary.simpleMessage("Email Address"),
        "endDate": MessageLookupByLibrary.simpleMessage("End Date"),
        "enterAddress": MessageLookupByLibrary.simpleMessage("Enter Address"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Enter Amount."),
        "enterBrandName":
            MessageLookupByLibrary.simpleMessage("Enter Brand Name"),
        "enterCapacity": MessageLookupByLibrary.simpleMessage("Enter Capacity"),
        "enterCategoryName":
            MessageLookupByLibrary.simpleMessage("Enter Category Name"),
        "enterColor": MessageLookupByLibrary.simpleMessage("Enter Color"),
        "enterDealerPrice":
            MessageLookupByLibrary.simpleMessage("Enter Dealer Price"),
        "enterDiscount":
            MessageLookupByLibrary.simpleMessage("Enter Discount."),
        "enterExpenseDate":
            MessageLookupByLibrary.simpleMessage("Enter Expense Date"),
        "enterFullAddress":
            MessageLookupByLibrary.simpleMessage("Enter Full Address"),
        "enterInvoiceNumber":
            MessageLookupByLibrary.simpleMessage("Enter Invoice Number"),
        "enterManufacturer":
            MessageLookupByLibrary.simpleMessage("Enter Manufacturer"),
        "enterMessageContent":
            MessageLookupByLibrary.simpleMessage("Enter Message Content"),
        "enterMrpOrRetailerPirce":
            MessageLookupByLibrary.simpleMessage("Enter MRP/Retailer Price"),
        "enterName": MessageLookupByLibrary.simpleMessage("Enter Name"),
        "enterNote": MessageLookupByLibrary.simpleMessage("Enter Note"),
        "enterPartyName":
            MessageLookupByLibrary.simpleMessage("Enter Party Name"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter Phone Number"),
        "enterProductCodeOrScan":
            MessageLookupByLibrary.simpleMessage("Enter Product Code Or Scan"),
        "enterProductName":
            MessageLookupByLibrary.simpleMessage("Enter Product Name"),
        "enterPurchasePrice":
            MessageLookupByLibrary.simpleMessage("Enter Purchase Price."),
        "enterReferenceNumber":
            MessageLookupByLibrary.simpleMessage("Enter Reference Number"),
        "enterSize": MessageLookupByLibrary.simpleMessage("Enter Size"),
        "enterStocks": MessageLookupByLibrary.simpleMessage("Enter Stocks."),
        "enterTransactionId":
            MessageLookupByLibrary.simpleMessage("Enter Transaction Id"),
        "enterType": MessageLookupByLibrary.simpleMessage("Enter Type"),
        "enterUserTitle":
            MessageLookupByLibrary.simpleMessage("Enter user title"),
        "enterWeight": MessageLookupByLibrary.simpleMessage("Enter Weight"),
        "enterWholeSalePrice":
            MessageLookupByLibrary.simpleMessage("Enter Wholesale Price"),
        "enterYourDescriptionHere":
            MessageLookupByLibrary.simpleMessage("Enter your description here"),
        "enterYourEmailAddress":
            MessageLookupByLibrary.simpleMessage("Enter Your Email Address"),
        "enterYourFeedBackTitle":
            MessageLookupByLibrary.simpleMessage("Enter Your Feedback Tittle"),
        "enterYourMobileNumber":
            MessageLookupByLibrary.simpleMessage("Enter your mobile number"),
        "enterYourName":
            MessageLookupByLibrary.simpleMessage("Enter Your Name"),
        "enterYourNumber":
            MessageLookupByLibrary.simpleMessage("Enter your phone number"),
        "enterYourPassword":
            MessageLookupByLibrary.simpleMessage("Enter your password"),
        "enterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter Your Phone Number"),
        "enterYourTransactionId":
            MessageLookupByLibrary.simpleMessage("Enter your transaction id"),
        "cashBox": MessageLookupByLibrary.simpleMessage("الخزنة"),
        "expense": MessageLookupByLibrary.simpleMessage("Expense"),
        "expenseCategory":
            MessageLookupByLibrary.simpleMessage("Expense Category"),
        "expenseDate": MessageLookupByLibrary.simpleMessage("Expense Date"),
        "expenseFor": MessageLookupByLibrary.simpleMessage("Expense For"),
        "expenseReport": MessageLookupByLibrary.simpleMessage("Expense Report"),
        "facebok": MessageLookupByLibrary.simpleMessage("Facebook"),
        "fashion": MessageLookupByLibrary.simpleMessage("Fashion"),
        "featureAreTheImportant": MessageLookupByLibrary.simpleMessage(
            "Features are the important part which makes AmrDev POS  different from traditional solutions."),
        "feedBack": MessageLookupByLibrary.simpleMessage("FeedBack"),
        "firstName": MessageLookupByLibrary.simpleMessage("First Name"),
        "fontSide": MessageLookupByLibrary.simpleMessage("Font Side"),
        "forUnlimitedUses":
            MessageLookupByLibrary.simpleMessage("For unlimited usages"),
        "forgotPassword":
            MessageLookupByLibrary.simpleMessage("Forgot password"),
        "forgotPasswords":
            MessageLookupByLibrary.simpleMessage("Forgot Password?"),
        "formDate": MessageLookupByLibrary.simpleMessage("From Date"),
        "freeDataBackup":
            MessageLookupByLibrary.simpleMessage("Free Data Backup"),
        "freeLifeTimeUpdate":
            MessageLookupByLibrary.simpleMessage("Free Lifetime Update"),
        "freePacakge": MessageLookupByLibrary.simpleMessage("Free Package"),
        // "freePlan": MessageLookupByLibrary.simpleMessage("Free Plan"),
        "gallary": MessageLookupByLibrary.simpleMessage("Gallery"),
        "getOtp": MessageLookupByLibrary.simpleMessage("Get Otp"),
        "govermentId": MessageLookupByLibrary.simpleMessage("Government Id"),
        "guest": MessageLookupByLibrary.simpleMessage("Guest"),
        "havenotAnAccounts": MessageLookupByLibrary.simpleMessage(
            "For assistance, call ***********"),
        "history": MessageLookupByLibrary.simpleMessage("History"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "howWeProtectYourInformation": MessageLookupByLibrary.simpleMessage(
            "How We Protect Your Information"),
        "howWeUseYourInformation":
            MessageLookupByLibrary.simpleMessage("How We Use Your Information"),
        "identityVerify":
            MessageLookupByLibrary.simpleMessage("Identity Verify"),
        "image": MessageLookupByLibrary.simpleMessage("Image"),
        "increaseStock": MessageLookupByLibrary.simpleMessage("Increase Stock"),
        "inistrument": MessageLookupByLibrary.simpleMessage("Instrument"),
        "instragram": MessageLookupByLibrary.simpleMessage("Instagram"),
        "invNo": MessageLookupByLibrary.simpleMessage("Inv No."),
        "invoiceNumber": MessageLookupByLibrary.simpleMessage("Invoice Number"),
        "invoiceSetting":
            MessageLookupByLibrary.simpleMessage("Invoice Setting"),
        "itemAdded": MessageLookupByLibrary.simpleMessage("Item Added"),
        "kg": MessageLookupByLibrary.simpleMessage("Kg"),
        "kycVerification":
            MessageLookupByLibrary.simpleMessage("KYC Verification"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "lastName": MessageLookupByLibrary.simpleMessage("Last Name"),
        "ledger": MessageLookupByLibrary.simpleMessage("Ledger"),
        "lifeTimePurchase":
            MessageLookupByLibrary.simpleMessage("Lifetime\nPurchase"),
        "link": MessageLookupByLibrary.simpleMessage("Link"),
        "linkedIn": MessageLookupByLibrary.simpleMessage("LinkedIn"),
        "logIn": MessageLookupByLibrary.simpleMessage("LogIn"),
        "logOUt": MessageLookupByLibrary.simpleMessage("Log Out"),
        "login": MessageLookupByLibrary.simpleMessage("Log In"),
        "logo": MessageLookupByLibrary.simpleMessage("Logo"),
        "loss": MessageLookupByLibrary.simpleMessage("Loss"),
        "lossOrProfit": MessageLookupByLibrary.simpleMessage("Loss/Profit"),
        "lossOrProfitDetails":
            MessageLookupByLibrary.simpleMessage("Loss/Profit Details"),
        "maan": MessageLookupByLibrary.simpleMessage("AmrDev"),
        "makeALastingImpression": MessageLookupByLibrary.simpleMessage(
            "Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty."),
        "manageYourBussinessWith":
            MessageLookupByLibrary.simpleMessage("Manage your business with "),
        "masterCard": MessageLookupByLibrary.simpleMessage("Master card"),
        "menufeturer": MessageLookupByLibrary.simpleMessage("Manufacturer"),
        "message": MessageLookupByLibrary.simpleMessage("Message"),
        "messageHistory":
            MessageLookupByLibrary.simpleMessage("Message History"),
        "mobiPosAppIsFree": MessageLookupByLibrary.simpleMessage(
            "AmrDev POS  app is free, easy to use. In fact, it\'s one of the best  POS systems around the world."),
        "mobiPosIsaCompleteBusinesSolution": MessageLookupByLibrary.simpleMessage(
            "AmrDev POS  is a complete business solution with stock, account, sales, expense & loss/profit."),
        "monthly": MessageLookupByLibrary.simpleMessage("Monthly"),
        "moreInfo": MessageLookupByLibrary.simpleMessage("More Info"),
        "name": MessageLookupByLibrary.simpleMessage("Enter Your Name."),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "noConnection": MessageLookupByLibrary.simpleMessage("No Connection"),
        "noData": MessageLookupByLibrary.simpleMessage("No Data"),
        "noDataAvailable":
            MessageLookupByLibrary.simpleMessage("No data available"),
        "noHistoryFound":
            MessageLookupByLibrary.simpleMessage("No History Found!"),
        "noTransactionFound":
            MessageLookupByLibrary.simpleMessage("No Transaction Found!"),
        "noUserFoundForThatEmail": MessageLookupByLibrary.simpleMessage(
            "No user found for that email."),
        "noUserRoleFound":
            MessageLookupByLibrary.simpleMessage("No User Role Found"),
        "note": MessageLookupByLibrary.simpleMessage("Note"),
        "notification": MessageLookupByLibrary.simpleMessage("Notification"),
        "ok": MessageLookupByLibrary.simpleMessage("Ok"),
        "onboardOne": MessageLookupByLibrary.simpleMessage(
            "POS that contains a great deal of functionality, including sales tracking, inventory management."),
        "onboardThree": MessageLookupByLibrary.simpleMessage(
            "This system helps you improve your operations for your customers."),
        "onboardTwo": MessageLookupByLibrary.simpleMessage(
            "Our POS system should simplify daily operations automatically, making it easy to navigate."),
        "openingBalance":
            MessageLookupByLibrary.simpleMessage("Opening Balance "),
        "order": MessageLookupByLibrary.simpleMessage("Orders"),
        "otp": MessageLookupByLibrary.simpleMessage("Close"),
        "pacakge": MessageLookupByLibrary.simpleMessage("Package"),
        "packageFeatures":
            MessageLookupByLibrary.simpleMessage("Package Features"),
        "paid": MessageLookupByLibrary.simpleMessage("Paid"),
        "paidAmount": MessageLookupByLibrary.simpleMessage("Paid Amount"),
        "parties": MessageLookupByLibrary.simpleMessage("Parties"),
        "taxs": MessageLookupByLibrary.simpleMessage("Taxs"),
        "partiesList": MessageLookupByLibrary.simpleMessage("Parties List"),
        "partyName": MessageLookupByLibrary.simpleMessage("Party Name"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "payCash": MessageLookupByLibrary.simpleMessage("Pay Cash"),
        "payWithBkash": MessageLookupByLibrary.simpleMessage("Pay with bkash"),
        "payWithPaypal":
            MessageLookupByLibrary.simpleMessage("Pay with Paypal"),
        "payeeName": MessageLookupByLibrary.simpleMessage("Payee Name"),
        "payeeNumber": MessageLookupByLibrary.simpleMessage("Payee Number"),
        "payment": MessageLookupByLibrary.simpleMessage("Payment"),
        "paymentAmount": MessageLookupByLibrary.simpleMessage("Payment Amount"),
        "paymentComplete":
            MessageLookupByLibrary.simpleMessage("Payment Complete"),
        "paymentInstructions":
            MessageLookupByLibrary.simpleMessage("Payment Instruction:"),
        "paymentType": MessageLookupByLibrary.simpleMessage("Payment Type"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "pickEndDate": MessageLookupByLibrary.simpleMessage("Pick End Date"),
        "pickStartDate":
            MessageLookupByLibrary.simpleMessage("Pick Start Date"),
        "pleaseCheckYourInternetConnectivity":
            MessageLookupByLibrary.simpleMessage(
                "Please check your internet connectivity"),
        "pleaseConnectYourBluttothPrinter":
            MessageLookupByLibrary.simpleMessage(
                "Please connect your bluetooth Printer"),
        "pleaseEnterAConfirmPassword": MessageLookupByLibrary.simpleMessage(
            "Please Enter A Confirm Password"),
        "pleaseEnterAPassword":
            MessageLookupByLibrary.simpleMessage("Please enter a password"),
        "pleaseEnterTheEmailAddressBelowToRecive":
            MessageLookupByLibrary.simpleMessage(
                "Please enter your email address below to receive password Reset Link."),
        "powerdedByMobiPos":
            MessageLookupByLibrary.simpleMessage("Powered By AmrDev 2025"),
        "premiumCustomerSupport":
            MessageLookupByLibrary.simpleMessage("Premium Customer Support"),
        "premiumPlan": MessageLookupByLibrary.simpleMessage("Premium plan"),
        "previousPayAmounts":
            MessageLookupByLibrary.simpleMessage("Previous Pay Amounts"),
        "price": MessageLookupByLibrary.simpleMessage("price"),
        "print": MessageLookupByLibrary.simpleMessage("Print"),
        "printingOption":
            MessageLookupByLibrary.simpleMessage("Printing Option"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
        "product": MessageLookupByLibrary.simpleMessage("Product"),
        "productCode": MessageLookupByLibrary.simpleMessage("Product Code"),
        "productList": MessageLookupByLibrary.simpleMessage("Product List"),
        "productName": MessageLookupByLibrary.simpleMessage("Product Name"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileEdit": MessageLookupByLibrary.simpleMessage("Profile Edit"),
        "profit": MessageLookupByLibrary.simpleMessage("Profit"),
        "promo": MessageLookupByLibrary.simpleMessage("promo"),
        "promoCode": MessageLookupByLibrary.simpleMessage("Promo Code"),
        "purchase": MessageLookupByLibrary.simpleMessage("Purchase"),
        "purchaseAlarm": MessageLookupByLibrary.simpleMessage("Purchase Alarm"),
        "purchaseConfirmed":
            MessageLookupByLibrary.simpleMessage("Purchase Confirmed"),
        "purchaseDetails":
            MessageLookupByLibrary.simpleMessage("Purchase Details"),
        "purchaseList": MessageLookupByLibrary.simpleMessage("Purchase List"),
        "purchasePremiumPlan":
            MessageLookupByLibrary.simpleMessage("Purchase Premium Plan"),
        "purchasePrice": MessageLookupByLibrary.simpleMessage("Purchase Price"),
        "purchaseRepoet":
            MessageLookupByLibrary.simpleMessage("Purchase Report"),
        "purchaseReports":
            MessageLookupByLibrary.simpleMessage("Purchase Price"),
        "purchaseReportss":
            MessageLookupByLibrary.simpleMessage("Purchase Reports"),
        "qty": MessageLookupByLibrary.simpleMessage("Qty"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
        "recentTransactions":
            MessageLookupByLibrary.simpleMessage("Recent Transactions"),
        "recivedThePin":
            MessageLookupByLibrary.simpleMessage("Received The pin"),
        "referenceNumber":
            MessageLookupByLibrary.simpleMessage("Reference Number"),
        "register": MessageLookupByLibrary.simpleMessage("Register"),
        "remainingDue": MessageLookupByLibrary.simpleMessage("Remaining Due"),
        "reports": MessageLookupByLibrary.simpleMessage("Reports"),
        "resendCode": MessageLookupByLibrary.simpleMessage("Resend Code"),
        "resendOtp": MessageLookupByLibrary.simpleMessage("Resend OTP : "),
        "retailer": MessageLookupByLibrary.simpleMessage("Retailer"),
        "retur": MessageLookupByLibrary.simpleMessage("Return"),
        "returnAMount": MessageLookupByLibrary.simpleMessage("Return Amount"),
        "returnAmount": MessageLookupByLibrary.simpleMessage("Return Amount"),
        "revenue": MessageLookupByLibrary.simpleMessage("Revenue"),
        "safeGuardYourBusinessDate": MessageLookupByLibrary.simpleMessage(
            "Safeguard your business data effortlessly. Our AmrDev POS Unlimited Upgrade includes free data backup, ensuring your valuable information is protected against any unforeseen events. Focus on what truly matters - your business growth."),
        "saleDetails": MessageLookupByLibrary.simpleMessage("Sale Details"),
        "salePrice": MessageLookupByLibrary.simpleMessage("Sale Price"),
        "saleReports": MessageLookupByLibrary.simpleMessage("Sale Report"),
        "saleReportss": MessageLookupByLibrary.simpleMessage("Sale Reports"),
        "sales": MessageLookupByLibrary.simpleMessage("Sales"),
        "salesAndPurchaseReports":
            MessageLookupByLibrary.simpleMessage("Sale & Purchase Reports"),
        "salesList": MessageLookupByLibrary.simpleMessage("Sales List"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "saveAndPublish":
            MessageLookupByLibrary.simpleMessage("Save and Publish"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "seeAllPromoCode":
            MessageLookupByLibrary.simpleMessage("See all promo codes"),
        "select": MessageLookupByLibrary.simpleMessage("Select"),
        "selectContacts":
            MessageLookupByLibrary.simpleMessage("Select Contacts"),
        "selectvariations":
            MessageLookupByLibrary.simpleMessage("Select Variation: "),
        "send": MessageLookupByLibrary.simpleMessage("Send"),
        "sendEmail": MessageLookupByLibrary.simpleMessage("Send Email"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Send Message"),
        "sendResetLink":
            MessageLookupByLibrary.simpleMessage("Send Reset Link"),
        "sendSms": MessageLookupByLibrary.simpleMessage("Send Sms"),
        "sendSmsw": MessageLookupByLibrary.simpleMessage("Send sms?"),
        "sendYOurEmail":
            MessageLookupByLibrary.simpleMessage("Send Your Email"),
        "setUpYourProfile":
            MessageLookupByLibrary.simpleMessage("Setup Your Profile"),
        "setting": MessageLookupByLibrary.simpleMessage("Setting"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "size": MessageLookupByLibrary.simpleMessage("Size"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "sms": MessageLookupByLibrary.simpleMessage("Sms"),
        "socailMarketing":
            MessageLookupByLibrary.simpleMessage("Social Marketing"),
        "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
        "startNewSale": MessageLookupByLibrary.simpleMessage("Start New Sale"),
        "startTypingToSearch":
            MessageLookupByLibrary.simpleMessage("Start typing to search"),
        "stayAtTheForFront": MessageLookupByLibrary.simpleMessage(
            "Stay at the forefront of technological advancements without any extra costs. Our AmrDev POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge."),
        "stockList": MessageLookupByLibrary.simpleMessage("Stock List"),
        "stocks": MessageLookupByLibrary.simpleMessage("Stocks"),
        "subTotal": MessageLookupByLibrary.simpleMessage("Sub Total"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "subscription": MessageLookupByLibrary.simpleMessage("Subscription"),
        "supplier": MessageLookupByLibrary.simpleMessage("Supplier"),
        "supplierName": MessageLookupByLibrary.simpleMessage("Supplier Name"),
        "swiftCode": MessageLookupByLibrary.simpleMessage("SWIFT Code"),
        "takeADriveruser": MessageLookupByLibrary.simpleMessage(
            "Take a driver\'s license, national identity card or passport photo"),
        "takeaNidCardToCheckYourInformation":
            MessageLookupByLibrary.simpleMessage(
                "Take an identity card to check your information"),
        "termsOfUse": MessageLookupByLibrary.simpleMessage("Terms of use"),
        "thankYOuForYourDuePayment": MessageLookupByLibrary.simpleMessage(
            "Thank You For Your DUe Payment"),
        "thankYouForYourPurchase":
            MessageLookupByLibrary.simpleMessage("Thank You for your purchase"),
        "theNameSysIt": MessageLookupByLibrary.simpleMessage(
            "The name says it all. With AmrDev POS Unlimited, there\'s no cap on your usage. Whether you\'re processing a handful of transactions or experiencing a rush of customers, you can operate with confidence, knowing you\'re not constrained by limits"),
        "theUserWillBe": MessageLookupByLibrary.simpleMessage(
            "The user will be deleted and all the data will be deleted from your account.Are you sure to delete this?"),
        "thirdPartyServices":
            MessageLookupByLibrary.simpleMessage("Third-Party Services"),
        "title": MessageLookupByLibrary.simpleMessage("Title"),
        "toDate": MessageLookupByLibrary.simpleMessage("To Date"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Total Amount"),
        "totalDue": MessageLookupByLibrary.simpleMessage("Total Due"),
        "totalExpense": MessageLookupByLibrary.simpleMessage("Total Expense"),
        "totalPayable": MessageLookupByLibrary.simpleMessage("Total Payable"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price"),
        "totalSale": MessageLookupByLibrary.simpleMessage("Total Sale"),
        "totalStock": MessageLookupByLibrary.simpleMessage("Total Stocks"),
        "totalVat": MessageLookupByLibrary.simpleMessage("Total Vat"),
        "totals": MessageLookupByLibrary.simpleMessage("Total: "),
        "transaction": MessageLookupByLibrary.simpleMessage("Transaction"),
        "transactionId": MessageLookupByLibrary.simpleMessage("Transaction Id"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Try Again"),
        "twitter": MessageLookupByLibrary.simpleMessage("Twitter"),
        "type": MessageLookupByLibrary.simpleMessage("Type"),
        "unitName": MessageLookupByLibrary.simpleMessage("Unit Name"),
        "unitPirce": MessageLookupByLibrary.simpleMessage("Unit Price"),
        "units": MessageLookupByLibrary.simpleMessage("Units"),
        "unlimitedUsage":
            MessageLookupByLibrary.simpleMessage("Unlimited Usage"),
        "unlockTheFull": MessageLookupByLibrary.simpleMessage(
            "Unlock the full potential of AmrDev POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you\'re well-versed in utilizing every facet of the system to optimize your business processes."),
        "update": MessageLookupByLibrary.simpleMessage("Update"),
        "updateContact": MessageLookupByLibrary.simpleMessage("Update Contact"),
        "updateNow": MessageLookupByLibrary.simpleMessage("Update Now"),
        "updateProduct": MessageLookupByLibrary.simpleMessage("Update Product"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Update Your Profile"),
        "updateYourProfileToConnect": MessageLookupByLibrary.simpleMessage(
            "Update your profile to connect your doctor with better impression"),
        "updateYourProfiletoConnectTOCusomter":
            MessageLookupByLibrary.simpleMessage(
                "Update your profile to connect your customer with better impression"),
        "uploadDocument":
            MessageLookupByLibrary.simpleMessage("Upload Document"),
        "uploadFile": MessageLookupByLibrary.simpleMessage("Upload File"),
        "upplier": MessageLookupByLibrary.simpleMessage("Supplier"),
        "useMobiPos": MessageLookupByLibrary.simpleMessage("Use AmrDev POS"),
        "useOfTheSystem":
            MessageLookupByLibrary.simpleMessage("Use of the system"),
        "userRole": MessageLookupByLibrary.simpleMessage("User Role"),
        "userRoleDetails":
            MessageLookupByLibrary.simpleMessage("User Role Details"),
        "userTitle": MessageLookupByLibrary.simpleMessage("User Title"),
        "verifyOtp": MessageLookupByLibrary.simpleMessage("Verifying OTP"),
        "verifyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Verify Phone Number"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("View details"),
        "walkInCustomer":
            MessageLookupByLibrary.simpleMessage("Walk-in customer"),
        "weHaveSendAnEmailwithInstructions": MessageLookupByLibrary.simpleMessage(
            "We Have Send An Email with instructions on how to reset password to:"),
        "weMayUseThirdPartyServicesToSupport": MessageLookupByLibrary.simpleMessage(
            "We may use third-party services to support our app\'s functionality, such as analytics providers and payment processors. These third-party services may collect information about you when you use our app. Please note that we are not responsible for the privacy practices of these third-party services."),
        "weTakeIndustryStandard": MessageLookupByLibrary.simpleMessage(
            "We take industry-standard measures to protect your personal information, including encryption and secure storage. We also limit access to your information to authorized personnel only."),
        "weUnderStand": MessageLookupByLibrary.simpleMessage(
            " We understand the importance of seamless operations. That\'s why our round-the-clock support is available to assist you, whether it\'s a quick query or a comprehensive concern. Connect with us anytime, anywhere via call or WhatsApp to experience unrivaled customer service."),
        "weUseYourPersonalInformation": MessageLookupByLibrary.simpleMessage(
            "We use your personal information to provide you with the best possible experience on our app, including to personalize your content recomme ndations, connect you with experts, and improve our apps functionality. We may also use your information to communicate with you about updates, promotions, or other information related to our app."),
        "weight": MessageLookupByLibrary.simpleMessage("Weight"),
        "whatsNew": MessageLookupByLibrary.simpleMessage("What\'s New"),
        "wholSeller": MessageLookupByLibrary.simpleMessage("Wholesaler"),
        "wholeSalePrice":
            MessageLookupByLibrary.simpleMessage("WholeSale Price"),
        "writeYourMessageHere":
            MessageLookupByLibrary.simpleMessage("Write your message here"),
        "wrongPasswordProvidedforThatUser":
            MessageLookupByLibrary.simpleMessage(
                "Wrong password provided for that user."),
        "yearly": MessageLookupByLibrary.simpleMessage("Yearly"),
        "yesDeleteForever":
            MessageLookupByLibrary.simpleMessage("Yes, Delete Forever"),
        "youAreUsing": MessageLookupByLibrary.simpleMessage("You are using"),
        "youHaveGotAnEmail":
            MessageLookupByLibrary.simpleMessage("You Have Got An Email"),
        "youHaveSuccefulyLogin": MessageLookupByLibrary.simpleMessage(
            "You are successfully login into your account. Stay with AmrDev POS ."),
        "youHaveToReLogin": MessageLookupByLibrary.simpleMessage(
            "You have to RE-LOGIN on your account."),
        "youNeedToIdentityVerifyBeforeYouBuying":
            MessageLookupByLibrary.simpleMessage(
                "You need to identity verify before your buying sms"),
        "yourMessageRemains":
            MessageLookupByLibrary.simpleMessage("Your message remains"),
        "yourPackage": MessageLookupByLibrary.simpleMessage("Your Package"),
        "yourPackageWillExpireinDay": MessageLookupByLibrary.simpleMessage(
            "Your Package Will Expire in 5 Day")
      };
}
