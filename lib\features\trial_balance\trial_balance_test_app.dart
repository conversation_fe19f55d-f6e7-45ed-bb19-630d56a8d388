// بسم الله الرحمن الرحيم
// تطبيق تجريبي منفصل لاختبار ميزة Trial Balance

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/trial_balance_screen.dart';

/// تطبيق تجريبي منفصل لاختبار ميزة Trial Balance
/// يمكن تشغيله بشكل مستقل دون التأثير على التطبيق الرئيسي
class TrialBalanceTestApp extends StatelessWidget {
  const TrialBalanceTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        title: 'اختبار ميزة Trial Balance',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Cairo',
        ),
        home: const TrialBalanceTestHome(),
        routes: {
          '/trial-balance': (context) => const TrialBalanceScreen(),
        },
      ),
    );
  }
}

/// الشاشة الرئيسية للاختبار
class TrialBalanceTestHome extends StatelessWidget {
  const TrialBalanceTestHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار ميزة Trial Balance'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ميزة تقرير ميزان المراجعة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'تقرير محاسبي يعرض جميع الحسابات وأرصدتها المدينة والدائنة',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // زر الوصول إلى التقرير
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TrialBalanceScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.balance),
              label: const Text('فتح تقرير ميزان المراجعة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2196F3),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // معلومات الميزة
            const Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الميزات المتاحة:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text('• إنشاء تقرير ميزان المراجعة تلقائياً'),
                        Text('• اختيار فترات زمنية مختلفة'),
                        Text('• عرض الحسابات المدينة والدائنة'),
                        Text('• التحقق من توازن التقرير'),
                        Text('• حفظ التقارير للمراجعة اللاحقة'),
                        Text('• عرض التقارير المحفوظة'),
                        SizedBox(height: 16),
                        Text(
                          'مصادر البيانات:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text('• المبيعات (Sales Transition)'),
                        Text('• المشتريات (Purchase Transition)'),
                        Text('• العملاء (Customers)'),
                        Text('• الموردين (Suppliers)'),
                        Text('• المصروفات (Expense)'),
                        Text('• الخزينة (Treasury)'),
                        Text('• المنتجات (Products)'),
                        SizedBox(height: 16),
                        Text(
                          'الحسابات المدعومة:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text('الأصول: النقدية، حسابات العملاء، المخزون'),
                        Text('الخصوم: حسابات الموردين'),
                        Text('الإيرادات: المبيعات'),
                        Text('المصروفات: المشتريات، تكلفة البضاعة، المصروفات العامة'),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// دالة لتشغيل التطبيق التجريبي
void runTrialBalanceTest() {
  runApp(const TrialBalanceTestApp());
}

/// مثال على كيفية استخدام الميزة في main.dart
/// 
/// ```dart
/// import 'package:mobile_pos/features/trial_balance/trial_balance_test_app.dart';
/// 
/// void main() {
///   // لتشغيل التطبيق التجريبي
///   runTrialBalanceTest();
///   
///   // أو لتشغيل التطبيق الرئيسي مع إضافة الميزة
///   // runApp(MyApp());
/// }
/// ```
