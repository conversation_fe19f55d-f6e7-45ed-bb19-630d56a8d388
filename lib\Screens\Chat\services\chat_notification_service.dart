import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../services/system_notification_service.dart';
import '../../../services/firebase_listener_manager.dart';
import 'dart:async';

/// خدمة إشعارات الدردشة
class ChatNotificationService {
  static int _unreadMessagesCount = 0;
  static final List<Function(int)> _countListeners = [];
  static StreamSubscription<DatabaseEvent>? _messagesSubscription;
  static bool _isInitialized = false;
  static const String _ownerId = 'chat_notification_service';

  /// الحصول على عدد الرسائل غير المقروءة
  static int get unreadMessagesCount => _unreadMessagesCount;

  /// إضافة مستمع لتغيير عدد الرسائل غير المقروءة
  static void addCountListener(Function(int) listener) {
    _countListeners.add(listener);
  }

  /// إزالة مستمع تغيير عدد الرسائل غير المقروءة
  static void removeCountListener(Function(int) listener) {
    _countListeners.remove(listener);
  }

  /// تحديث عدد الرسائل غير المقروءة
  static void _updateUnreadCount(int count) {
    _unreadMessagesCount = count;
    for (final listener in _countListeners) {
      try {
        listener(count);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('خطأ في استدعاء مستمع عدد الرسائل: $e');
        }
      }
    }
  }

  /// تهيئة خدمة إشعارات الدردشة
  static Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        debugPrint('خدمة إشعارات الدردشة مهيأة بالفعل');
      }
      return;
    }

    try {
      if (kDebugMode) {
        // debugPrint('تهيئة خدمة إشعارات الدردشة...');
      }

      // بدء مراقبة الرسائل غير المقروءة
      await _startMonitoringUnreadMessages();

      _isInitialized = true;
      if (kDebugMode) {
        // debugPrint('تم تهيئة خدمة إشعارات الدردشة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في تهيئة خدمة إشعارات الدردشة: $e');
      }
    }
  }

  /// بدء مراقبة الرسائل غير المقروءة
  static Future<void> _startMonitoringUnreadMessages() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    try {
      // إلغاء الاشتراك السابق إذا كان موجوداً
      await _messagesSubscription?.cancel();

      // استخدام FirebaseListenerManager لمنع الاستماع المزدوج
      final stream = FirebaseListenerManager().listenToPath(
        'Chat Messages',
        ownerId: _ownerId,
        eventType: 'value',
        keepSynced: true,
      );

      _messagesSubscription = stream.listen((event) {
        _calculateUnreadMessages(currentUser.uid);
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في بدء مراقبة الرسائل غير المقروءة: $e');
      }
    }
  }

  /// حساب عدد الرسائل غير المقروءة
  static Future<void> _calculateUnreadMessages(String userId) async {
    try {
      int totalUnread = 0;

      final messagesRef = FirebaseDatabase.instance.ref('Chat Messages');
      final snapshot = await messagesRef.once();

      if (snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);

        for (final chatId in data.keys) {
          // التحقق من أن المستخدم جزء من هذه المحادثة
          if (chatId.contains(userId)) {
            final chatData = Map<String, dynamic>.from(data[chatId]);
            final messages = chatData['messages'];

            if (messages != null) {
              final messagesData = Map<String, dynamic>.from(messages);

              for (final messageData in messagesData.values) {
                final message = Map<String, dynamic>.from(messageData);

                // التحقق من أن الرسالة موجهة للمستخدم الحالي وغير مقروءة
                if (message['recipientId'] == userId &&
                    message['isRead'] == false) {
                  totalUnread++;
                }
              }
            }
          }
        }
      }

      _updateUnreadCount(totalUnread);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في حساب الرسائل غير المقروءة: $e');
      }
    }
  }

  /// إرسال إشعار رسالة جديدة
  static Future<void> sendNewMessageNotification({
    required String senderId,
    required String senderName,
    required String message,
    required String chatId,
  }) async {
    try {
      // إنشاء إشعار للرسالة الجديدة
      await SystemNotificationService.createNotification(
        title: 'رسالة جديدة من $senderName',
        message:
            message.length > 50 ? '${message.substring(0, 50)}...' : message,
        type: 'chat_message',
        data: {
          'senderId': senderId,
          'senderName': senderName,
          'chatId': chatId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (kDebugMode) {
        debugPrint('تم إرسال إشعار رسالة جديدة من: $senderName');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في إرسال إشعار الرسالة الجديدة: $e');
      }
    }
  }

  /// إرسال إشعار رسالة صوتية جديدة
  static Future<void> sendNewVoiceMessageNotification({
    required String senderId,
    required String senderName,
    required int duration,
    required String chatId,
  }) async {
    try {
      await SystemNotificationService.createNotification(
        title: 'رسالة صوتية جديدة من $senderName',
        message: 'رسالة صوتية بمدة $duration ثانية',
        type: 'voice_message',
        data: {
          'senderId': senderId,
          'senderName': senderName,
          'chatId': chatId,
          'duration': duration.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (kDebugMode) {
        debugPrint('تم إرسال إشعار رسالة صوتية جديدة من: $senderName');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في إرسال إشعار الرسالة الصوتية: $e');
      }
    }
  }

  /// إرسال إشعار صورة جديدة
  static Future<void> sendNewImageNotification({
    required String senderId,
    required String senderName,
    required String chatId,
  }) async {
    try {
      await SystemNotificationService.createNotification(
        title: 'صورة جديدة من $senderName',
        message: 'تم إرسال صورة جديدة',
        type: 'image_message',
        data: {
          'senderId': senderId,
          'senderName': senderName,
          'chatId': chatId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (kDebugMode) {
        debugPrint('تم إرسال إشعار صورة جديدة من: $senderName');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في إرسال إشعار الصورة: $e');
      }
    }
  }

  /// تحديث حالة قراءة الرسائل وإعادة حساب العدد
  static Future<void> markChatAsRead(String chatId) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // إعادة حساب الرسائل غير المقروءة
      await _calculateUnreadMessages(currentUser.uid);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في تحديث حالة قراءة المحادثة: $e');
      }
    }
  }

  /// الحصول على إحصائيات الدردشة
  static Future<Map<String, int>> getChatStatistics() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return {'totalChats': 0, 'unreadMessages': 0, 'todayMessages': 0};
      }

      int totalChats = 0;
      int todayMessages = 0;
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      final messagesRef = FirebaseDatabase.instance.ref('Chat Messages');
      final snapshot = await messagesRef.once();

      if (snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);

        for (final chatId in data.keys) {
          if (chatId.contains(currentUser.uid)) {
            totalChats++;

            final chatData = Map<String, dynamic>.from(data[chatId]);
            final messages = chatData['messages'];

            if (messages != null) {
              final messagesData = Map<String, dynamic>.from(messages);

              for (final messageData in messagesData.values) {
                final message = Map<String, dynamic>.from(messageData);
                final timestamp = message['timestamp'];

                if (timestamp != null) {
                  final messageDate = DateTime.fromMillisecondsSinceEpoch(
                    timestamp,
                  );
                  if (messageDate.isAfter(todayStart)) {
                    todayMessages++;
                  }
                }
              }
            }
          }
        }
      }

      return {
        'totalChats': totalChats,
        'unreadMessages': _unreadMessagesCount,
        'todayMessages': todayMessages,
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في الحصول على إحصائيات الدردشة: $e');
      }
      return {'totalChats': 0, 'unreadMessages': 0, 'todayMessages': 0};
    }
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    try {
      // إلغاء الاشتراك في Firebase
      await _messagesSubscription?.cancel();
      _messagesSubscription = null;

      // إلغاء المستمعين من FirebaseListenerManager
      FirebaseListenerManager().cancelListenersByOwner(_ownerId);

      // تنظيف المتغيرات
      _countListeners.clear();
      _unreadMessagesCount = 0;
      _isInitialized = false;

      if (kDebugMode) {
        debugPrint('تم تنظيف موارد خدمة إشعارات الدردشة');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في تنظيف موارد خدمة إشعارات الدردشة: $e');
      }
    }
  }
}
