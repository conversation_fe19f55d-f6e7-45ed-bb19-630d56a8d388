/// فئة ملف الوسائط
class MediaFile {
  String? url;
  String? name;
  String? type;

  MediaFile({this.url, this.name, this.type});
}

/// نموذج رسالة الدردشة
class ChatMessage {
  String? objectId;
  String text;
  String senderId;
  String senderName;
  String recipientId;
  String conversationId;
  String messageType;
  String? mediaUrl;
  MediaFile? mediaFile; // إضافة خاصية mediaFile للتوافق مع الكود القديم
  bool isRead;
  bool isDelivered;
  Map<String, String> reactions;
  String? replyTo;
  bool isDeleted;
  List<String> deletedFor;
  DateTime? createdAt;

  // أنواع الرسائل
  static const String typeText = 'text';
  static const String typeImage = 'image';
  static const String typeVoice = 'voice';
  static const String typeFile = 'file';
  static const String typeVideo = 'video';
  static const String typeLocation = 'location';
  static const String typeContact = 'contact';
  static const String typeCall = 'call'; // نوع جديد للمكالمات

  ChatMessage({
    this.objectId,
    this.text = '',
    this.senderId = '',
    this.senderName = '',
    this.recipientId = '',
    this.conversationId = '',
    this.messageType = typeText,
    this.mediaUrl,
    this.mediaFile,
    this.isRead = false,
    this.isDelivered = false,
    this.reactions = const {},
    this.replyTo,
    this.isDeleted = false,
    this.deletedFor = const [],
    this.createdAt,
  }) {
    // إذا كان mediaUrl موجودًا ولكن mediaFile غير موجود، قم بإنشاء mediaFile
    if (mediaUrl != null && mediaUrl!.isNotEmpty && mediaFile == null) {
      mediaFile = MediaFile(url: mediaUrl);
    }
    // إذا كان mediaFile موجودًا ولكن mediaUrl غير موجود، قم بتعيين mediaUrl
    else if (mediaFile != null &&
        mediaFile!.url != null &&
        (mediaUrl == null || mediaUrl!.isEmpty)) {
      mediaUrl = mediaFile!.url;
    }
  }

  // تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    final map = {
      'objectId': objectId,
      'text': text,
      'senderId': senderId,
      'senderName': senderName,
      'recipientId': recipientId,
      'conversationId': conversationId,
      'messageType': messageType,
      'mediaUrl': mediaUrl,
      'isRead': isRead,
      'isDelivered': isDelivered,
      'reactions': reactions,
      'replyTo': replyTo,
      'isDeleted': isDeleted,
      'deletedFor': deletedFor,
      'createdAt': createdAt?.toIso8601String(),
    };

    // إضافة معلومات mediaFile إذا كانت موجودة
    if (mediaFile != null && mediaFile!.url != null) {
      map['mediaFile'] = {
        'url': mediaFile!.url,
        'name': mediaFile!.name,
        'type': mediaFile!.type,
      };
    }

    return map;
  }

  // إنشاء نموذج من Map
  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    // إنشاء mediaFile إذا كان هناك mediaUrl أو mediaFile في الخريطة
    MediaFile? mediaFileObj;
    if (map['mediaUrl'] != null && map['mediaUrl'].isNotEmpty) {
      mediaFileObj = MediaFile(url: map['mediaUrl']);
    } else if (map['mediaFile'] != null) {
      if (map['mediaFile'] is String) {
        mediaFileObj = MediaFile(url: map['mediaFile']);
      } else if (map['mediaFile'] is Map) {
        final mediaFileMap = map['mediaFile'] as Map;
        mediaFileObj = MediaFile(
          url: mediaFileMap['url'],
          name: mediaFileMap['name'],
          type: mediaFileMap['type'],
        );
      }
    }

    return ChatMessage(
      objectId: map['objectId'],
      text: map['text'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      recipientId: map['recipientId'] ?? '',
      conversationId: map['conversationId'] ?? '',
      messageType: map['messageType'] ?? typeText,
      mediaUrl: map['mediaUrl'],
      mediaFile: mediaFileObj,
      isRead: map['isRead'] ?? false,
      isDelivered: map['isDelivered'] ?? false,
      reactions: Map<String, String>.from(map['reactions'] ?? {}),
      replyTo: map['replyTo'],
      isDeleted: map['isDeleted'] ?? false,
      deletedFor: List<String>.from(map['deletedFor'] ?? []),
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
    );
  }

  // دوال مساعدة للتوافق مع الكود القديم

  // الحصول على قيمة من الخريطة
  T? get<T>(String key) {
    switch (key) {
      case 'text':
        return text as T?;
      case 'senderId':
        return senderId as T?;
      case 'senderName':
        return senderName as T?;
      case 'recipientId':
        return recipientId as T?;
      case 'conversationId':
        return conversationId as T?;
      case 'messageType':
        return messageType as T?;
      case 'mediaFile':
        return mediaFile as T?;
      case 'isRead':
        return isRead as T?;
      case 'isDelivered':
        return isDelivered as T?;
      case 'reactions':
        return reactions as T?;
      case 'replyTo':
        return replyTo as T?;
      case 'isDeleted':
        return isDeleted as T?;
      case 'deletedFor':
        return deletedFor as T?;
      case 'createdAt':
        return createdAt as T?;
      case 'fileUrl':
        return mediaUrl as T?;
      case 'downloadPage':
        return null;
      case 'fileName':
        return mediaFile?.name as T?;
      case 'fileType':
        return mediaFile?.type as T?;
      default:
        return null;
    }
  }

  // تعيين قيمة في الخريطة
  void set<T>(String key, T? value) {
    switch (key) {
      case 'text':
        text = value as String;
        break;
      case 'senderId':
        senderId = value as String;
        break;
      case 'senderName':
        senderName = value as String;
        break;
      case 'recipientId':
        recipientId = value as String;
        break;
      case 'conversationId':
        conversationId = value as String;
        break;
      case 'messageType':
        messageType = value as String;
        break;
      case 'mediaFile':
        mediaFile = value as MediaFile?;
        if (mediaFile != null && mediaFile!.url != null) {
          mediaUrl = mediaFile!.url;
        }
        break;
      case 'isRead':
        isRead = value as bool;
        break;
      case 'isDelivered':
        isDelivered = value as bool;
        break;
      case 'reactions':
        reactions = value as Map<String, String>;
        break;
      case 'replyTo':
        replyTo = value as String?;
        break;
      case 'isDeleted':
        isDeleted = value as bool;
        break;
      case 'deletedFor':
        deletedFor = value as List<String>;
        break;
      case 'createdAt':
        createdAt = value as DateTime?;
        break;
      case 'fileUrl':
        mediaUrl = value as String?;
        if (mediaUrl != null && mediaUrl!.isNotEmpty) {
          if (mediaFile == null) {
            mediaFile = MediaFile(url: mediaUrl);
          } else {
            mediaFile!.url = mediaUrl;
          }
        }
        break;
      default:
        break;
    }
  }

  // حفظ الرسالة (للتوافق مع الكود القديم)
  Future<ParseResponse> save() async {
    // هذه الدالة للتوافق فقط، لا تقوم بأي شيء
    return ParseResponse(
      success: true,
      results: [this],
      statusCode: 200,
      error: null,
    );
  }
}

// فئة ParseResponse للتوافق مع الكود القديم
class ParseResponse {
  final bool success;
  final List<dynamic>? results;
  final int statusCode;
  final ParseError? error;

  ParseResponse({
    required this.success,
    this.results,
    required this.statusCode,
    this.error,
  });
}

// فئة ParseError للتوافق مع الكود القديم
class ParseError {
  final String message;
  final int code;

  ParseError({
    required this.message,
    required this.code,
  });
}
