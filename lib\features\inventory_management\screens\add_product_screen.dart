// بسم الله الرحمن الرحيم
// شاشة إضافة منتج - تتيح إضافة منتج جديد أو تعديل منتج موجود

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/product_model.dart';

import '../services/inventory_service.dart';
import '../services/barcode_service.dart';
import 'barcode_scanner_screen.dart';

/// شاشة إضافة منتج
class AddProductScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة إضافة منتج
  const AddProductScreen({
    super.key,
    this.product,
  });

  /// المنتج (للتعديل)
  final ProductModel? product;

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  bool _isLoading = false;
  bool _isEditing = false;
  final _formKey = GlobalKey<FormState>();

  // حقول النموذج
  final _nameController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _costController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitController = TextEditingController();
  final _minQuantityController = TextEditingController();
  final _maxQuantityController = TextEditingController();
  final _discountController = TextEditingController();
  final _taxController = TextEditingController();
  String? _selectedCategoryId;
  String? _selectedSupplierId;
  String? _selectedLocationId;
  bool _isActive = true;
  String? _imagePath;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.product != null;
    _initializeForm();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _barcodeController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _quantityController.dispose();
    _unitController.dispose();
    _minQuantityController.dispose();
    _maxQuantityController.dispose();
    _discountController.dispose();
    _taxController.dispose();
    super.dispose();
  }

  // تهيئة النموذج
  void _initializeForm() {
    if (_isEditing && widget.product != null) {
      final product = widget.product!;
      _nameController.text = product.name;
      _barcodeController.text = product.barcode;
      _descriptionController.text = product.description ?? '';
      _priceController.text = product.price.toString();
      _costController.text = product.cost.toString();
      _quantityController.text = product.quantity.toString();
      _unitController.text = product.unit;
      _minQuantityController.text = product.minQuantity.toString();
      _maxQuantityController.text = product.maxQuantity?.toString() ?? '';
      _discountController.text = product.discount.toString();
      _taxController.text = product.tax.toString();
      _selectedCategoryId = product.categoryId;
      _selectedSupplierId = product.supplierId;
      _selectedLocationId = product.locationId;
      _isActive = product.isActive;
      _imagePath = product.imagePath;
    } else {
      // قيم افتراضية للمنتج الجديد
      _unitController.text = 'قطعة';
      _minQuantityController.text = '5';
      _discountController.text = '0';
      _taxController.text = '0';
      _isActive = true;
    }
  }

  // حفظ المنتج
  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء نموذج المنتج
      final product = ProductModel(
        id: _isEditing ? widget.product!.id : const Uuid().v4(),
        name: _nameController.text,
        barcode: _barcodeController.text,
        categoryId: _selectedCategoryId ?? '',
        price: double.parse(_priceController.text),
        cost: double.parse(_costController.text),
        quantity: int.parse(_quantityController.text),
        createdAt: _isEditing ? widget.product!.createdAt : DateTime.now(),
        description: _descriptionController.text.isNotEmpty
            ? _descriptionController.text
            : null,
        imagePath: _imagePath,
        unit: _unitController.text,
        minQuantity: int.parse(_minQuantityController.text),
        maxQuantity: _maxQuantityController.text.isNotEmpty
            ? int.parse(_maxQuantityController.text)
            : null,
        discount: double.parse(_discountController.text),
        tax: double.parse(_taxController.text),
        isActive: _isActive,
        supplierId: _selectedSupplierId,
        locationId: _selectedLocationId,
        attributes: _isEditing ? widget.product!.attributes : {},
        updatedAt: DateTime.now(),
      );

      // حفظ المنتج
      final inventoryService = ref.read(inventoryServiceProvider);
      ProductModel? savedProduct;

      if (_isEditing) {
        savedProduct = await inventoryService.updateProduct(product);
      } else {
        savedProduct = await inventoryService.addProduct(product);
      }

      if (savedProduct != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditing
                  ? 'تم تحديث المنتج بنجاح'
                  : 'تم إضافة المنتج بنجاح'),
            ),
          );
          Navigator.pop(context, savedProduct);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  _isEditing ? 'فشل في تحديث المنتج' : 'فشل في إضافة المنتج'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // مسح الباركود
  Future<void> _scanBarcode() async {
    final barcode = await Navigator.push<String>(
      context,
      MaterialPageRoute(
        builder: (context) => const BarcodeScannerScreen(),
      ),
    );

    if (barcode != null && barcode.isNotEmpty) {
      setState(() {
        _barcodeController.text = barcode;
      });
    }
  }

  // إنشاء باركود عشوائي
  void _generateRandomBarcode() {
    final barcodeService = ref.read(barcodeServiceProvider);
    final barcode = barcodeService.generateRandomBarcode(BarcodeType.ean13);
    setState(() {
      _barcodeController.text = barcode;
    });
  }

  @override
  Widget build(BuildContext context) {
    final allCategoriesAsync = ref.watch(allCategoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل منتج' : 'إضافة منتج'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري الحفظ...',
            )
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // صورة المنتج
                    Center(
                      child: GestureDetector(
                        onTap: () {
                          // في الإصدار الحقيقي، يجب إضافة وظيفة لاختيار صورة
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text(
                                    'سيتم إضافة وظيفة اختيار الصورة قريبًا')),
                          );
                        },
                        child: Container(
                          width: 150,
                          height: 150,
                          decoration: BoxDecoration(
                            color: AppColors.lightGreyColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.greyTextColor),
                          ),
                          child: _imagePath != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    _imagePath!,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : const Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_a_photo,
                                      size: 50,
                                      color: AppColors.greyTextColor,
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'إضافة صورة',
                                      style: TextStyle(
                                        color: AppColors.greyTextColor,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // معلومات المنتج الأساسية
                    const Text(
                      'معلومات المنتج',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // اسم المنتج
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المنتج *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم المنتج';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // الباركود
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _barcodeController,
                            decoration: const InputDecoration(
                              labelText: 'الباركود *',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الباركود';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.qr_code_scanner),
                          onPressed: _scanBarcode,
                          tooltip: 'مسح الباركود',
                        ),
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _generateRandomBarcode,
                          tooltip: 'إنشاء باركود عشوائي',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // الفئة
                    allCategoriesAsync.when(
                      data: (categories) {
                        return DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الفئة *',
                            border: OutlineInputBorder(),
                          ),
                          value: _selectedCategoryId,
                          items: categories.map((category) {
                            return DropdownMenuItem<String>(
                              value: category.id,
                              child: Text(category.name),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategoryId = value;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار الفئة';
                            }
                            return null;
                          },
                        );
                      },
                      loading: () => const CircularProgressIndicator(),
                      error: (error, stackTrace) => Text('حدث خطأ: $error'),
                    ),
                    const SizedBox(height: 16),

                    // الوصف
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'الوصف',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),

                    // معلومات السعر
                    const Text(
                      'معلومات السعر',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // سعر البيع
                    TextFormField(
                      controller: _priceController,
                      decoration: const InputDecoration(
                        labelText: 'سعر البيع *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال سعر البيع';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال سعر صحيح';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // تكلفة الشراء
                    TextFormField(
                      controller: _costController,
                      decoration: const InputDecoration(
                        labelText: 'تكلفة الشراء *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.money),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال تكلفة الشراء';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال تكلفة صحيحة';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // نسبة الخصم والضريبة
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _discountController,
                            decoration: const InputDecoration(
                              labelText: 'نسبة الخصم (%)',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.discount),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d+\.?\d{0,2}')),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return null;
                              }
                              final discount = double.tryParse(value);
                              if (discount == null) {
                                return 'يرجى إدخال نسبة صحيحة';
                              }
                              if (discount < 0 || discount > 100) {
                                return 'النسبة يجب أن تكون بين 0 و 100';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _taxController,
                            decoration: const InputDecoration(
                              labelText: 'نسبة الضريبة (%)',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.receipt),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d+\.?\d{0,2}')),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return null;
                              }
                              final tax = double.tryParse(value);
                              if (tax == null) {
                                return 'يرجى إدخال نسبة صحيحة';
                              }
                              if (tax < 0 || tax > 100) {
                                return 'النسبة يجب أن تكون بين 0 و 100';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // معلومات المخزون
                    const Text(
                      'معلومات المخزون',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // الكمية
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _quantityController,
                            decoration: const InputDecoration(
                              labelText: 'الكمية *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.inventory),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الكمية';
                              }
                              if (int.tryParse(value) == null) {
                                return 'يرجى إدخال كمية صحيحة';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _unitController,
                            decoration: const InputDecoration(
                              labelText: 'وحدة القياس *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.straighten),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال وحدة القياس';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // الحد الأدنى والأقصى للكمية
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _minQuantityController,
                            decoration: const InputDecoration(
                              labelText: 'الحد الأدنى للكمية *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.arrow_downward),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الحد الأدنى للكمية';
                              }
                              if (int.tryParse(value) == null) {
                                return 'يرجى إدخال كمية صحيحة';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _maxQuantityController,
                            decoration: const InputDecoration(
                              labelText: 'الحد الأقصى للكمية',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.arrow_upward),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return null;
                              }
                              if (int.tryParse(value) == null) {
                                return 'يرجى إدخال كمية صحيحة';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // معلومات إضافية
                    const Text(
                      'معلومات إضافية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // حالة المنتج
                    SwitchListTile(
                      title: const Text('المنتج نشط'),
                      subtitle: const Text('المنتج متاح للبيع'),
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                    ),
                    const SizedBox(height: 32),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _saveProduct,
                        child:
                            Text(_isEditing ? 'تحديث المنتج' : 'إضافة المنتج'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
