import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../model/admin_notification_model.dart';
import '../services/admin_notifications_service.dart';

/// موفر بيانات الإشعارات الإدارية
class AdminNotificationsProvider extends ChangeNotifier {
  List<AdminNotificationModel> _allNotifications = [];
  List<AdminNotificationModel> _activeNotifications = [];
  Map<String, dynamic> _stats = {};
  bool _isLoading = false;
  String? _error;
  StreamSubscription? _activeNotificationsSubscription;

  // Getters
  List<AdminNotificationModel> get allNotifications => _allNotifications;
  List<AdminNotificationModel> get activeNotifications => _activeNotifications;
  Map<String, dynamic> get stats => _stats;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// تحميل جميع الإشعارات الإدارية
  Future<void> loadAllNotifications() async {
    _setLoading(true);
    _clearError();

    try {
      _allNotifications = await AdminNotificationsService.getAllAdminNotifications();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الإشعارات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الإشعارات النشطة
  Future<void> loadActiveNotifications() async {
    _setLoading(true);
    _clearError();

    try {
      _activeNotifications = await AdminNotificationsService.getActiveNotifications();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الإشعارات النشطة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// مراقبة الإشعارات النشطة في الوقت الفعلي
  void watchActiveNotifications() {
    _activeNotificationsSubscription?.cancel();
    _activeNotificationsSubscription = AdminNotificationsService
        .watchActiveNotifications()
        .listen(
      (notifications) {
        _activeNotifications = notifications;
        notifyListeners();
      },
      onError: (error) {
        _setError('خطأ في مراقبة الإشعارات: $error');
      },
    );
  }

  /// إرسال إشعار إداري جديد
  Future<bool> sendNotification({
    required String title,
    required String message,
    required String type,
    String? actionUrl,
    String? imageUrl,
    Map<String, dynamic>? customData,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await AdminNotificationsService.sendAdminNotification(
        title: title,
        message: message,
        type: type,
        actionUrl: actionUrl,
        imageUrl: imageUrl,
        customData: customData,
      );

      if (success) {
        // إعادة تحميل البيانات
        await loadAllNotifications();
        await loadActiveNotifications();
        await loadStats();
      }

      return success;
    } catch (e) {
      _setError('خطأ في إرسال الإشعار: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث حالة الإشعار
  Future<bool> updateNotificationStatus(String notificationId, bool isActive) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await AdminNotificationsService.updateNotificationStatus(
        notificationId,
        isActive,
      );

      if (success) {
        // تحديث البيانات المحلية
        final index = _allNotifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _allNotifications[index] = _allNotifications[index].copyWith(
            isActive: isActive,
            updatedAt: DateTime.now(),
          );
        }

        // إعادة تحميل الإشعارات النشطة
        await loadActiveNotifications();
        await loadStats();
        notifyListeners();
      }

      return success;
    } catch (e) {
      _setError('خطأ في تحديث حالة الإشعار: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف إشعار إداري
  Future<bool> deleteNotification(String notificationId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await AdminNotificationsService.deleteAdminNotification(notificationId);

      if (success) {
        // إزالة الإشعار من البيانات المحلية
        _allNotifications.removeWhere((n) => n.id == notificationId);
        _activeNotifications.removeWhere((n) => n.id == notificationId);
        await loadStats();
        notifyListeners();
      }

      return success;
    } catch (e) {
      _setError('خطأ في حذف الإشعار: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إرسال إشعار طوارئ
  Future<bool> sendEmergencyNotification({
    required String title,
    required String message,
    String? actionUrl,
  }) async {
    return await AdminNotificationsService.sendEmergencyNotification(
      title: title,
      message: message,
      actionUrl: actionUrl,
    );
  }

  /// إرسال إشعار صيانة
  Future<bool> sendMaintenanceNotification({
    required String title,
    required String message,
    DateTime? scheduledTime,
    String? estimatedDuration,
  }) async {
    return await AdminNotificationsService.sendMaintenanceNotification(
      title: title,
      message: message,
      scheduledTime: scheduledTime,
      estimatedDuration: estimatedDuration,
    );
  }

  /// إرسال إشعار تحديث
  Future<bool> sendUpdateNotification({
    required String title,
    required String message,
    String? version,
    String? downloadUrl,
  }) async {
    return await AdminNotificationsService.sendUpdateNotification(
      title: title,
      message: message,
      version: version,
      downloadUrl: downloadUrl,
    );
  }

  /// إرسال إشعار ترويجي
  Future<bool> sendPromotionalNotification({
    required String title,
    required String message,
    String? imageUrl,
    String? actionUrl,
    DateTime? expiryDate,
  }) async {
    return await AdminNotificationsService.sendPromotionalNotification(
      title: title,
      message: message,
      imageUrl: imageUrl,
      actionUrl: actionUrl,
      expiryDate: expiryDate,
    );
  }

  /// تحميل إحصائيات الإشعارات
  Future<void> loadStats() async {
    try {
      _stats = await AdminNotificationsService.getNotificationStats();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات الإشعارات: $e');
    }
  }

  /// البحث في الإشعارات
  List<AdminNotificationModel> searchNotifications(String query) {
    if (query.isEmpty) return _allNotifications;

    return _allNotifications.where((notification) {
      return notification.title.toLowerCase().contains(query.toLowerCase()) ||
          notification.message.toLowerCase().contains(query.toLowerCase()) ||
          notification.type.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// فلترة الإشعارات حسب النوع
  List<AdminNotificationModel> filterByType(String type) {
    return _allNotifications.where((notification) => notification.type == type).toList();
  }

  /// فلترة الإشعارات حسب الحالة
  List<AdminNotificationModel> filterByStatus(bool isActive) {
    return _allNotifications.where((notification) => notification.isActive == isActive).toList();
  }

  /// الحصول على الإشعارات الطارئة
  List<AdminNotificationModel> get emergencyNotifications {
    return _allNotifications.where((notification) => notification.isEmergency).toList();
  }

  /// الحصول على الإشعارات المنتهية الصلاحية
  List<AdminNotificationModel> get expiredNotifications {
    return _allNotifications.where((notification) => notification.isExpired).toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    _activeNotificationsSubscription?.cancel();
    super.dispose();
  }
}

/// موفر Riverpod للإشعارات الإدارية
final adminNotificationsProvider = ChangeNotifierProvider<AdminNotificationsProvider>((ref) {
  return AdminNotificationsProvider();
});

/// موفر للإشعارات النشطة فقط (للمستخدمين)
final activeNotificationsProvider = StreamProvider<List<AdminNotificationModel>>((ref) {
  return AdminNotificationsService.watchActiveNotifications();
});
