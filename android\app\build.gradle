// plugins {
//     id "com.android.application"
//     id "kotlin-android"
//     id "dev.flutter.flutter-gradle-plugin"
//     id "com.google.gms.google-services"
//     id "com.google.firebase.crashlytics"
// }
plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "amrdev.pos.user.omarhasany"
    compileSdkVersion 36
    ndkVersion "27.0.12077973"

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
        abortOnError false
        quiet true
        warningsAsErrors false
        ignoreWarnings true
        disable 'GradleCompatible', 'GradleDependency', 'NewerVersionAvailable'
        disable 'DeprecatedApi', 'UnsafeOptInUsageError'
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        coreLibraryDesugaringEnabled true
    }

    tasks.withType(JavaCompile) {
        // إخفاء تحذيرات Java
        options.compilerArgs << '-Xlint:none'
        options.compilerArgs << '-Xlint:-options'
        options.compilerArgs << '-Xlint:-deprecation'
        options.compilerArgs << '-Xlint:-unchecked'
        options.compilerArgs << '-Xlint:-removal'
        options.compilerArgs << '-nowarn'
        options.warnings = false
        options.deprecation = false
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11
        freeCompilerArgs += [
            '-opt-in=kotlin.RequiresOptIn',
            '-Xno-param-assertions',
            '-Xno-call-assertions',
            '-Xno-receiver-assertions',
            '-Xsuppress-version-warnings'
        ]
        // إخفاء تحذيرات Kotlin
        suppressWarnings = true
        allWarningsAsErrors = false
    }

    defaultConfig {
        applicationId "amrdev.pos.user.omarhasany"
        minSdkVersion 24
        targetSdkVersion 36
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

   signingConfigs {
    release {
        keyAlias keystoreProperties['keyAlias']
        keyPassword keystoreProperties['keyPassword']
        storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
        storePassword keystoreProperties['storePassword']
    }
}

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // implementation "com.google.firebase:firebase-messaging:24.1.0"
    // implementation 'com.google.firebase:firebase-database:21.0.0'
    // implementation("com.google.firebase:firebase-appcheck")
    // ////////////////////////
    // implementation 'androidx.multidex:multidex:2.0.1'
    // implementation platform("com.google.firebase:firebase-bom:33.9.0")
    // implementation 'com.google.firebase:firebase-auth-ktx:23.2.0'
    // implementation 'com.google.firebase:firebase-analytics-ktx'
    // implementation 'com.google.firebase:firebase-firestore-ktx'
    // implementation 'com.google.android.gms:play-services-auth:21.3.0'
    // implementation 'com.google.android.material:material:1.10.0'
    // implementation 'androidx.core:core-ktx:1.12.0'
    implementation "com.google.firebase:firebase-messaging:24.1.0"
    implementation 'com.google.firebase:firebase-database:21.0.0'
    implementation("com.google.firebase:firebase-appcheck")
    // //////////////////////
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation platform("com.google.firebase:firebase-bom:33.9.0")
    implementation 'com.google.firebase:firebase-auth-ktx:23.2.0'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.core:core-ktx:1.12.0'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'


}

