// استخدام التاريخ العادي بدلاً من Timestamp

class QuotationModel {
  final String id;
  final String customerName;
  final String customerPhone;
  final String customerEmail;
  final String customerAddress;
  final DateTime date;
  final double totalAmount;
  final double discount;
  final double tax;
  final double finalAmount;
  final String status; // pending, accepted, rejected
  final String notes;
  final List<QuotationItemModel> items;

  QuotationModel({
    required this.id,
    required this.customerName,
    required this.customerPhone,
    required this.customerEmail,
    required this.customerAddress,
    required this.date,
    required this.totalAmount,
    required this.discount,
    required this.tax,
    required this.finalAmount,
    required this.status,
    required this.notes,
    required this.items,
  });

  // تحويل من Firestore
  factory QuotationModel.fromMap(Map<String, dynamic> map, String id) {
    List<QuotationItemModel> itemsList = [];
    if (map['items'] != null && map['items'] is List) {
      itemsList = List<QuotationItemModel>.from(
        (map['items'] as List).map(
          (item) => QuotationItemModel.fromMap(item),
        ),
      );
    }

    return QuotationModel(
      id: id,
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
      customerEmail: map['customerEmail'] ?? '',
      customerAddress: map['customerAddress'] ?? '',
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] ?? 0),
      totalAmount: (map['totalAmount'] ?? 0).toDouble(),
      discount: (map['discount'] ?? 0).toDouble(),
      tax: (map['tax'] ?? 0).toDouble(),
      finalAmount: (map['finalAmount'] ?? 0).toDouble(),
      status: map['status'] ?? 'pending',
      notes: map['notes'] ?? '',
      items: itemsList,
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'customerAddress': customerAddress,
      'date': date.millisecondsSinceEpoch,
      'totalAmount': totalAmount,
      'discount': discount,
      'tax': tax,
      'finalAmount': finalAmount,
      'status': status,
      'notes': notes,
      'items': items.map((item) => item.toMap()).toList(),
    };
  }

  // نسخة مع تعديلات
  QuotationModel copyWith({
    String? id,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    String? customerAddress,
    DateTime? date,
    double? totalAmount,
    double? discount,
    double? tax,
    double? finalAmount,
    String? status,
    String? notes,
    List<QuotationItemModel>? items,
  }) {
    return QuotationModel(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerEmail: customerEmail ?? this.customerEmail,
      customerAddress: customerAddress ?? this.customerAddress,
      date: date ?? this.date,
      totalAmount: totalAmount ?? this.totalAmount,
      discount: discount ?? this.discount,
      tax: tax ?? this.tax,
      finalAmount: finalAmount ?? this.finalAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      items: items ?? this.items,
    );
  }
}

class QuotationItemModel {
  final String productId;
  final String productName;
  final double quantity;
  final double unitPrice;
  final double totalPrice;

  QuotationItemModel({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  // تحويل من Map
  factory QuotationItemModel.fromMap(Map<dynamic, dynamic> map) {
    return QuotationItemModel(
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      quantity: (map['quantity'] ?? 0).toDouble(),
      unitPrice: (map['unitPrice'] ?? 0).toDouble(),
      totalPrice: (map['totalPrice'] ?? 0).toDouble(),
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
    };
  }

  // نسخة مع تعديلات
  QuotationItemModel copyWith({
    String? productId,
    String? productName,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
  }) {
    return QuotationItemModel(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}
