import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/item_model.dart';
// import 'package:mobile_pos/services/item_service.dart';

final itemReportProvider = FutureProvider<List<ItemModel>>((ref) async {
  final itemService = ref.read(itemServiceProvider);
  return await itemService.fetchItems();
});

class ItemReportProvider {
  Future<List<ItemModel>> fetchItems() async {
    // تنفيذ منطق جلب البيانات هنا
    // على سبيل المثال، يمكنك جلب البيانات من API أو قاعدة بيانات محلية
    return [];
  }
}

class ItemService {
  Future<List<ItemModel>> fetchItems() async {
    // تنفيذ منطق جلب البيانات هنا
    // على سبيل المثال، يمكنك جلب البيانات من API أو قاعدة بيانات محلية
    return [];
  }
}

final itemServiceProvider = Provider<ItemService>((ref) {
  return ItemService();
});
