import 'package:firebase_database/firebase_database.dart';

import 'package:mobile_pos/models/quotation_model.dart';

class QuotationRepository {
  final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // الحصول على قائمة عروض الأسعار
  Stream<List<QuotationModel>> getQuotationList() {
    return _database.child('Quotations').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<QuotationModel> quotations = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> quotationMap = {};
          value.forEach((k, v) {
            quotationMap[k.toString()] = v;
          });
          quotations.add(QuotationModel.fromMap(quotationMap, key.toString()));
        }
      });
      return quotations;
    });
  }

  // إضافة عرض سعر جديد
  Future<void> addQuotation(QuotationModel quotation) async {
    try {
      final newQuotationRef = _database.child('Quotations').push();
      await newQuotationRef.set(quotation.toMap());
    } catch (e) {
      // خطأ في إضافة عرض سعر
      rethrow;
    }
  }

  // تحديث عرض سعر
  Future<void> updateQuotation(QuotationModel quotation) async {
    try {
      await _database
          .child('Quotations/${quotation.id}')
          .update(quotation.toMap());
    } catch (e) {
      // خطأ في تحديث عرض سعر
      rethrow;
    }
  }

  // حذف عرض سعر
  Future<void> deleteQuotation(String quotationId) async {
    try {
      await _database.child('Quotations/$quotationId').remove();
    } catch (e) {
      // خطأ في حذف عرض سعر
      rethrow;
    }
  }

  // تغيير حالة عرض السعر
  Future<void> changeQuotationStatus(String quotationId, String status) async {
    try {
      await _database
          .child('Quotations/$quotationId')
          .update({'status': status});
    } catch (e) {
      // خطأ في تغيير حالة عرض السعر
      rethrow;
    }
  }

  // تحويل عرض السعر إلى فاتورة
  Future<void> convertToInvoice(QuotationModel quotation) async {
    try {
      // تحديث حالة عرض السعر
      await _database
          .child('Quotations/${quotation.id}')
          .update({'status': 'converted'});

      // إنشاء فاتورة جديدة (يمكن تعديل هذا الجزء حسب هيكل الفواتير في التطبيق)
      final newInvoiceRef = _database.child('Sales').push();

      // تحويل بيانات عرض السعر إلى بيانات الفاتورة
      Map<String, dynamic> invoiceData = {
        'customerName': quotation.customerName,
        'customerPhone': quotation.customerPhone,
        'customerAddress': quotation.customerAddress,
        'invoiceDate': quotation.date.millisecondsSinceEpoch,
        'totalAmount': quotation.totalAmount,
        'discount': quotation.discount,
        'tax': quotation.tax,
        'dueAmount': 0, // يمكن تعديل هذا حسب متطلبات التطبيق
        'paidAmount': quotation.finalAmount,
        'paymentType': 'cash', // يمكن تعديل هذا حسب متطلبات التطبيق
        'products': quotation.items
            .map((item) => {
                  'productId': item.productId,
                  'productName': item.productName,
                  'quantity': item.quantity,
                  'unitPrice': item.unitPrice,
                  'totalPrice': item.totalPrice,
                })
            .toList(),
        'quotationId': quotation.id, // للإشارة إلى عرض السعر الأصلي
      };

      await newInvoiceRef.set(invoiceData);
    } catch (e) {
      // خطأ في تحويل عرض السعر إلى فاتورة
      rethrow;
    }
  }
}
