import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';

import '../currency.dart';
import '../model/due_transaction_model.dart';

class TransitionRepo {
  Future<List<SalesTransitionModel>> getAllTransition() async {
    List<SalesTransitionModel> transitionList = [];

    // استخدام FirebaseDatabaseService بدلاً من الاستدعاء المباشر
    final transitionRef = FirebaseDatabaseService.getReference(
        '$constUserId/Sales Transition',
        keepSynced: true);

    try {
      final snapshot = await transitionRef.orderByKey().get();

      for (var element in snapshot.children) {
        SalesTransitionModel data = SalesTransitionModel.fromJson(
            jsonDecode(jsonEncode(element.value)));
        data.key = element.key;
        transitionList.add(data);
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات المبيعات: $e');
    }

    return transitionList;
  }
}

class PurchaseTransitionRepo {
  Future<List<dynamic>> getAllTransition() async {
    List<dynamic> transitionList = [];

    // استخدام FirebaseDatabaseService بدلاً من الاستدعاء المباشر
    final purchaseTransitionRef = FirebaseDatabaseService.getReference(
        '$constUserId/Purchase Transition',
        keepSynced: true);

    try {
      final snapshot = await purchaseTransitionRef.orderByKey().get();

      for (var element in snapshot.children) {
        PurchaseTransactionModel data = PurchaseTransactionModel.fromJson(
            jsonDecode(jsonEncode(element.value)));
        data.key = element.key;
        transitionList.add(data);
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات المشتريات: $e');
    }

    return transitionList;
  }
}

class DueTransitionRepo {
  Future<List<DueTransactionModel>> getAllTransition() async {
    List<DueTransactionModel> transitionList = [];

    // استخدام FirebaseDatabaseService بدلاً من الاستدعاء المباشر
    final dueTransitionRef = FirebaseDatabaseService.getReference(
        '$constUserId/Due Transaction',
        keepSynced: true);

    try {
      final snapshot = await dueTransitionRef.orderByKey().get();

      for (var element in snapshot.children) {
        transitionList.add(DueTransactionModel.fromJson(
            jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات المستحقات: $e');
    }

    return transitionList;
  }
}
