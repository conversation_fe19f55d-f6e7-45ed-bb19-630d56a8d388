import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/app_update_model.dart';
import 'package:mobile_pos/repositories/app_update_repository.dart';
import 'package:mobile_pos/repositories/app_update_repository_impl.dart';

/// مزود مستودع التحديثات
final appUpdateRepositoryProvider = Provider<AppUpdateRepository>((ref) {
  return AppUpdateRepositoryImpl();
});

/// مزود حالة التحقق من التحديثات
final appUpdateCheckProvider =
    FutureProvider.autoDispose<AppUpdateModel>((ref) async {
  final repository = ref.watch(appUpdateRepositoryProvider);
  return await repository.checkForUpdates();
});

/// مزود حالة التحقق من التحديثات مع التحديث الإجباري
final appUpdateCheckForceProvider =
    FutureProvider.autoDispose<AppUpdateModel>((ref) async {
  final repository = ref.watch(appUpdateRepositoryProvider);
  return await repository.checkForUpdates(force: true);
});

/// مزود سجل التغييرات
final changelogProvider =
    FutureProvider.autoDispose<List<ChangelogModel>>((ref) async {
  final repository = ref.watch(appUpdateRepositoryProvider);
  return await repository.getChangelogHistory();
});

/// مزود حالة التحديث التلقائي
final autoUpdateEnabledProvider =
    StateNotifierProvider<AutoUpdateNotifier, bool>((ref) {
  final repository = ref.watch(appUpdateRepositoryProvider);
  return AutoUpdateNotifier(repository);
});

/// مزود حالة التنزيل
final downloadStatusProvider = StateNotifierProvider.autoDispose<
    DownloadStatusNotifier, AsyncValue<DownloadStatus>>((ref) {
  return DownloadStatusNotifier(ref.watch(appUpdateRepositoryProvider));
});

/// مزود للتحقق من وجود ملف تحديث محمل مسبقًا
final downloadedUpdateProvider = FutureProvider.family<String?, String>(
  (ref, version) async {
    final repository = ref.watch(appUpdateRepositoryProvider);
    return repository.checkForDownloadedUpdate(version);
  },
);

/// مدير حالة التحديث التلقائي
class AutoUpdateNotifier extends StateNotifier<bool> {
  final AppUpdateRepository _repository;

  AutoUpdateNotifier(this._repository) : super(true) {
    _loadInitialState();
  }

  Future<void> _loadInitialState() async {
    state = await _repository.getAutoUpdateEnabled();
  }

  Future<void> setEnabled(bool enabled) async {
    await _repository.setAutoUpdateEnabled(enabled);
    state = enabled;
  }
}

/// مدير حالة التنزيل
class DownloadStatusNotifier extends StateNotifier<AsyncValue<DownloadStatus>> {
  final AppUpdateRepository _repository;

  DownloadStatusNotifier(this._repository)
      : super(AsyncValue.data(DownloadStatus()));

  // إعادة تعيين حالة التنزيل
  void reset() {
    debugPrint('إعادة تعيين حالة التنزيل');
    state = AsyncValue.data(DownloadStatus());
  }

  Future<void> startDownload(AppUpdateModel updateInfo) async {
    debugPrint('بدء تنزيل التحديث من المزود...');
    state = const AsyncValue.loading();

    try {
      debugPrint('جاري طلب تدفق التنزيل من المستودع...');
      final downloadStream = await _repository.downloadUpdate(updateInfo);
      debugPrint('تم الحصول على تدفق التنزيل بنجاح');

      // إضافة حالة أولية للتنزيل
      state = AsyncValue.data(DownloadStatus(
        isDownloading: true,
        progress: 0.0,
        downloadedBytes: '0 KB',
        totalBytes: '${updateInfo.updateSize} MB',
      ));

      debugPrint('تم تعيين حالة التنزيل الأولية، جاري الاستماع للتحديثات...');

      downloadStream.listen(
        (status) {
          debugPrint(
              'تم استلام تحديث حالة التنزيل: ${status.progress.toStringAsFixed(2)}');
          state = AsyncValue.data(status);
        },
        onError: (error) {
          debugPrint('خطأ في تدفق التنزيل: $error');
          state = AsyncValue.error(error, StackTrace.current);
        },
        onDone: () {
          debugPrint('اكتمل تدفق التنزيل');
          // التنزيل اكتمل بنجاح، لا نحتاج لفعل أي شيء هنا
          // لأن آخر حالة ستكون isCompleted = true
        },
      );
    } catch (e, stack) {
      debugPrint('استثناء أثناء بدء التنزيل: $e');
      debugPrint('تتبع الاستثناء: $stack');
      state = AsyncValue.error(e, stack);
    }
  }

  Future<bool> installUpdate() async {
    debugPrint('محاولة تثبيت التحديث...');

    // التحقق من حالة التنزيل
    if (state is AsyncError) {
      debugPrint('خطأ: حالة التنزيل في حالة خطأ');
      return false;
    }

    if (state is AsyncLoading) {
      debugPrint('خطأ: التنزيل لا يزال قيد التقدم');
      return false;
    }

    if (state.value == null) {
      debugPrint('خطأ: حالة التنزيل غير متوفرة');
      return false;
    }

    final downloadStatus = state.value!;
    debugPrint(
        'حالة التنزيل: isCompleted=${downloadStatus.isCompleted}, filePath=${downloadStatus.filePath}');

    // التحقق من اكتمال التنزيل ووجود مسار الملف
    if (downloadStatus.isCompleted && downloadStatus.filePath.isNotEmpty) {
      debugPrint('جاري تثبيت التحديث من المسار: ${downloadStatus.filePath}');
      return await _repository.installUpdate(downloadStatus.filePath);
    } else {
      if (!downloadStatus.isCompleted) {
        debugPrint('خطأ: التنزيل غير مكتمل');
      }
      if (downloadStatus.filePath.isEmpty) {
        debugPrint('خطأ: مسار الملف غير متوفر');
      }
      return false;
    }
  }

  // تم نقل هذه الدالة إلى الأعلى مع إضافة رسائل تصحيح
}
