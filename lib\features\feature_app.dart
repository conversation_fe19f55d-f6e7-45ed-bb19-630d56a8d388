// بسم الله الرحمن الرحيم
// تطبيق الميزات - نقطة الدخول الرئيسية لتطبيق الميزات

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'core/feature_manager.dart';
import 'core/theme/app_theme.dart';
import 'feature_registry.dart';

/// مزود مدير الميزات
final featureManagerProvider = Provider<FeatureManager>((ref) {
  return FeatureManager();
});

/// تطبيق الميزات - يمكن تشغيله كتطبيق مستقل أو دمجه مع التطبيق الرئيسي
class FeatureApp extends StatefulWidget {
  const FeatureApp({super.key});

  @override
  State<FeatureApp> createState() => _FeatureAppState();
}

class _FeatureAppState extends State<FeatureApp> {
  @override
  void initState() {
    super.initState();
    // تسجيل جميع الميزات
    FeatureRegistry().registerAllFeatures();

    // تهيئة الميزات المفعلة
    FeatureRegistry().initializeEnabledFeatures();
  }

  @override
  void dispose() {
    // إيقاف مدير الميزات
    FeatureManager().dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'تطبيق الميزات',
        theme: AppTheme.lightTheme,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ar', ''), // العربية
        ],
        locale: const Locale('ar', ''),
        routes: FeatureManager().getAllRoutes(),
        home: const FeatureHome(),
      ),
    );
  }
}

/// الشاشة الرئيسية لتطبيق الميزات
class FeatureHome extends ConsumerWidget {
  const FeatureHome({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final featureManager = ref.watch(featureManagerProvider);
    final enabledFeatures = featureManager.getEnabledFeatures();

    return Scaffold(
      appBar: AppBar(
        title: const Text('الميزات المتاحة'),
        centerTitle: true,
      ),
      body: enabledFeatures.isEmpty
          ? const Center(
              child: Text(
                'لا توجد ميزات مفعلة',
                style: TextStyle(fontSize: 18),
              ),
            )
          : ListView.builder(
              itemCount: enabledFeatures.length,
              itemBuilder: (context, index) {
                final feature = enabledFeatures[index];
                return ListTile(
                  leading: Icon(feature.featureIcon),
                  title: Text(feature.featureName),
                  subtitle: Text(feature.featureDescription),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => feature.getMainScreen(),
                      ),
                    );
                  },
                );
              },
            ),
    );
  }
}
