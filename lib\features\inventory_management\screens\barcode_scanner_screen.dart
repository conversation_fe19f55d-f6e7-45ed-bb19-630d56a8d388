// بسم الله الرحمن الرحيم
// شاشة ماسح الباركود - تتيح مسح الباركود باستخدام الكاميرا

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../core/theme/app_theme.dart';

import '../services/inventory_service.dart';
import 'product_details_screen.dart';

/// شاشة ماسح الباركود
class BarcodeScannerScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة ماسح الباركود
  const BarcodeScannerScreen({super.key});

  @override
  ConsumerState<BarcodeScannerScreen> createState() =>
      _BarcodeScannerScreenState();
}

class _BarcodeScannerScreenState extends ConsumerState<BarcodeScannerScreen> {
  final MobileScannerController _scannerController = MobileScannerController();
  bool _isScanning = true;
  String? _lastScannedBarcode;
  bool _isSearching = false;
  bool _isFlashOn = false;
  bool _isFrontCamera = false;

  @override
  void dispose() {
    _scannerController.dispose();
    super.dispose();
  }

  // معالجة نتيجة المسح
  Future<void> _onDetect(BarcodeCapture capture) async {
    if (!_isScanning || _isSearching) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) return;

    final barcode = barcodes.first.rawValue;
    if (barcode == null || barcode.isEmpty || barcode == _lastScannedBarcode) {
      return;
    }

    setState(() {
      _isScanning = false;
      _lastScannedBarcode = barcode;
      _isSearching = true;
    });

    // معالجة الباركود
    await _processBarcode(barcode);
  }

  // معالجة الباركود
  Future<void> _processBarcode(String barcode) async {
    try {
      // البحث عن المنتج بالباركود
      final product =
          await ref.read(inventoryServiceProvider).getProductByBarcode(barcode);

      if (mounted) {
        if (product != null) {
          // إذا تم العثور على المنتج، عرض تفاصيله
          final result = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('تم العثور على المنتج'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('الاسم: ${product.name}'),
                  Text('الباركود: ${product.barcode}'),
                  Text('السعر: ${product.price}'),
                  Text('الكمية: ${product.quantity}'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('مسح مرة أخرى'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('عرض التفاصيل'),
                ),
              ],
            ),
          );

          if (result == true) {
            if (mounted) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => ProductDetailsScreen(product: product),
                ),
              );
            }
          } else {
            // استئناف المسح
            setState(() {
              _isScanning = true;
              _isSearching = false;
            });
          }
        } else {
          // إذا لم يتم العثور على المنتج، عرض خيارات
          final result = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('لم يتم العثور على المنتج'),
              content: Text('لم يتم العثور على منتج بالباركود: $barcode'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('مسح مرة أخرى'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('إرجاع الباركود'),
                ),
              ],
            ),
          );

          if (result == true) {
            if (mounted) {
              Navigator.pop(context, barcode);
            }
          } else {
            // استئناف المسح
            setState(() {
              _isScanning = true;
              _isSearching = false;
            });
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
        setState(() {
          _isScanning = true;
          _isSearching = false;
        });
      }
    }
  }

  // تبديل الفلاش
  void _toggleFlash() {
    _scannerController.toggleTorch();
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
  }

  // تبديل الكاميرا
  void _toggleCamera() {
    _scannerController.switchCamera();
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مسح الباركود'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(_isFlashOn ? Icons.flash_on : Icons.flash_off),
            onPressed: _toggleFlash,
            tooltip: _isFlashOn ? 'إيقاف الفلاش' : 'تشغيل الفلاش',
          ),
          IconButton(
            icon: Icon(_isFrontCamera ? Icons.camera_front : Icons.camera_rear),
            onPressed: _toggleCamera,
            tooltip: _isFrontCamera ? 'الكاميرا الخلفية' : 'الكاميرا الأمامية',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                // ماسح الباركود
                MobileScanner(
                  controller: _scannerController,
                  onDetect: _onDetect,
                ),

                // إطار المسح
                Center(
                  child: Container(
                    width: 250,
                    height: 250,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.mainColor,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),

                // مؤشر المسح
                if (_isScanning && !_isSearching)
                  const Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: SizedBox(
                      height: 3,
                      child: LinearProgressIndicator(
                        backgroundColor: Colors.transparent,
                      ),
                    ),
                  ),

                // مؤشر البحث
                if (_isSearching)
                  Container(
                    color: Colors.black54,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
              ],
            ),
          ),

          // تعليمات المسح
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[200],
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تعليمات المسح:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text('1. وجه الكاميرا نحو الباركود'),
                const Text('2. حافظ على ثبات الكاميرا'),
                const Text('3. تأكد من وضوح الباركود وإضاءة كافية'),
                const SizedBox(height: 8),
                if (_lastScannedBarcode != null)
                  Text(
                    'آخر باركود تم مسحه: $_lastScannedBarcode',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
