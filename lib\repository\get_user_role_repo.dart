// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/model/user_role_model.dart';
import '../currency.dart';

class UserRoleRepo {
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<String> getUserID() async {
    return auth.currentUser?.uid ?? '';
  }

  Future<List<UserRoleModel>> getAllUserRole() async {
    List<UserRoleModel> allUser = [];
    try {
      String userId = await getUserID();

      // التحقق من صحة معرف المستخدم
      if (userId.isEmpty && constUserId.isEmpty) {
        print('لا يوجد معرف مستخدم صالح');
        return allUser;
      }

      DatabaseReference userRef;

      if (Platform.isWindows) {
        userRef = FirebaseDatabase.instance
            .ref()
            .child('users')
            .child(userId)
            .child('User Role');
      } else {
        userRef = FirebaseDatabase.instance.ref(constUserId).child('User Role');
      }

      DatabaseEvent event = await userRef.orderByKey().once();
      if (event.snapshot.value != null) {
        for (var child in event.snapshot.children) {
          try {
            var data =
                UserRoleModel.fromJson(jsonDecode(jsonEncode(child.value)));
            data.userKey = child.key;
            allUser.add(data);
          } catch (e) {
            print('Error parsing user role data: $e');
          }
        }
      }
    } catch (e) {
      print('Error in getAllUserRole: $e');
      // في حالة عدم وجود صلاحيات، إرجاع قائمة فارغة
      if (e.toString().contains('Permission denied')) {
        print('لا توجد صلاحيات للوصول إلى User Role للمستخدم: $constUserId');
      }
    }
    return allUser;
  }

  Future<List<UserRoleModel>> getAllUserRoleFromAdmin() async {
    List<UserRoleModel> allUser = [];
    try {
      DatabaseReference adminRef = FirebaseDatabase.instance
          .ref()
          .child('Admin Panel')
          .child('User Role');

      DatabaseEvent event = await adminRef.orderByKey().once();
      if (event.snapshot.value != null) {
        for (var child in event.snapshot.children) {
          try {
            var data =
                UserRoleModel.fromJson(jsonDecode(jsonEncode(child.value)));
            data.userKey = child.key;
            allUser.add(data);
          } catch (e) {
            print('Error parsing user role data: $e');
          }
        }
      }
    } catch (e) {
      print('Error in getAllUserRoleFromAdmin: $e');
      // في حالة عدم وجود صلاحيات أو خطأ في الاتصال، إرجاع قائمة فارغة
      if (e.toString().contains('Permission denied')) {
        print('لا توجد صلاحيات للوصول إلى Admin Panel User Role');
      }
    }
    return allUser;
  }
}
