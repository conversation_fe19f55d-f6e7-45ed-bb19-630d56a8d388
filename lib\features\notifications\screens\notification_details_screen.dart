// بسم الله الرحمن الرحيم
// شاشة تفاصيل الإشعار - تعرض تفاصيل إشعار معين

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/auth/auth_service.dart';
import '../../core/theme/app_theme.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';

/// شاشة تفاصيل الإشعار
class NotificationDetailsScreen extends ConsumerStatefulWidget {
  const NotificationDetailsScreen({
    super.key,
    required this.notification,
  });

  final NotificationModel notification;

  @override
  ConsumerState<NotificationDetailsScreen> createState() =>
      _NotificationDetailsScreenState();
}

class _NotificationDetailsScreenState
    extends ConsumerState<NotificationDetailsScreen> {
  late NotificationModel _notification;

  @override
  void initState() {
    super.initState();
    _notification = widget.notification;

    // تحديث حالة الإشعار إلى "تم التفاعل معه" إذا كان مقروءًا
    if (_notification.status == NotificationStatus.read) {
      _updateNotificationStatus(NotificationStatus.acted);
    }
  }

  // تحديث حالة الإشعار
  Future<void> _updateNotificationStatus(NotificationStatus status) async {
    try {
      final currentUser = ref.read(authServiceProvider).currentUser;
      if (currentUser != null) {
        final success = await ref
            .read(notificationServiceProvider)
            .updateNotificationStatus(
              currentUser.uid,
              _notification.id,
              status,
            );

        if (success) {
          setState(() {
            _notification = _notification.copyWith(status: status);
          });

          // تحديث مزودات الإشعارات
          // تجاهل تحذيرات عدم استخدام القيمة المرجعة
          // ignore: unused_result
          ref.refresh(currentUserNotificationsProvider);
          // ignore: unused_result
          ref.refresh(unreadNotificationsCountProvider);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  // الحصول على أيقونة نوع الإشعار
  IconData _getNotificationTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return Icons.money_off;
      case NotificationType.message:
        return Icons.message;
      case NotificationType.system:
        return Icons.system_update;
      case NotificationType.general:
        return Icons.notifications;
    }
  }

  // الحصول على لون نوع الإشعار
  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return Colors.red;
      case NotificationType.message:
        return Colors.blue;
      case NotificationType.system:
        return Colors.purple;
      case NotificationType.general:
        return Colors.orange;
    }
  }

  // الحصول على اسم نوع الإشعار
  String _getNotificationTypeName(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return 'مديونية';
      case NotificationType.message:
        return 'رسالة';
      case NotificationType.system:
        return 'نظام';
      case NotificationType.general:
        return 'عام';
    }
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  // عرض محتوى الإشعار حسب نوعه
  Widget _buildNotificationContent() {
    switch (_notification.type) {
      case NotificationType.debt:
        return _buildDebtNotificationContent();
      case NotificationType.message:
        return _buildMessageNotificationContent();
      case NotificationType.system:
        return _buildSystemNotificationContent();
      case NotificationType.general:
        return _buildGeneralNotificationContent();
    }
  }

  // عرض محتوى إشعار المديونية
  Widget _buildDebtNotificationContent() {
    final data = _notification.data;
    if (data != null) {
      final customerName = data['customerName'] as String?;
      final amount = data['amount'] as double?;
      final dueDate = data['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['dueDate'] as int)
          : null;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (customerName != null) ...[
            const Text(
              'العميل:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(customerName),
            const SizedBox(height: 8),
          ],
          if (amount != null) ...[
            const Text(
              'المبلغ:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text('$amount جنيه'),
            const SizedBox(height: 8),
          ],
          if (dueDate != null) ...[
            const Text(
              'تاريخ الاستحقاق:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(_formatDate(dueDate)),
            const SizedBox(height: 16),
          ],
          ElevatedButton.icon(
            onPressed: () {
              // التنقل إلى شاشة المديونية
              final debtId = _notification.data?['debtId'] as String?;
              if (debtId != null) {
                // يمكن استخدام Navigator للتنقل إلى شاشة المديونية
                // Navigator.pushNamed(context, '/debts/details', arguments: debtId);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text('جاري التنقل إلى المديونية رقم: $debtId')),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('لا يمكن العثور على معرف المديونية')),
                );
              }
            },
            icon: const Icon(Icons.visibility),
            label: const Text('عرض تفاصيل المديونية'),
          ),
        ],
      );
    }

    return const Text('لا توجد بيانات إضافية');
  }

  // عرض محتوى إشعار الرسالة
  Widget _buildMessageNotificationContent() {
    final data = _notification.data;
    if (data != null) {
      final senderName = data['senderName'] as String?;
      final messagePreview = data['messagePreview'] as String?;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (senderName != null) ...[
            const Text(
              'المرسل:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(senderName),
            const SizedBox(height: 8),
          ],
          if (messagePreview != null) ...[
            const Text(
              'نص الرسالة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(messagePreview),
            const SizedBox(height: 16),
          ],
          ElevatedButton.icon(
            onPressed: () {
              // التنقل إلى شاشة المحادثة
              final chatId = _notification.data?['chatId'] as String?;

              if (chatId != null) {
                // يمكن استخدام Navigator للتنقل إلى شاشة المحادثة
                // Navigator.pushNamed(context, '/chats/details', arguments: chatId);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text('جاري التنقل إلى المحادثة رقم: $chatId')),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('لا يمكن العثور على معرف المحادثة')),
                );
              }
            },
            icon: const Icon(Icons.chat),
            label: const Text('الرد على الرسالة'),
          ),
        ],
      );
    }

    return const Text('لا توجد بيانات إضافية');
  }

  // عرض محتوى إشعار النظام
  Widget _buildSystemNotificationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'رسالة النظام:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        Text(_notification.body),
      ],
    );
  }

  // عرض محتوى الإشعار العام
  Widget _buildGeneralNotificationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الرسالة:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        Text(_notification.body),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.mainColor,
      appBar: AppBar(
        backgroundColor: AppColors.mainColor,
        title:
            const Text('تفاصيل الإشعار', style: TextStyle(color: Colors.white)),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0.0,
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 15),
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الإشعار
                Row(
                  children: [
                    CircleAvatar(
                      radius: 24,
                      backgroundColor:
                          _getNotificationTypeColor(_notification.type),
                      child: Icon(
                        _getNotificationTypeIcon(_notification.type),
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _notification.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _formatDate(_notification.createdAt),
                            style: const TextStyle(
                              color: AppColors.greyTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),

                // معلومات الإشعار
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'النوع:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color:
                                  _getNotificationTypeColor(_notification.type)
                                      .withAlpha(51), // 0.2 * 255 = 51
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _getNotificationTypeName(_notification.type),
                              style: TextStyle(
                                color: _getNotificationTypeColor(
                                    _notification.type),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'المحتوى:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      _buildNotificationContent(),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // صورة الإشعار (إذا وجدت)
                if (_notification.imageUrl != null) ...[
                  const Text(
                    'الصورة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      _notification.imageUrl!,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: 200,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(
                              Icons.error,
                              color: Colors.red,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // زر الإجراء (إذا وجد)
                if (_notification.actionUrl != null)
                  Center(
                    child: ElevatedButton(
                      onPressed: () {
                        // تنفيذ الإجراء المطلوب
                        final actionUrl = _notification.actionUrl;
                        if (actionUrl != null) {
                          // يمكن استخدام url_launcher لفتح الرابط
                          // launchUrl(Uri.parse(actionUrl));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text('جاري فتح الرابط: $actionUrl')),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content:
                                    Text('لا يمكن العثور على رابط الإجراء')),
                          );
                        }
                      },
                      child: const Text('تنفيذ الإجراء'),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
