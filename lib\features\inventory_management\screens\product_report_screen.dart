// بسم الله الرحمن الرحيم
// شاشة تقرير المنتج - تعرض تقرير شامل للمنتج مع الرسوم البيانية

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/product_model.dart';
import '../models/product_transaction_model.dart';
import '../services/product_transaction_service.dart';

/// شاشة تقرير المنتج
class ProductReportScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة تقرير المنتج
  const ProductReportScreen({
    super.key,
    required this.product,
  });

  /// المنتج
  final ProductModel product;

  @override
  ConsumerState<ProductReportScreen> createState() =>
      _ProductReportScreenState();
}

class _ProductReportScreenState extends ConsumerState<ProductReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  ProductStatistics? _statistics;
  List<ProductTransactionModel> _transactions = [];
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الإحصائيات
      final statistics = await ref
          .read(productTransactionServiceProvider)
          .getProductStatistics(widget.product.barcode);
      _statistics = statistics;

      // تحميل المعاملات في الفترة المحددة
      final transactions = await ref
          .read(productTransactionServiceProvider)
          .getProductTransactionsByDateRange(
            widget.product.barcode,
            _startDate,
            _endDate,
          );
      _transactions = transactions;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // اختيار فترة زمنية
  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقرير المنتج',
              style: const TextStyle(fontSize: 18),
            ),
            Text(
              widget.product.name,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.greyTextColor,
              ),
            ),
          ],
        ),
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'اختيار الفترة الزمنية',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الملخص'),
            Tab(text: 'التحليل'),
            Tab(text: 'التفاصيل'),
          ],
        ),
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل التقرير...',
            )
          : Column(
              children: [
                // شريط الفترة الزمنية
                Container(
                  padding: const EdgeInsets.all(16),
                  color: AppColors.lightGreyColor,
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'من ${DateFormat('yyyy-MM-dd').format(_startDate)} إلى ${DateFormat('yyyy-MM-dd').format(_endDate)}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_transactions.length} معاملة',
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.greyTextColor,
                        ),
                      ),
                    ],
                  ),
                ),

                // محتوى التبويبات
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildSummaryTab(),
                      _buildAnalysisTab(),
                      _buildDetailsTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  // بناء تبويب الملخص
  Widget _buildSummaryTab() {
    final currencyFormat = NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الإحصائيات الرئيسية
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المبيعات',
                  currencyFormat.format(_statistics?.totalSalesAmount ?? 0),
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'إجمالي المشتريات',
                  currencyFormat.format(_statistics?.totalPurchaseAmount ?? 0),
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'صافي الربح',
                  currencyFormat.format(_statistics?.netProfit ?? 0),
                  Icons.account_balance_wallet,
                  _statistics?.netProfit != null && _statistics!.netProfit >= 0
                      ? Colors.green
                      : Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'هامش الربح',
                  '${(_statistics?.profitMargin ?? 0).toStringAsFixed(1)}%',
                  Icons.percent,
                  Colors.purple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // تفاصيل الكميات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل الكميات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('الكمية المباعة', '${_statistics?.totalSalesQuantity ?? 0} ${widget.product.unit}'),
                  _buildInfoRow('الكمية المشتراة', '${_statistics?.totalPurchaseQuantity ?? 0} ${widget.product.unit}'),
                  _buildInfoRow('الكمية الحالية', '${widget.product.quantity} ${widget.product.unit}'),
                  _buildInfoRow('عدد فواتير البيع', '${_statistics?.salesInvoiceCount ?? 0}'),
                  _buildInfoRow('عدد فواتير الشراء', '${_statistics?.purchaseInvoiceCount ?? 0}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // تفاصيل الأسعار
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل الأسعار',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('متوسط سعر البيع', currencyFormat.format(_statistics?.averageSalePrice ?? 0)),
                  _buildInfoRow('متوسط سعر الشراء', currencyFormat.format(_statistics?.averagePurchasePrice ?? 0)),
                  _buildInfoRow('السعر الحالي', currencyFormat.format(widget.product.price)),
                  _buildInfoRow('تكلفة الشراء الحالية', currencyFormat.format(widget.product.cost)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء تبويب التحليل
  Widget _buildAnalysisTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64,
            color: AppColors.greyTextColor,
          ),
          SizedBox(height: 16),
          Text(
            'الرسوم البيانية والتحليلات',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.greyTextColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم إضافة الرسوم البيانية قريباً',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.greyTextColor,
            ),
          ),
        ],
      ),
    );
  }

  // بناء تبويب التفاصيل
  Widget _buildDetailsTab() {
    if (_transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: AppColors.greyTextColor,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد معاملات في هذه الفترة',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.greyTextColor,
              ),
            ),
          ],
        ),
      );
    }

    final currencyFormat = NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getTransactionColor(transaction.transactionType),
              child: Icon(
                _getTransactionIcon(transaction.transactionType),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text('فاتورة ${transaction.invoiceNumber}'),
            subtitle: Text(
              '${transaction.customerName} • ${DateFormat('yyyy-MM-dd').format(transaction.date)}',
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  currencyFormat.format(transaction.totalAmount),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getTransactionColor(transaction.transactionType),
                  ),
                ),
                Text(
                  '${transaction.quantity} ${widget.product.unit}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.greyTextColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.greyTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.greyTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على لون المعاملة
  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Colors.green;
      case TransactionType.purchase:
        return Colors.blue;
      case TransactionType.saleReturn:
        return Colors.orange;
      case TransactionType.purchaseReturn:
        return Colors.purple;
    }
  }

  // الحصول على أيقونة المعاملة
  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Icons.point_of_sale;
      case TransactionType.purchase:
        return Icons.shopping_cart;
      case TransactionType.saleReturn:
        return Icons.assignment_return;
      case TransactionType.purchaseReturn:
        return Icons.keyboard_return;
    }
  }
}
