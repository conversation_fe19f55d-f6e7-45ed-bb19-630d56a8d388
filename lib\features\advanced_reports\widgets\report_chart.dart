// بسم الله الرحمن الرحيم
// مخطط التقرير - مكون يعرض بيانات التقرير في شكل مخطط بياني

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

/// نوع المخطط
enum ChartType {
  /// مخطط خطي
  line,

  /// مخطط شريطي
  bar,

  /// مخطط دائري
  pie,
}

/// مخطط التقرير
class ReportChart extends StatelessWidget {
  /// ينشئ مخطط التقرير
  const ReportChart({
    super.key,
    required this.data,
    this.type = ChartType.line,
    this.title,
    this.xAxisTitle,
    this.yAxisTitle,
    this.height = 300,
    this.colors,
    this.animate = true,
  });

  /// بيانات المخطط
  final List<Map<String, dynamic>> data;

  /// نوع المخطط
  final ChartType type;

  /// عنوان المخطط (اختياري)
  final String? title;

  /// عنوان المحور السيني (اختياري)
  final String? xAxisTitle;

  /// عنوان المحور الصادي (اختياري)
  final String? yAxisTitle;

  /// ارتفاع المخطط
  final double height;

  /// ألوان المخطط (اختياري)
  final List<Color>? colors;

  /// هل يتم تحريك المخطط؟
  final bool animate;

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return SizedBox(
        height: height,
        child: const Center(
          child: Text('لا توجد بيانات للعرض'),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
        ],
        SizedBox(
          height: height,
          child: _buildChart(),
        ),
      ],
    );
  }

  /// بناء المخطط المناسب
  Widget _buildChart() {
    switch (type) {
      case ChartType.line:
        return _buildLineChart();
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.pie:
        return _buildPieChart();
    }
  }

  /// بناء مخطط خطي
  Widget _buildLineChart() {
    return LineChart(
      LineChartData(
        gridData: const FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 1,
          verticalInterval: 1,
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < data.length) {
                  final item = data[value.toInt()];
                  if (item.containsKey('date')) {
                    return Text(
                      DateFormat('MM/dd').format(DateTime.parse(item['date'])),
                      style: const TextStyle(fontSize: 10),
                    );
                  } else if (item.containsKey('label')) {
                    return Text(
                      item['label'].toString(),
                      style: const TextStyle(fontSize: 10),
                    );
                  }
                }
                return const Text('');
              },
            ),
            axisNameWidget: xAxisTitle != null
                ? Text(
                    xAxisTitle!,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 10),
                );
              },
              reservedSize: 42,
            ),
            axisNameWidget: yAxisTitle != null
                ? Text(
                    yAxisTitle!,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: const Color(0xff37434d)),
        ),
        minX: 0,
        maxX: data.length.toDouble() - 1,
        minY: 0,
        maxY: _getMaxValue() * 1.2,
        lineBarsData: [
          LineChartBarData(
            spots: _getLineSpots(),
            isCurved: true,
            color: colors?.isNotEmpty == true ? colors!.first : Colors.blue,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: (colors?.isNotEmpty == true ? colors!.first : Colors.blue)
                  .withAlpha(76),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart() {
    return BarChart(
      BarChartData(
        gridData: const FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 1,
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < data.length) {
                  final item = data[value.toInt()];
                  if (item.containsKey('category')) {
                    return Text(
                      item['category'].toString(),
                      style: const TextStyle(fontSize: 10),
                    );
                  } else if (item.containsKey('label')) {
                    return Text(
                      item['label'].toString(),
                      style: const TextStyle(fontSize: 10),
                    );
                  }
                }
                return const Text('');
              },
            ),
            axisNameWidget: xAxisTitle != null
                ? Text(
                    xAxisTitle!,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: _getMaxValue() / 5,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 10),
                );
              },
              reservedSize: 42,
            ),
            axisNameWidget: yAxisTitle != null
                ? Text(
                    yAxisTitle!,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: const Color(0xff37434d)),
        ),
        barGroups: _getBarGroups(),
        maxY: _getMaxValue() * 1.2,
      ),
    );
  }

  /// بناء مخطط دائري
  Widget _buildPieChart() {
    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        sections: _getPieSections(),
      ),
    );
  }

  /// الحصول على نقاط المخطط الخطي
  List<FlSpot> _getLineSpots() {
    final spots = <FlSpot>[];
    for (var i = 0; i < data.length; i++) {
      final item = data[i];
      double value = 0;
      if (item.containsKey('value')) {
        value = (item['value'] as num).toDouble();
      } else if (item.containsKey('sales')) {
        value = (item['sales'] as num).toDouble();
      } else if (item.containsKey('amount')) {
        value = (item['amount'] as num).toDouble();
      }
      spots.add(FlSpot(i.toDouble(), value));
    }
    return spots;
  }

  /// الحصول على مجموعات المخطط الشريطي
  List<BarChartGroupData> _getBarGroups() {
    final groups = <BarChartGroupData>[];
    for (var i = 0; i < data.length; i++) {
      final item = data[i];
      double value = 0;
      if (item.containsKey('value')) {
        value = (item['value'] as num).toDouble();
      } else if (item.containsKey('sales')) {
        value = (item['sales'] as num).toDouble();
      } else if (item.containsKey('amount')) {
        value = (item['amount'] as num).toDouble();
      }
      groups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: value,
              color: _getColor(i),
              width: 20,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
          ],
        ),
      );
    }
    return groups;
  }

  /// الحصول على أقسام المخطط الدائري
  List<PieChartSectionData> _getPieSections() {
    final sections = <PieChartSectionData>[];
    for (var i = 0; i < data.length; i++) {
      final item = data[i];
      double value = 0;
      if (item.containsKey('value')) {
        value = (item['value'] as num).toDouble();
      } else if (item.containsKey('sales')) {
        value = (item['sales'] as num).toDouble();
      } else if (item.containsKey('amount')) {
        value = (item['amount'] as num).toDouble();
      }
      String title = '';
      if (item.containsKey('category')) {
        title = item['category'].toString();
      } else if (item.containsKey('label')) {
        title = item['label'].toString();
      } else if (item.containsKey('name')) {
        title = item['name'].toString();
      }
      sections.add(
        PieChartSectionData(
          color: _getColor(i),
          value: value,
          title: title,
          radius: 50,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }
    return sections;
  }

  /// الحصول على القيمة القصوى
  double _getMaxValue() {
    double maxValue = 0;
    for (var item in data) {
      double value = 0;
      if (item.containsKey('value')) {
        value = (item['value'] as num).toDouble();
      } else if (item.containsKey('sales')) {
        value = (item['sales'] as num).toDouble();
      } else if (item.containsKey('amount')) {
        value = (item['amount'] as num).toDouble();
      }
      if (value > maxValue) {
        maxValue = value;
      }
    }
    return maxValue;
  }

  /// الحصول على لون العنصر
  Color _getColor(int index) {
    final defaultColors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.amber,
      Colors.indigo,
      Colors.cyan,
    ];
    if (colors != null && colors!.isNotEmpty) {
      return colors![index % colors!.length];
    }
    return defaultColors[index % defaultColors.length];
  }
}
