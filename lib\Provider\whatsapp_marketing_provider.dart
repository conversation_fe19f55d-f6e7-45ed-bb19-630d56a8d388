import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/whatsapp_marketing_model.dart';
import 'package:mobile_pos/repository/whatsapp_marketing_repo.dart';

// Provider for WhatsApp marketing repository
final whatsappMarketingRepositoryProvider = Provider<WhatsappMarketingRepository>((ref) {
  return WhatsappMarketingRepository();
});

// Provider for campaign list
final campaignListProvider = StreamProvider<List<WhatsappCampaignModel>>((ref) {
  final whatsappRepo = ref.watch(whatsappMarketingRepositoryProvider);
  return whatsappRepo.getCampaignList();
});

// Provider for group list
final groupListProvider = StreamProvider<List<WhatsappGroupModel>>((ref) {
  final whatsappRepo = ref.watch(whatsappMarketingRepositoryProvider);
  return whatsappRepo.getGroupList();
});

// Provider for contact list
final contactListProvider = StreamProvider<List<WhatsappContactModel>>((ref) {
  final whatsappRepo = ref.watch(whatsappMarketingRepositoryProvider);
  return whatsappRepo.getContactList();
});

// Provider for selected campaign
final selectedCampaignProvider = StateProvider<WhatsappCampaignModel?>((ref) => null);

// Provider for selected group
final selectedGroupProvider = StateProvider<WhatsappGroupModel?>((ref) => null);

// Provider for selected contact
final selectedContactProvider = StateProvider<WhatsappContactModel?>((ref) => null);

// Provider for campaign filter
final campaignFilterProvider = StateProvider<String>((ref) => 'all'); // all, draft, scheduled, sent, failed

// Provider for filtered campaign list
final filteredCampaignListProvider = Provider<List<WhatsappCampaignModel>>((ref) {
  final campaigns = ref.watch(campaignListProvider);
  final filter = ref.watch(campaignFilterProvider);

  return campaigns.when(
    data: (data) {
      if (filter == 'all') {
        return data;
      } else {
        return data.where((campaign) => campaign.status == filter).toList();
      }
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Provider for campaign notifier
final campaignNotifierProvider = StateNotifierProvider<CampaignNotifier, AsyncValue<void>>((ref) {
  final whatsappRepo = ref.watch(whatsappMarketingRepositoryProvider);
  return CampaignNotifier(whatsappRepo);
});

// Notifier for campaign operations
class CampaignNotifier extends StateNotifier<AsyncValue<void>> {
  final WhatsappMarketingRepository _whatsappRepo;

  CampaignNotifier(this._whatsappRepo) : super(const AsyncValue.data(null));

  // Add a new campaign
  Future<void> addCampaign(WhatsappCampaignModel campaign) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.addCampaign(campaign);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update an existing campaign
  Future<void> updateCampaign(WhatsappCampaignModel campaign) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.updateCampaign(campaign);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Delete a campaign
  Future<void> deleteCampaign(String campaignId) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.deleteCampaign(campaignId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Change campaign status
  Future<void> changeCampaignStatus(String campaignId, String status) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.changeCampaignStatus(campaignId, status);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Send WhatsApp messages
  Future<Map<String, dynamic>> sendWhatsappMessage(WhatsappCampaignModel campaign) async {
    state = const AsyncValue.loading();
    try {
      final result = await _whatsappRepo.sendWhatsappMessage(campaign);
      state = const AsyncValue.data(null);
      return result;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}

// Provider for group notifier
final groupNotifierProvider = StateNotifierProvider<GroupNotifier, AsyncValue<void>>((ref) {
  final whatsappRepo = ref.watch(whatsappMarketingRepositoryProvider);
  return GroupNotifier(whatsappRepo);
});

// Notifier for group operations
class GroupNotifier extends StateNotifier<AsyncValue<void>> {
  final WhatsappMarketingRepository _whatsappRepo;

  GroupNotifier(this._whatsappRepo) : super(const AsyncValue.data(null));

  // Add a new group
  Future<void> addGroup(WhatsappGroupModel group) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.addGroup(group);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update an existing group
  Future<void> updateGroup(WhatsappGroupModel group) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.updateGroup(group);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Delete a group
  Future<void> deleteGroup(String groupId) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.deleteGroup(groupId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

// Provider for contact notifier
final contactNotifierProvider = StateNotifierProvider<ContactNotifier, AsyncValue<void>>((ref) {
  final whatsappRepo = ref.watch(whatsappMarketingRepositoryProvider);
  return ContactNotifier(whatsappRepo);
});

// Notifier for contact operations
class ContactNotifier extends StateNotifier<AsyncValue<void>> {
  final WhatsappMarketingRepository _whatsappRepo;

  ContactNotifier(this._whatsappRepo) : super(const AsyncValue.data(null));

  // Add a new contact
  Future<void> addContact(WhatsappContactModel contact) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.addContact(contact);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update an existing contact
  Future<void> updateContact(WhatsappContactModel contact) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.updateContact(contact);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Delete a contact
  Future<void> deleteContact(String contactId) async {
    state = const AsyncValue.loading();
    try {
      await _whatsappRepo.deleteContact(contactId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
