// بسم الله الرحمن الرحيم
// خدمة ملف المستخدم - توفر واجهة للتعامل مع بيانات ملف المستخدم

import 'dart:io';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/auth/auth_service.dart';
import '../models/user_profile_model.dart';

/// خدمة ملف المستخدم
class UserProfileService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة ملف المستخدم
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  // مثيل Firebase Database
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // مثيل Firebase Storage
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // خدمة المصادقة
  final AuthService _authService = AuthService();

  /// تهيئة خدمة ملف المستخدم
  Future<void> initialize() async {
    try {
      debugPrint('تهيئة خدمة ملف المستخدم...');
      // التحقق من وجود المستخدم الحالي
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        // التحقق من وجود ملف للمستخدم
        final profile = await getUserProfile(currentUser.uid);
        if (profile == null) {
          // إنشاء ملف جديد للمستخدم إذا لم يكن موجودًا
          await createUserProfile(currentUser);
          debugPrint('تم إنشاء ملف جديد للمستخدم: ${currentUser.uid}');
        } else {
          // تحديث آخر نشاط للمستخدم
          await updateLastActive(currentUser.uid);
        }
      }
      debugPrint('تم تهيئة خدمة ملف المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة ملف المستخدم: $e');
    }
  }

  // الحصول على مرجع قاعدة البيانات لملف المستخدم
  DatabaseReference _getUserProfileRef(String uid) {
    return _database.ref('features/user_profiles/$uid');
  }

  // الحصول على مرجع التخزين لصورة ملف المستخدم
  Reference _getUserProfileImageRef(String uid) {
    return _storage.ref('features/user_profiles/$uid/profile_image');
  }

  /// الحصول على ملف المستخدم
  Future<UserProfileModel?> getUserProfile(String uid) async {
    try {
      final snapshot = await _getUserProfileRef(uid).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        return UserProfileModel.fromMap(Map<String, dynamic>.from(data));
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على ملف المستخدم: $e');
      return null;
    }
  }

  /// الحصول على ملف المستخدم الحالي
  Future<UserProfileModel?> getCurrentUserProfile() async {
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      return getUserProfile(currentUser.uid);
    }
    return null;
  }

  /// إنشاء أو تحديث ملف المستخدم
  Future<bool> updateUserProfile(UserProfileModel profile) async {
    try {
      await _getUserProfileRef(profile.uid).set(profile.toMap());
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث ملف المستخدم: $e');
      return false;
    }
  }

  /// تحميل صورة ملف المستخدم
  Future<String?> uploadProfileImage(String uid, File imageFile) async {
    try {
      final ref = _getUserProfileImageRef(uid);
      await ref.putFile(imageFile);
      final url = await ref.getDownloadURL();
      return url;
    } catch (e) {
      debugPrint('خطأ في تحميل صورة ملف المستخدم: $e');
      return null;
    }
  }

  /// حذف صورة ملف المستخدم
  Future<bool> deleteProfileImage(String uid) async {
    try {
      await _getUserProfileImageRef(uid).delete();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف صورة ملف المستخدم: $e');
      return false;
    }
  }

  /// تحديث آخر نشاط للمستخدم
  Future<bool> updateLastActive(String uid) async {
    try {
      await _getUserProfileRef(uid).update({
        'lastActive': DateTime.now().millisecondsSinceEpoch,
      });
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث آخر نشاط للمستخدم: $e');
      return false;
    }
  }

  /// إنشاء ملف مستخدم جديد
  Future<UserProfileModel?> createUserProfile(UserModel user) async {
    try {
      final profile = UserProfileModel(
        uid: user.uid,
        displayName: user.displayName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        photoURL: user.photoURL,
        createdAt: DateTime.now(),
        lastActive: DateTime.now(),
      );

      final success = await updateUserProfile(profile);
      if (success) {
        return profile;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في إنشاء ملف مستخدم جديد: $e');
      return null;
    }
  }
}

/// مزود خدمة ملف المستخدم
final userProfileServiceProvider = Provider<UserProfileService>((ref) {
  return UserProfileService();
});

/// مزود ملف المستخدم الحالي
final currentUserProfileProvider =
    FutureProvider<UserProfileModel?>((ref) async {
  return await UserProfileService().getCurrentUserProfile();
});
