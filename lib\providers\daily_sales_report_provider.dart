import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/daily_sales_report_model.dart';
import 'package:mobile_pos/services/daily_sales_report_service.dart';

/// مزود خدمة تقرير المبيعات اليومي
final dailySalesReportServiceProvider =
    Provider<DailySalesReportService>((ref) {
  return DailySalesReportService();
});

/// مزود التاريخ المحدد للتقرير
final selectedReportDateProvider = StateProvider<DateTime>((ref) {
  return DateTime.now();
});

/// مزود تقرير المبيعات اليومي
final dailySalesReportProvider =
    FutureProvider<DailySalesReportModel>((ref) async {
  final service = ref.read(dailySalesReportServiceProvider);
  final selectedDate = ref.watch(selectedReportDateProvider);

  return await service.getDailySalesReport(selectedDate);
});

/// مزود الإحصائيات السريعة
final quickStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.read(dailySalesReportServiceProvider);
  final selectedDate = ref.watch(selectedReportDateProvider);

  return await service.getQuickStats(selectedDate);
});

/// مزود للتحقق من وجود مبيعات
final hasSalesProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(dailySalesReportServiceProvider);
  final selectedDate = ref.watch(selectedReportDateProvider);

  return await service.hasSalesOnDate(selectedDate);
});

/// مزود حالة التحميل
final reportLoadingProvider = StateProvider<bool>((ref) => false);

/// مزود رسائل الخطأ
final reportErrorProvider = StateProvider<String?>((ref) => null);

/// مزود إدارة التقرير
class DailySalesReportNotifier
    extends StateNotifier<AsyncValue<DailySalesReportModel?>> {
  DailySalesReportNotifier(this._service) : super(const AsyncValue.data(null));

  final DailySalesReportService _service;

  /// تحديث التاريخ وإعادة تحميل التقرير
  Future<void> updateDate(DateTime newDate) async {
    state = const AsyncValue.loading();

    try {
      final report = await _service.getDailySalesReport(newDate);
      state = AsyncValue.data(report);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// إعادة تحميل التقرير
  Future<void> refresh() async {
    if (state.value != null) {
      await updateDate(state.value!.reportDate);
    }
  }

  /// تحميل التقرير لتاريخ محدد
  Future<void> loadReport(DateTime date) async {
    await updateDate(date);
  }
}

/// مزود إدارة التقرير
final dailySalesReportNotifierProvider = StateNotifierProvider<
    DailySalesReportNotifier, AsyncValue<DailySalesReportModel?>>((ref) {
  final service = ref.read(dailySalesReportServiceProvider);
  return DailySalesReportNotifier(service);
});

/// مزود فلترة التقارير حسب النوع
enum ReportFilterType { all, cash, credit }

final reportFilterProvider =
    StateProvider<ReportFilterType>((ref) => ReportFilterType.all);

/// مزود التقرير المفلتر
final filteredReportProvider = Provider<DailySalesReportModel?>((ref) {
  final report = ref.watch(dailySalesReportNotifierProvider).value;
  final filter = ref.watch(reportFilterProvider);

  if (report == null) return null;

  switch (filter) {
    case ReportFilterType.cash:
      // فلترة المبيعات النقدية فقط
      final cashTransactions = report.salesTransactions
          .where((t) => (t.dueAmount ?? 0) == 0)
          .toList();
      return DailySalesReportModel.fromTransactions(
          report.reportDate, cashTransactions);

    case ReportFilterType.credit:
      // فلترة المبيعات الآجلة فقط
      final creditTransactions = report.salesTransactions
          .where((t) => (t.dueAmount ?? 0) > 0)
          .toList();
      return DailySalesReportModel.fromTransactions(
          report.reportDate, creditTransactions);

    case ReportFilterType.all:
      return report;
  }
});

/// مزود البحث في التقرير
final reportSearchProvider = StateProvider<String>((ref) => '');

/// مزود التقرير مع البحث
final searchedReportProvider = Provider<DailySalesReportModel?>((ref) {
  final report = ref.watch(filteredReportProvider);
  final searchQuery = ref.watch(reportSearchProvider);

  if (report == null || searchQuery.isEmpty) return report;

  // البحث في أسماء العملاء وأرقام الفواتير
  final filteredTransactions = report.salesTransactions
      .where((t) =>
          t.customerName.toLowerCase().contains(searchQuery.toLowerCase()) ||
          t.invoiceNumber.toLowerCase().contains(searchQuery.toLowerCase()))
      .toList();

  return DailySalesReportModel.fromTransactions(
      report.reportDate, filteredTransactions);
});
