class PaginatedData<T> {
  final List<T> items;
  final bool hasMore;
  final int page;
  final int totalItems;
  
  PaginatedData({
    required this.items,
    required this.hasMore,
    required this.page,
    this.totalItems = 0,
  });
  
  PaginatedData<T> copyWith({
    List<T>? items,
    bool? hasMore,
    int? page,
    int? totalItems,
  }) {
    return PaginatedData<T>(
      items: items ?? this.items,
      hasMore: hasMore ?? this.hasMore,
      page: page ?? this.page,
      totalItems: totalItems ?? this.totalItems,
    );
  }
}
