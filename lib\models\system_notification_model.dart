/// نموذج الإشعارات بين المستخدمين
class SystemNotificationModel {
  /// معرف الإشعار
  final String id;

  /// معرف المستخدم المرسل
  final String senderId;

  /// اسم المستخدم المرسل
  final String senderName;

  /// نوع الإشعار
  final String type;

  /// عنوان الإشعار
  final String title;

  /// وصف الإشعار
  final String message;

  /// البيانات المرتبطة بالإشعار
  final Map<String, dynamic> data;

  /// تاريخ الإشعار
  final DateTime timestamp;

  /// حالة قراءة الإشعار
  final bool isRead;

  /// إنشاء نموذج الإشعار
  SystemNotificationModel({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.type,
    required this.title,
    required this.message,
    required this.data,
    required this.timestamp,
    this.isRead = false,
  });

  /// إنشاء نموذج من JSON
  factory SystemNotificationModel.fromJson(Map<dynamic, dynamic> json) {
    try {
      // التعامل مع القيم الفارغة بشكل آمن
      final id = json['id']?.toString() ?? '';
      final senderId = json['senderId']?.toString() ?? '';
      final senderName = json['senderName']?.toString() ?? 'مستخدم غير معروف';
      final type = json['type']?.toString() ?? 'system';
      final title = json['title']?.toString() ?? 'إشعار جديد';
      final message = json['message']?.toString() ?? '';

      // التعامل مع البيانات بشكل آمن
      Map<String, dynamic> data = {};
      if (json['data'] != null && json['data'] is Map) {
        data = Map<String, dynamic>.from(json['data'] as Map);
      }

      // التعامل مع الطابع الزمني بشكل آمن
      DateTime timestamp;
      try {
        final timestampStr = json['timestamp']?.toString();
        if (timestampStr != null && timestampStr.isNotEmpty) {
          timestamp = DateTime.parse(timestampStr);
        } else {
          timestamp = DateTime.now();
        }
      } catch (e) {
        timestamp = DateTime.now();
      }

      // التعامل مع حالة القراءة بشكل آمن
      final isRead = json['isRead'] == true;

      return SystemNotificationModel(
        id: id,
        senderId: senderId,
        senderName: senderName,
        type: type,
        title: title,
        message: message,
        data: data,
        timestamp: timestamp,
        isRead: isRead,
      );
    } catch (e) {
      // في حالة حدوث أي خطأ، إنشاء إشعار افتراضي
      return SystemNotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        senderId: '',
        senderName: 'مستخدم غير معروف',
        type: 'system',
        title: 'إشعار جديد',
        message: 'تم استلام إشعار جديد',
        data: {},
        timestamp: DateTime.now(),
        isRead: false,
      );
    }
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'type': type,
      'title': title,
      'message': message,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
    };
  }

  /// نسخة مع تعديل حالة القراءة
  SystemNotificationModel copyWith({
    bool? isRead,
  }) {
    return SystemNotificationModel(
      id: id,
      senderId: senderId,
      senderName: senderName,
      type: type,
      title: title,
      message: message,
      data: data,
      timestamp: timestamp,
      isRead: isRead ?? this.isRead,
    );
  }

  @override
  String toString() {
    return 'SystemNotificationModel{id: $id, senderId: $senderId, senderName: $senderName, type: $type, title: $title, message: $message, timestamp: $timestamp, isRead: $isRead}';
  }
}

/// أنواع الإشعارات بين المستخدمين
class SystemNotificationTypes {
  /// إشعارات المبيعات الآجلة
  static const String creditSale = 'credit_sale';

  /// إشعارات المشتريات الآجلة
  static const String creditPurchase = 'credit_purchase';

  /// إشعارات المصروفات
  static const String expense = 'expense';

  /// إشعارات المديونية
  static const String due = 'due';

  /// إشعارات سداد المديونية
  static const String duePayment = 'due_payment';

  /// إشعارات المخزون
  static const String inventory = 'inventory';

  /// إشعارات النظام
  static const String system = 'system';

  /// الحصول على عنوان الإشعار حسب النوع
  static String getTitle(String type) {
    switch (type) {
      case creditSale:
        return 'فاتورة مبيعات آجلة';
      case creditPurchase:
        return 'فاتورة مشتريات آجلة';
      case expense:
        return 'مصروف جديد';
      case due:
        return 'مديونية جديدة';
      case duePayment:
        return 'سداد مديونية';
      case inventory:
        return 'تحديث المخزون';
      case system:
        return 'إشعار النظام';
      default:
        return 'إشعار جديد';
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  static String getIcon(String type) {
    switch (type) {
      case creditSale:
        return 'assets/icons/sales.png';
      case creditPurchase:
        return 'assets/icons/purchase.png';
      case expense:
        return 'assets/icons/expense.png';
      case due:
        return 'assets/icons/due.png';
      case duePayment:
        return 'assets/icons/due.png'; // استخدام نفس أيقونة المديونية
      case inventory:
        return 'assets/icons/inventory.png';
      case system:
        return 'assets/icons/system.png';
      default:
        return 'assets/icons/notification.png';
    }
  }
}
