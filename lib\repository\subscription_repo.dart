import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart'; // إضافة استيراد للوصول إلى debugPrint
import 'package:mobile_pos/model/subscription_model.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/subscription.dart';

import '../currency.dart';
import '../model/subscription_plan_model.dart';

class SubscriptionRepo {
  static Future<SubscriptionModel> getSubscriptionData() async {
    try {
      // الحصول على مرجع قاعدة البيانات
      DatabaseReference ref = FirebaseDatabaseService.getReference(
          '$constUserId/Subscription',
          keepSynced: true);

      // الحصول على البيانات
      final model = await ref.get();

      // التحقق من وجود البيانات
      if (model.exists) {
        // تحويل البيانات إلى JSON
        var data = jsonDecode(jsonEncode(model.value));

        // تحويل البيانات إلى نموذج
        final subscriptionModel = SubscriptionModel.fromJson(data);

        // التحقق من اسم الاشتراك وتصحيحه إذا كان Free
        String correctedSubscriptionName = subscriptionModel.subscriptionName;
        if (correctedSubscriptionName == 'Free') {
          correctedSubscriptionName = 'Admin';
          debugPrint(
              'تم تصحيح اسم الاشتراك من Free إلى Admin في SubscriptionRepo');
        }

        // تعيين اسم الاشتراك المصحح
        Subscription.selectedItem = correctedSubscriptionName;

        return subscriptionModel;
      } else {
        // إذا لم تكن البيانات موجودة، رفع استثناء
        debugPrint('❌ لا توجد بيانات اشتراك في قاعدة البيانات');
        throw Exception('لا توجد بيانات اشتراك للمستخدم');
      }
    } catch (e) {
      // في حالة حدوث خطأ، رفع الاستثناء
      debugPrint('❌ خطأ في الحصول على بيانات الاشتراك: $e');
      rethrow; // إعادة رفع الاستثناء
    }
  }
}

class SubscriptionPlanRepo {
  Future<List<SubscriptionPlanModel>> getAllSubscriptionPlans() async {
    List<SubscriptionPlanModel> planList = [];
    await FirebaseDatabase.instance
        .ref()
        .child('Admin Panel')
        .child('Subscription Plan')
        .orderByKey()
        .get()
        .then((value) {
      for (var element in value.children) {
        planList.add(SubscriptionPlanModel.fromJson(
            jsonDecode(jsonEncode(element.value))));
      }
    });
    return planList;
  }
}
