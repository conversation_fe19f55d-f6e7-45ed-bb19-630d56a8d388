import 'package:flutter/foundation.dart';

/// نموذج سجل المستخدم
class UserLogModel {
  /// معرف السجل
  final String id;

  /// معرف المستخدم
  final String userId;

  /// اسم المستخدم
  final String userName;

  /// نوع النشاط (مثل: بيع، شراء، مديونية، إلخ)
  final String actionType;

  /// وصف النشاط
  final String description;

  /// البيانات المرتبطة بالنشاط (مثل: رقم الفاتورة، معرف العميل، إلخ)
  final Map<String, dynamic> data;

  /// تاريخ النشاط
  final DateTime timestamp;

  /// إنشاء نموذج سجل المستخدم
  UserLogModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.actionType,
    required this.description,
    required this.data,
    required this.timestamp,
  });

  /// إنشاء نموذج من JSON
  factory UserLogModel.fromJson(Map<dynamic, dynamic> json) {
    try {
      // التعامل مع حقل البيانات
      Map<String, dynamic> dataMap = {};
      if (json['data'] != null) {
        if (json['data'] is Map) {
          dataMap = Map<String, dynamic>.from(json['data'] as Map);
        } else {
          // إذا كانت البيانات ليست من نوع Map، نستخدم Map فارغة
          debugPrint('حقل البيانات ليس من نوع Map: ${json['data']}');
        }
      }

      // التعامل مع حقل التاريخ
      DateTime timestamp;
      try {
        if (json['timestamp'] is String) {
          timestamp = DateTime.parse(json['timestamp'] as String);
        } else if (json['timestamp'] is int) {
          timestamp =
              DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int);
        } else {
          // إذا لم يكن التاريخ من نوع String أو int، نستخدم التاريخ الحالي
          timestamp = DateTime.now();
          debugPrint(
              'حقل التاريخ ليس من نوع String أو int: ${json['timestamp']}');
        }
      } catch (e) {
        // في حالة حدوث خطأ في تحليل التاريخ، نستخدم التاريخ الحالي
        timestamp = DateTime.now();
        debugPrint('خطأ في تحليل التاريخ: $e');
      }

      return UserLogModel(
        id: json['id']?.toString() ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: json['userId']?.toString() ?? 'غير معروف',
        userName: json['userName']?.toString() ?? 'غير معروف',
        actionType: json['actionType']?.toString() ?? 'غير معروف',
        description: json['description']?.toString() ?? 'لا يوجد وصف',
        data: dataMap,
        timestamp: timestamp,
      );
    } catch (e) {
      // في حالة حدوث خطأ، نعيد نموذج افتراضي
      debugPrint('خطأ في إنشاء نموذج سجل المستخدم: $e');
      return UserLogModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'غير معروف',
        userName: 'غير معروف',
        actionType: 'غير معروف',
        description: 'حدث خطأ في تحليل السجل',
        data: {},
        timestamp: DateTime.now(),
      );
    }
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'actionType': actionType,
      'description': description,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// أنواع الأنشطة المتاحة
class UserLogActionTypes {
  // أنشطة المبيعات
  static const String salesCreated = 'sales_created';
  static const String salesUpdated = 'sales_updated';
  static const String salesDeleted = 'sales_deleted';
  static const String salesPaymentReceived = 'sales_payment_received';
  static const String salesReturn = 'sales_return';

  // أنشطة المشتريات
  static const String purchaseCreated = 'purchase_created';
  static const String purchaseUpdated = 'purchase_updated';
  static const String purchaseDeleted = 'purchase_deleted';
  static const String purchasePaymentMade = 'purchase_payment_made';
  static const String purchaseReturn = 'purchase_return';

  // أنشطة المديونية
  static const String dueCreated = 'due_created';
  static const String dueUpdated = 'due_updated';
  static const String duePaymentReceived = 'due_payment_received';
  static const String dueReminder = 'due_reminder';

  // أنشطة المصروفات
  static const String expenseCreated = 'expense_created';
  static const String expenseUpdated = 'expense_updated';
  static const String expenseDeleted = 'expense_deleted';

  // أنشطة المخزون
  static const String inventoryAdded = 'inventory_added';
  static const String inventoryUpdated = 'inventory_updated';
  static const String inventoryRemoved = 'inventory_removed';
  static const String inventoryAdjusted = 'inventory_adjusted';

  // أنشطة العملاء
  static const String customerCreated = 'customer_created';
  static const String customerUpdated = 'customer_updated';
  static const String customerDeleted = 'customer_deleted';

  // أنشطة الموردين
  static const String supplierCreated = 'supplier_created';
  static const String supplierUpdated = 'supplier_updated';
  static const String supplierDeleted = 'supplier_deleted';

  // أنشطة المستخدمين
  static const String userLogin = 'user_login';
  static const String userLogout = 'user_logout';
  static const String userCreated = 'user_created';
  static const String userUpdated = 'user_updated';
  static const String userDeleted = 'user_deleted';

  // أنشطة النظام
  static const String systemBackup = 'system_backup';
  static const String systemRestore = 'system_restore';
  static const String systemSettings = 'system_settings';
}
