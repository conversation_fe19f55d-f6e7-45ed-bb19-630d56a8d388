# 📊 ميزة تقرير ميزان المراجعة - Trial Balance Feature

## 📋 نظرة عامة

ميزة تقرير ميزان المراجعة (Trial Balance) هي ميزة محاسبية متقدمة تقوم بإنشاء تقارير ميزان المراجعة تلقائياً من البيانات المتاحة في النظام. تم تصميمها لتكون ميزة مستقلة ومتكاملة يمكن إضافتها أو إزالتها دون التأثير على باقي أجزاء التطبيق.

## ✨ الميزات الرئيسية

### 📊 إنشاء التقارير
- إنشاء تقرير ميزان المراجعة تلقائياً من البيانات المتاحة
- دعم فترات زمنية مختلفة (يومي، أسبوعي، شهري، ربع سنوي، سنوي)
- فترات زمنية مخصصة حسب الحاجة
- التحقق التلقائي من توازن التقرير

### 🏦 الحسابات المدعومة
- **الأصول (Assets)**:
  - النقدية في الخزينة
  - حسابات العملاء المدينة
  - المخزون (قيمة المنتجات)
  
- **الخصوم (Liabilities)**:
  - حسابات الموردين الدائنة
  
- **الإيرادات (Revenue)**:
  - المبيعات
  
- **المصروفات (Expenses)**:
  - تكلفة البضاعة المباعة
  - المشتريات
  - المصروفات العامة (مجمعة حسب الفئة)

### 💾 إدارة التقارير
- حفظ التقارير للمراجعة اللاحقة
- عرض قائمة التقارير المحفوظة
- حذف التقارير غير المطلوبة
- تحميل التقارير المحفوظة للعرض

### 📱 واجهة المستخدم
- واجهة مستخدم سهلة ومتجاوبة
- عرض التقرير في جدول منظم
- ملخص سريع للأرصدة
- مؤشرات بصرية لحالة التوازن

## 🏗️ هيكل الميزة

```
lib/features/trial_balance/
├── models/
│   └── trial_balance_model.dart      # نماذج البيانات
├── services/
│   └── trial_balance_service.dart    # خدمات البيانات
├── providers/
│   └── trial_balance_provider.dart   # مزودات البيانات (Riverpod)
├── screens/
│   └── trial_balance_screen.dart     # شاشة التقرير الرئيسية
├── trial_balance_feature.dart        # ملف الميزة الرئيسي
└── README.md                         # هذا الملف
```

## 🔧 التكامل مع Firebase

### مصادر البيانات
الميزة تجمع البيانات من المسارات التالية في Firebase:

```
constUserId/
├── Sales Transition/          # بيانات المبيعات
├── Purchase Transition/       # بيانات المشتريات
├── Customers/                 # بيانات العملاء
├── Suppliers/                 # بيانات الموردين
├── Expense/                   # بيانات المصروفات
├── Treasury/Balance/          # رصيد الخزينة
├── Products/                  # بيانات المنتجات
└── Trial Balance Reports/     # التقارير المحفوظة
```

### هيكل التقرير المحفوظ
```json
{
  "reportId": "unique_id",
  "reportTitle": "تقرير ميزان المراجعة - 2024/01/15",
  "reportDate": "2024-01-15T10:30:00.000Z",
  "fromDate": "2024-01-01T00:00:00.000Z",
  "toDate": "2024-01-31T23:59:59.000Z",
  "items": [
    {
      "accountName": "النقدية في الخزينة",
      "accountCode": "1001",
      "debitAmount": 15000.00,
      "creditAmount": 0.00,
      "accountType": "الأصول",
      "description": "الرصيد النقدي الحالي في الخزينة"
    }
  ],
  "totalDebits": 50000.00,
  "totalCredits": 50000.00,
  "isBalanced": true,
  "generatedBy": "user_id",
  "generatedAt": "2024-01-15T10:30:00.000Z"
}
```

## 📊 منطق الحسابات

### الحسابات المدينة (Debit)
- النقدية الموجبة في الخزينة
- مديونية العملاء (المبالغ المستحقة من العملاء)
- قيمة المخزون (الكمية × سعر الشراء)
- تكلفة البضاعة المباعة
- المشتريات
- المصروفات

### الحسابات الدائنة (Credit)
- النقدية السالبة في الخزينة (إذا وجدت)
- مديونية الموردين (المبالغ المستحقة للموردين)
- المبيعات

### التحقق من التوازن
```dart
bool isBalanced = (totalDebits - totalCredits).abs() < 0.01;
```

## 🚀 كيفية الاستخدام

### 1. الوصول إلى الميزة
```dart
// من خلال التنقل المباشر
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const TrialBalanceScreen()),
);

// أو من خلال المسارات المسجلة
Navigator.pushNamed(context, '/trial-balance');
```

### 2. إنشاء تقرير جديد
1. اختر الفترة الزمنية (محددة مسبقاً أو مخصصة)
2. أدخل عنوان التقرير
3. اضغط على "إنشاء التقرير"
4. راجع النتائج في تبويب "التقرير الحالي"

### 3. حفظ التقرير
1. بعد إنشاء التقرير، اضغط على "حفظ التقرير"
2. سيتم حفظ التقرير في Firebase
3. يمكن الوصول إليه من تبويب "التقارير المحفوظة"

## 🔌 التكامل مع النظام

### إضافة الميزة إلى التطبيق
الميزة مسجلة تلقائياً في `feature_registry.dart`:

```dart
import 'trial_balance/trial_balance_feature.dart';

final List<FeatureInterface> _availableFeatures = [
  // الميزات الأخرى...
  TrialBalanceFeature(),
];
```

### استخدام مزودات البيانات
```dart
// في أي شاشة أخرى
final trialBalanceState = ref.watch(trialBalanceProvider);

// إنشاء تقرير
await ref.read(trialBalanceProvider.notifier).generateReport(
  title: 'تقرير مخصص',
);

// الحصول على التقارير المحفوظة
final savedReports = await ref.read(savedTrialBalanceReportsProvider.future);
```

## 🛠️ التخصيص والإعدادات

### إعدادات الميزة
```dart
final config = TrialBalanceFeature().getConfiguration();
// {
//   'default_date_range': 'current_month',
//   'auto_save_reports': true,
//   'show_account_codes': true,
//   'group_by_account_type': true,
//   'decimal_places': 2,
//   'currency_symbol': 'جنيه',
//   'date_format': 'yyyy/MM/dd',
//   'number_format': '#,##0.00',
// }
```

### إضافة حسابات جديدة
لإضافة حسابات جديدة، قم بتعديل `TrialBalanceService`:

```dart
// في _getNewAccountType
Future<List<TrialBalanceItem>> _getNewAccountType(DateTime fromDate, DateTime toDate) async {
  // منطق جمع البيانات الجديدة
  return items;
}

// ثم أضفها إلى generateTrialBalance
items.addAll(await _getNewAccountType(fromDate, toDate));
```

## 🔒 الأمان والصلاحيات

### الصلاحيات المطلوبة
- `read_sales_data`: قراءة بيانات المبيعات
- `read_purchase_data`: قراءة بيانات المشتريات
- `read_customer_data`: قراءة بيانات العملاء
- `read_supplier_data`: قراءة بيانات الموردين
- `read_expense_data`: قراءة بيانات المصروفات
- `read_treasury_data`: قراءة بيانات الخزينة
- `read_inventory_data`: قراءة بيانات المخزون
- `create_reports`: إنشاء التقارير
- `save_reports`: حفظ التقارير
- `delete_reports`: حذف التقارير

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **التقرير غير متوازن**
   - تحقق من صحة البيانات في Firebase
   - راجع منطق الحسابات في `TrialBalanceService`

2. **بيانات مفقودة**
   - تأكد من وجود البيانات في المسارات المطلوبة
   - تحقق من صلاحيات الوصول

3. **أخطاء في التواريخ**
   - تأكد من صيغة التواريخ في البيانات
   - راجع منطق مقارنة التواريخ

## 🔮 التطوير المستقبلي

### ميزات مخططة
- [ ] تصدير التقارير إلى PDF
- [ ] تصدير التقارير إلى Excel
- [ ] طباعة التقارير
- [ ] مقارنة التقارير بين فترات مختلفة
- [ ] رسوم بيانية للأرصدة
- [ ] تنبيهات عدم التوازن
- [ ] تقارير فرعية حسب نوع الحساب

### تحسينات مقترحة
- [ ] تحسين الأداء للبيانات الكبيرة
- [ ] إضافة فلاتر متقدمة
- [ ] دعم عملات متعددة
- [ ] واجهة مستخدم محسنة للأجهزة اللوحية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع التوثيق في الكود
- تحقق من رسائل الخطأ في وحدة التحكم
- راجع ملفات السجل (logs) للتفاصيل

---

**ملاحظة**: هذه الميزة تم تطويرها كجزء من نظام AmrDevPOS وتتطلب وجود البيانات الأساسية في Firebase لتعمل بشكل صحيح.
