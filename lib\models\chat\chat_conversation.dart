/// فئة صورة المجموعة
class GroupImage {
  String? url;
  String? name;
  String? type;

  GroupImage({this.url, this.name, this.type});
}

/// نموذج المحادثة
class ChatConversation {
  String? objectId;
  List<String> participants;
  String lastMessageText;
  DateTime lastMessageDate;
  String lastMessageSenderId;
  int unreadCount;
  bool isGroup;
  String? groupName;
  GroupImage? groupImage;
  String? groupDescription;
  List<String> groupAdmins;
  List<String> muted;
  List<String> pinned;
  String lastMessageType;
  DateTime? createdAt;

  // خريطة لتخزين عدد الرسائل غير المقروءة لكل مستخدم
  Map<String, int> unreadCounts = {};

  // أسماء الحقول (للتوافق مع الكود القديم)
  static const String keyParticipants = 'participants';
  static const String keyLastMessageText = 'lastMessageText';
  static const String keyLastMessageDate = 'lastMessageDate';
  static const String keyLastMessageSenderId = 'lastMessageSenderId';
  static const String keyUnreadCount = 'unreadCount';
  static const String keyIsGroup = 'isGroup';
  static const String keyGroupName = 'groupName';
  static const String keyGroupImage = 'groupImage';
  static const String keyGroupDescription = 'groupDescription';
  static const String keyGroupAdmins = 'groupAdmins';
  static const String keyMuted = 'muted';
  static const String keyPinned = 'pinned';
  static const String keyLastMessageType = 'lastMessageType';

  // المُنشئ
  ChatConversation({
    this.objectId,
    required this.participants,
    required this.lastMessageText,
    required this.lastMessageDate,
    required this.lastMessageSenderId,
    this.unreadCount = 0,
    required this.isGroup,
    this.groupName,
    this.groupImage,
    this.groupDescription,
    required this.groupAdmins,
    required this.muted,
    required this.pinned,
    required this.lastMessageType,
    this.createdAt,
  });

  // الحصول على عدد الرسائل غير المقروءة لمستخدم معين
  int getUnreadCountForUser(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  // تعيين عدد الرسائل غير المقروءة لمستخدم معين
  void setUnreadCountForUser(String userId, int count) {
    unreadCounts[userId] = count;
  }

  // الحصول على الحقول المخصصة
  Map<String, dynamic> getCustomAttributes() {
    final attributes = <String, dynamic>{};

    // إضافة unreadCounts
    for (final entry in unreadCounts.entries) {
      attributes['unreadCount_${entry.key}'] = entry.value;
    }

    return attributes;
  }

  // دوال مساعدة للتوافق مع الكود القديم

  // الحصول على قيمة من الخريطة
  T? get<T>(String key) {
    switch (key) {
      case keyParticipants:
        return participants as T?;
      case keyLastMessageText:
        return lastMessageText as T?;
      case keyLastMessageDate:
        return lastMessageDate as T?;
      case keyLastMessageSenderId:
        return lastMessageSenderId as T?;
      case keyUnreadCount:
        return unreadCount as T?;
      case keyIsGroup:
        return isGroup as T?;
      case keyGroupName:
        return groupName as T?;
      case keyGroupImage:
        return groupImage as T?;
      case keyGroupDescription:
        return groupDescription as T?;
      case keyGroupAdmins:
        return groupAdmins as T?;
      case keyMuted:
        return muted as T?;
      case keyPinned:
        return pinned as T?;
      case keyLastMessageType:
        return lastMessageType as T?;
      default:
        if (key.startsWith('unreadCount_')) {
          final userId = key.substring('unreadCount_'.length);
          return getUnreadCountForUser(userId) as T?;
        }
        return null;
    }
  }

  // تعيين قيمة في الخريطة
  void set<T>(String key, T? value) {
    switch (key) {
      case keyParticipants:
        participants = value as List<String>;
        break;
      case keyLastMessageText:
        lastMessageText = value as String;
        break;
      case keyLastMessageDate:
        lastMessageDate = value as DateTime;
        break;
      case keyLastMessageSenderId:
        lastMessageSenderId = value as String;
        break;
      case keyUnreadCount:
        unreadCount = value as int;
        break;
      case keyIsGroup:
        isGroup = value as bool;
        break;
      case keyGroupName:
        groupName = value as String?;
        break;
      case keyGroupImage:
        groupImage = value as GroupImage?;
        break;
      case keyGroupDescription:
        groupDescription = value as String?;
        break;
      case keyGroupAdmins:
        groupAdmins = value as List<String>;
        break;
      case keyMuted:
        muted = value as List<String>;
        break;
      case keyPinned:
        pinned = value as List<String>;
        break;
      case keyLastMessageType:
        lastMessageType = value as String;
        break;
      default:
        if (key.startsWith('unreadCount_')) {
          final userId = key.substring('unreadCount_'.length);
          setUnreadCountForUser(userId, value as int);
        }
        break;
    }
  }

  // إنشاء نسخة من الكائن
  ChatConversation clone(Map<String, dynamic> map) {
    return ChatConversation.fromMap(map);
  }

  // إنشاء نموذج من Map
  factory ChatConversation.fromMap(Map<String, dynamic> map) {
    // إنشاء groupImage إذا كان هناك groupImage في الخريطة
    GroupImage? groupImageObj;
    if (map['groupImage'] != null) {
      if (map['groupImage'] is String) {
        groupImageObj = GroupImage(url: map['groupImage']);
      } else if (map['groupImage'] is Map) {
        final groupImageMap = map['groupImage'] as Map;
        groupImageObj = GroupImage(
          url: groupImageMap['url'],
          name: groupImageMap['name'],
          type: groupImageMap['type'],
        );
      }
    }

    return ChatConversation(
      objectId: map['objectId'],
      participants: List<String>.from(map['participants'] ?? []),
      lastMessageText: map['lastMessageText'] ?? '',
      lastMessageDate: map['lastMessageDate'] != null
          ? DateTime.parse(map['lastMessageDate'])
          : DateTime.now(),
      lastMessageSenderId: map['lastMessageSenderId'] ?? '',
      unreadCount: map['unreadCount'] ?? 0,
      isGroup: map['isGroup'] ?? false,
      groupName: map['groupName'],
      groupImage: groupImageObj,
      groupDescription: map['groupDescription'],
      groupAdmins: List<String>.from(map['groupAdmins'] ?? []),
      muted: List<String>.from(map['muted'] ?? []),
      pinned: List<String>.from(map['pinned'] ?? []),
      lastMessageType: map['lastMessageType'] ?? 'text',
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
    );
  }

  // تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    final map = {
      'objectId': objectId,
      'participants': participants,
      'lastMessageText': lastMessageText,
      'lastMessageDate': lastMessageDate.toIso8601String(),
      'lastMessageSenderId': lastMessageSenderId,
      'unreadCount': unreadCount,
      'isGroup': isGroup,
      'groupName': groupName,
      'groupDescription': groupDescription,
      'groupAdmins': groupAdmins,
      'muted': muted,
      'pinned': pinned,
      'lastMessageType': lastMessageType,
      'createdAt': createdAt?.toIso8601String(),
    };

    // إضافة معلومات groupImage إذا كانت موجودة
    if (groupImage != null && groupImage!.url != null) {
      map['groupImage'] = {
        'url': groupImage!.url,
        'name': groupImage!.name,
        'type': groupImage!.type,
      };
    }

    // إضافة unreadCounts
    if (unreadCounts.isNotEmpty) {
      for (final entry in unreadCounts.entries) {
        map['unreadCount_${entry.key}'] = entry.value;
      }
    }

    return map;
  }

  // تحويل النموذج إلى JSON
  Map<String, dynamic> fromJson(Map<String, dynamic> map) {
    objectId = map['objectId'];
    participants = List<String>.from(map['participants'] ?? []);
    lastMessageText = map['lastMessageText'] ?? '';
    lastMessageDate = map['lastMessageDate'] != null
        ? DateTime.parse(map['lastMessageDate'])
        : DateTime.now();
    lastMessageSenderId = map['lastMessageSenderId'] ?? '';
    unreadCount = map['unreadCount'] ?? 0;
    isGroup = map['isGroup'] ?? false;
    groupName = map['groupName'];

    if (map['groupImage'] != null) {
      if (map['groupImage'] is String) {
        groupImage = GroupImage(url: map['groupImage']);
      } else if (map['groupImage'] is Map) {
        final groupImageMap = map['groupImage'] as Map;
        groupImage = GroupImage(
          url: groupImageMap['url'],
          name: groupImageMap['name'],
          type: groupImageMap['type'],
        );
      }
    }

    groupDescription = map['groupDescription'];
    groupAdmins = List<String>.from(map['groupAdmins'] ?? []);
    muted = List<String>.from(map['muted'] ?? []);
    pinned = List<String>.from(map['pinned'] ?? []);
    lastMessageType = map['lastMessageType'] ?? 'text';
    createdAt =
        map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null;

    // استخراج unreadCounts من الحقول المخصصة
    for (final key in map.keys) {
      if (key.startsWith('unreadCount_')) {
        final userId = key.substring('unreadCount_'.length);
        final count = map[key] as int? ?? 0;
        unreadCounts[userId] = count;
      }
    }

    return map;
  }

  // حفظ المحادثة (للتوافق مع الكود القديم)
  Future<ParseResponse> save() async {
    // هذه الدالة للتوافق فقط، لا تقوم بأي شيء
    return ParseResponse(
      success: true,
      results: [this],
      statusCode: 200,
      error: null,
    );
  }
}

// فئة ParseResponse للتوافق مع الكود القديم
class ParseResponse {
  final bool success;
  final List<dynamic>? results;
  final int statusCode;
  final ParseError? error;

  ParseResponse({
    required this.success,
    this.results,
    required this.statusCode,
    this.error,
  });
}

// فئة ParseError للتوافق مع الكود القديم
class ParseError {
  final String message;
  final int code;

  ParseError({
    required this.message,
    required this.code,
  });
}
