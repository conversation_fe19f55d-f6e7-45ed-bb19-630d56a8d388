<!DOCTYPE html>
<html lang="en">
<head>
    <title></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
         /* In order to place the tracking correctly */
         canvas.drawing, canvas.drawingBuffer {
         position: absolute;
         left: 0;
         top: 0;
         }
         html, body {
         height: 100%; /* IMPORTANT!!! Stretches viewport to 100% */
         }
      </style>
    <script src="html5-qrcode.min.js"></script>
</head>
<body>
<!-- Div to show the scanner -->
<div id="reader" ></div>
<script>
  //refer doc here https://github.com/mebjas/html5-qrcode
 const html5QrCode = new Html5Qrcode("reader");
 console.log("Starting SCANNING CODE");
 const qrCodeSuccessCallback = (decodedText, decodedResult) => {
     html5QrCode.stop();
     /* handle success for web */
     window.parent.postMessage(decodedText, "*");

     /* handle success for window */
     if (window.chrome.webview != "undefined") {
         var param = {
             "methodName": "successCallback",
             "data": decodedText
         }
         window.chrome.webview.postMessage(param);
     }

 };
 const config = {
     fps: 10,
     qrbox: {
         width: 280,
         height: 120,
         aspectRatio: 1.7777778
     }
 };

 // If you want to prefer back camera
 html5QrCode.start({
     facingMode: "environment"
 }, config, qrCodeSuccessCallback);
 //html5QrCode.start({ facingMode: "user" }, config, qrCodeSuccessCallback);

 //Window event listener
 if (window.chrome.webview != undefined) {
     window.chrome.webview.addEventListener('message', function(e) {
         let data = JSON.parse(JSON.stringify(e.data));
         if (data.event === "close") {
             html5QrCode.stop();
         }

     });
 }
 </script>
</body>
</html>