// بسم الله الرحمن الرحيم
// نموذج حركة المخزون - يمثل بيانات حركة المخزون

import 'dart:convert';

/// نوع حركة المخزون
enum StockMovementType {
  /// إضافة مخزون
  addition,

  /// سحب مخزون
  withdrawal,

  /// تعديل مخزون
  adjustment,

  /// نقل مخزون
  transfer,

  /// بيع
  sale,

  /// مرتجع
  return_,

  /// تالف
  damaged,

  /// جرد
  inventory,
}

/// نموذج حركة المخزون
class StockMovementModel {
  /// ينشئ نموذج حركة المخزون
  const StockMovementModel({
    required this.id,
    required this.productId,
    required this.type,
    required this.quantity,
    required this.createdAt,
    this.referenceId,
    this.referenceType,
    this.fromLocationId,
    this.toLocationId,
    this.notes,
    this.createdBy,
    this.previousQuantity,
    this.newQuantity,
  });

  /// معرف الحركة
  final String id;

  /// معرف المنتج
  final String productId;

  /// نوع الحركة
  final StockMovementType type;

  /// الكمية
  final int quantity;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// معرف المرجع (اختياري)
  final String? referenceId;

  /// نوع المرجع (اختياري)
  final String? referenceType;

  /// معرف الموقع المصدر (اختياري)
  final String? fromLocationId;

  /// معرف الموقع الهدف (اختياري)
  final String? toLocationId;

  /// ملاحظات (اختياري)
  final String? notes;

  /// منشئ الحركة (اختياري)
  final String? createdBy;

  /// الكمية السابقة (اختياري)
  final int? previousQuantity;

  /// الكمية الجديدة (اختياري)
  final int? newQuantity;

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'type': type.index,
      'quantity': quantity,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'referenceId': referenceId,
      'referenceType': referenceType,
      'fromLocationId': fromLocationId,
      'toLocationId': toLocationId,
      'notes': notes,
      'createdBy': createdBy,
      'previousQuantity': previousQuantity,
      'newQuantity': newQuantity,
    };
  }

  /// إنشاء نموذج من Map
  factory StockMovementModel.fromMap(Map<String, dynamic> map) {
    return StockMovementModel(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      type: StockMovementType.values[map['type'] ?? 0],
      quantity: map['quantity'] ?? 0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      referenceId: map['referenceId'],
      referenceType: map['referenceType'],
      fromLocationId: map['fromLocationId'],
      toLocationId: map['toLocationId'],
      notes: map['notes'],
      createdBy: map['createdBy'],
      previousQuantity: map['previousQuantity'],
      newQuantity: map['newQuantity'],
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory StockMovementModel.fromJson(String source) =>
      StockMovementModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'StockMovementModel(id: $id, productId: $productId, type: $type, quantity: $quantity)';
  }
}
