import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:mobile_pos/model/personal_information_model.dart';

class ProfileRepo {
  final DatabaseReference ref = FirebaseDatabase.instance.ref();
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<String> getUserID() async {
    return auth.currentUser?.uid ?? '';
  }

  Future<PersonalInformationModel> getDetails() async {
    PersonalInformationModel personalInfo = PersonalInformationModel(
        companyName: 'Loading...',
        businessCategory: 'Loading...',
        countryName: 'Loading...',
        language: 'Loading...',
        phoneNumber: 'Loading...',
        gst: '',
        pictureUrl:
            'https://cdn.pixabay.com/photo/2017/06/13/12/53/profile-2398782_960_720.png');

    try {
      String userId = await getUserID();
      DatabaseReference userRef;

      if (Platform.isWindows) {
        // في الويندوز نستخدم المسار الكامل
        userRef = FirebaseDatabase.instance
            .ref()
            .child('users')
            .child(userId)
            .child('Personal Information');
      } else {
        // في الأندرويد نستخدم المسار المختصر
        String uid = await getUserID();
        userRef =
            FirebaseDatabase.instance.ref(uid).child('Personal Information');
      }

      DatabaseEvent event = await userRef.once();
      if (event.snapshot.value == null) {
        return personalInfo;
      }

      var data = jsonDecode(jsonEncode(event.snapshot.value));
      return PersonalInformationModel.fromJson(data);
    } catch (e) {
      debugPrint('Error in getDetails: $e');
      return personalInfo;
    }
  }

  Future<bool> isProfileSetupDone() async {
    try {
      String userId = await getUserID();
      DatabaseReference userRef;

      if (Platform.isWindows) {
        // في الويندوز نستخدم المسار الكامل
        userRef = FirebaseDatabase.instance
            .ref()
            .child('users')
            .child(userId)
            .child('Personal Information');
      } else {
        // في الأندرويد نستخدم المسار المختصر
        userRef = ref.child('${await getUserID()}/Personal Information');
      }

      DatabaseEvent event = await userRef.once();
      return event.snapshot.value != null;
    } catch (e) {
      debugPrint('Error in isProfileSetupDone: $e');
      return false;
    }
  }
}
