buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // إعدادات عامة لإخفاء التحذيرات
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:none"
            options.compilerArgs << "-Xlint:-options"
            options.compilerArgs << "-Xlint:-deprecation"
            options.compilerArgs << "-Xlint:-unchecked"
            options.compilerArgs << "-Xlint:-removal"
            options.compilerArgs << "-nowarn"
            options.warnings = false
        }

        tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
            kotlinOptions {
                freeCompilerArgs += [
                    "-opt-in=kotlin.RequiresOptIn",
                    "-Xno-param-assertions",
                    "-Xno-call-assertions",
                    "-Xno-receiver-assertions",
                    "-Xsuppress-version-warnings"
                ]
                suppressWarnings = true
                allWarningsAsErrors = false
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
