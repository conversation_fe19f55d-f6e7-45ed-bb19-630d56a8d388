# إخفاء جميع التحذيرات والأخطاء غير المهمة
-dontwarn **
-ignorewarnings

# تجاهل كافة التحذيرات للفئات المتعلقة بـ SSLCommerz
-dontwarn com.sslwireless.sslcommerz.**
-keep class com.sslwireless.sslcommerz.** { *; }

# تجاهل كافة التحذيرات للفئات المتعلقة بـ Stripe Push Provisioning
-dontwarn com.stripe.android.pushProvisioning.**
-keep class com.stripe.android.pushProvisioning.** { *; }

# إعدادات Android 36
-dontwarn java.lang.invoke.StringConcatFactory
-keep class androidx.** { *; }
-keep class com.google.android.material.** { *; }
-dontwarn org.bouncycastle.**
-dontwarn org.conscrypt.**
-dontwarn org.openjsse.**

# إخفاء تحذيرات Flutter TTS
-dontwarn com.tundralabs.fluttertts.**
-keep class com.tundralabs.fluttertts.** { *; }

# إخفاء تحذيرات Kotlin
-dontwarn kotlin.**
-dontwarn kotlinx.**
-keep class kotlin.** { *; }

# إخفاء تحذيرات Firebase
-dontwarn com.google.firebase.**
-keep class com.google.firebase.** { *; }

# إخفاء تحذيرات flutter_webrtc
-dontwarn com.cloudwebrtc.webrtc.**
-keep class com.cloudwebrtc.webrtc.** { *; }

# إخفاء تحذيرات عامة
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
-dontwarn com.google.common.**

# إعدادات عامة لإخفاء التحذيرات
-verbose
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt
