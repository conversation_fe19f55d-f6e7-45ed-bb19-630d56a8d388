// بسم الله الرحمن الرحيم
// خدمة بيانات تقرير Trial Balance

import 'dart:convert';
import 'dart:developer';
import 'package:firebase_database/firebase_database.dart';
import '../../../constant.dart';
import '../../../currency.dart';
import '../../../model/transition_model.dart';
import '../../../Screens/Customers/Model/customer_model.dart';
import '../../../model/product_model.dart';
import '../../../model/expense_model.dart';
import '../models/trial_balance_model.dart';

class TrialBalanceService {
  static const String _reportsPath = 'Trial Balance Reports';

  /// إنشاء تقرير Trial Balance جديد
  Future<TrialBalanceReport> generateTrialBalance({
    required DateTime fromDate,
    required DateTime toDate,
    String? reportTitle,
  }) async {
    try {
      log('بدء إنشاء تقرير ميزان المراجعة من ${fromDate.toString()} إلى ${toDate.toString()}');

      List<TrialBalanceItem> items = [];

      // 1. جمع بيانات النقدية من الخزينة
      items.addAll(await _getCashAccounts(fromDate, toDate));

      // 2. جمع بيانات العملاء (حسابات مدينة)
      items.addAll(await _getCustomerAccounts(fromDate, toDate));

      // 3. جمع بيانات الموردين (حسابات دائنة)
      items.addAll(await _getSupplierAccounts(fromDate, toDate));

      // 4. جمع بيانات المبيعات (إيرادات)
      items.addAll(await _getSalesAccounts(fromDate, toDate));

      // 5. جمع بيانات المشتريات (مصروفات)
      items.addAll(await _getPurchaseAccounts(fromDate, toDate));

      // 6. جمع بيانات المصروفات
      items.addAll(await _getExpenseAccounts(fromDate, toDate));

      // 7. جمع بيانات المخزون
      items.addAll(await _getInventoryAccounts());

      // حساب الإجماليات
      double totalDebits = items.fold(0, (sum, item) => sum + item.debitAmount);
      double totalCredits =
          items.fold(0, (sum, item) => sum + item.creditAmount);

      // إنشاء التقرير
      final report = TrialBalanceReport(
        reportId: DateTime.now().millisecondsSinceEpoch.toString(),
        reportTitle: reportTitle ?? 'تقرير ميزان المراجعة',
        reportDate: DateTime.now(),
        fromDate: fromDate,
        toDate: toDate,
        items: items,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        isBalanced: (totalDebits - totalCredits).abs() < 0.01,
        generatedBy: constUserId,
        generatedAt: DateTime.now(),
      );

      log('تم إنشاء تقرير ميزان المراجعة بنجاح');
      log('إجمالي المدين: $totalDebits');
      log('إجمالي الدائن: $totalCredits');
      log('متوازن: ${report.isBalanced}');

      return report;
    } catch (e, stackTrace) {
      log('خطأ في إنشاء تقرير ميزان المراجعة: $e');
      log('تفاصيل الخطأ: $stackTrace');
      rethrow;
    }
  }

  /// جمع بيانات النقدية من الخزينة
  Future<List<TrialBalanceItem>> _getCashAccounts(
      DateTime fromDate, DateTime toDate) async {
    try {
      final treasuryRef =
          FirebaseDatabase.instance.ref('$constUserId/Treasury/Balance');
      final snapshot = await treasuryRef.get();

      if (!snapshot.exists) {
        return [];
      }

      final data = jsonDecode(jsonEncode(snapshot.value));
      final currentBalance =
          double.tryParse(data['currentBalance']?.toString() ?? '0') ?? 0;

      return [
        TrialBalanceItem(
          accountName: 'النقدية في الخزينة',
          accountCode: '1001',
          debitAmount: currentBalance > 0 ? currentBalance : 0,
          creditAmount: currentBalance < 0 ? currentBalance.abs() : 0,
          accountType: AccountCategory.assets,
          description: 'الرصيد النقدي الحالي في الخزينة',
        ),
      ];
    } catch (e) {
      log('خطأ في جمع بيانات النقدية: $e');
      return [];
    }
  }

  /// جمع بيانات العملاء (حسابات مدينة)
  Future<List<TrialBalanceItem>> _getCustomerAccounts(
      DateTime fromDate, DateTime toDate) async {
    try {
      final customersRef =
          FirebaseDatabase.instance.ref('$constUserId/Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        return [];
      }

      List<TrialBalanceItem> items = [];
      double totalCustomerDue = 0;

      for (var element in snapshot.children) {
        try {
          final customerData =
              CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
          final dueAmount = double.tryParse(customerData.dueAmount) ?? 0;

          if (dueAmount != 0) {
            totalCustomerDue += dueAmount;
          }
        } catch (e) {
          continue;
        }
      }

      if (totalCustomerDue != 0) {
        items.add(
          TrialBalanceItem(
            accountName: 'حسابات العملاء',
            accountCode: '1201',
            debitAmount: totalCustomerDue > 0 ? totalCustomerDue : 0,
            creditAmount: totalCustomerDue < 0 ? totalCustomerDue.abs() : 0,
            accountType: AccountCategory.assets,
            description: 'إجمالي مديونية العملاء',
          ),
        );
      }

      return items;
    } catch (e) {
      log('خطأ في جمع بيانات العملاء: $e');
      return [];
    }
  }

  /// جمع بيانات الموردين (حسابات دائنة)
  Future<List<TrialBalanceItem>> _getSupplierAccounts(
      DateTime fromDate, DateTime toDate) async {
    try {
      final suppliersRef =
          FirebaseDatabase.instance.ref('$constUserId/Suppliers');
      final snapshot = await suppliersRef.get();

      if (!snapshot.exists) {
        return [];
      }

      List<TrialBalanceItem> items = [];
      double totalSupplierDue = 0;

      for (var element in snapshot.children) {
        try {
          final supplierData = jsonDecode(jsonEncode(element.value));
          final dueAmount =
              double.tryParse(supplierData['dueAmount']?.toString() ?? '0') ??
                  0;

          if (dueAmount != 0) {
            totalSupplierDue += dueAmount;
          }
        } catch (e) {
          continue;
        }
      }

      if (totalSupplierDue != 0) {
        items.add(
          TrialBalanceItem(
            accountName: 'حسابات الموردين',
            accountCode: '2001',
            debitAmount: totalSupplierDue < 0 ? totalSupplierDue.abs() : 0,
            creditAmount: totalSupplierDue > 0 ? totalSupplierDue : 0,
            accountType: AccountCategory.liabilities,
            description: 'إجمالي مديونية الموردين',
          ),
        );
      }

      return items;
    } catch (e) {
      log('خطأ في جمع بيانات الموردين: $e');
      return [];
    }
  }

  /// جمع بيانات المبيعات (إيرادات)
  Future<List<TrialBalanceItem>> _getSalesAccounts(
      DateTime fromDate, DateTime toDate) async {
    try {
      final salesRef =
          FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
      final snapshot = await salesRef.get();

      if (!snapshot.exists) {
        return [];
      }

      double totalSales = 0;
      double totalCostOfSales = 0;

      for (var element in snapshot.children) {
        try {
          final saleData = SalesTransitionModel.fromJson(
              jsonDecode(jsonEncode(element.value)));
          final saleDate = DateTime.parse(saleData.purchaseDate);

          if (saleDate.isAfter(fromDate.subtract(const Duration(days: 1))) &&
              saleDate.isBefore(toDate.add(const Duration(days: 1)))) {
            totalSales += saleData.totalAmount ?? 0;

            // حساب تكلفة البضاعة المباعة
            if (saleData.productList != null) {
              for (var product in saleData.productList!) {
                totalCostOfSales +=
                    (product.subTotal ?? 0) - (saleData.lossProfit ?? 0);
              }
            }
          }
        } catch (e) {
          continue;
        }
      }

      List<TrialBalanceItem> items = [];

      if (totalSales > 0) {
        items.add(
          TrialBalanceItem(
            accountName: 'المبيعات',
            accountCode: '4001',
            debitAmount: 0,
            creditAmount: totalSales,
            accountType: AccountCategory.revenue,
            description: 'إجمالي المبيعات للفترة',
          ),
        );
      }

      if (totalCostOfSales > 0) {
        items.add(
          TrialBalanceItem(
            accountName: 'تكلفة البضاعة المباعة',
            accountCode: '5001',
            debitAmount: totalCostOfSales,
            creditAmount: 0,
            accountType: AccountCategory.expenses,
            description: 'تكلفة البضاعة المباعة للفترة',
          ),
        );
      }

      return items;
    } catch (e) {
      log('خطأ في جمع بيانات المبيعات: $e');
      return [];
    }
  }

  /// جمع بيانات المشتريات
  Future<List<TrialBalanceItem>> _getPurchaseAccounts(
      DateTime fromDate, DateTime toDate) async {
    try {
      final purchaseRef =
          FirebaseDatabase.instance.ref('$constUserId/Purchase Transition');
      final snapshot = await purchaseRef.get();

      if (!snapshot.exists) {
        return [];
      }

      double totalPurchases = 0;

      for (var element in snapshot.children) {
        try {
          final purchaseData = PurchaseTransactionModel.fromJson(
              jsonDecode(jsonEncode(element.value)));
          final purchaseDate = DateTime.parse(purchaseData.purchaseDate);

          if (purchaseDate
                  .isAfter(fromDate.subtract(const Duration(days: 1))) &&
              purchaseDate.isBefore(toDate.add(const Duration(days: 1)))) {
            totalPurchases += purchaseData.totalAmount ?? 0;
          }
        } catch (e) {
          continue;
        }
      }

      List<TrialBalanceItem> items = [];

      if (totalPurchases > 0) {
        items.add(
          TrialBalanceItem(
            accountName: 'المشتريات',
            accountCode: '5002',
            debitAmount: totalPurchases,
            creditAmount: 0,
            accountType: AccountCategory.expenses,
            description: 'إجمالي المشتريات للفترة',
          ),
        );
      }

      return items;
    } catch (e) {
      log('خطأ في جمع بيانات المشتريات: $e');
      return [];
    }
  }

  /// جمع بيانات المصروفات
  Future<List<TrialBalanceItem>> _getExpenseAccounts(
      DateTime fromDate, DateTime toDate) async {
    try {
      final expenseRef = FirebaseDatabase.instance.ref('$constUserId/Expense');
      final snapshot = await expenseRef.get();

      if (!snapshot.exists) {
        return [];
      }

      Map<String, double> expenseCategories = {};

      for (var element in snapshot.children) {
        try {
          final expenseData =
              ExpenseModel.fromJson(jsonDecode(jsonEncode(element.value)));
          final expenseDate = DateTime.parse(expenseData.expenseDate);

          if (expenseDate.isAfter(fromDate.subtract(const Duration(days: 1))) &&
              expenseDate.isBefore(toDate.add(const Duration(days: 1)))) {
            final category = expenseData.category.isNotEmpty
                ? expenseData.category
                : 'مصروفات عامة';
            expenseCategories[category] = (expenseCategories[category] ?? 0) +
                (double.tryParse(expenseData.amount) ?? 0);
          }
        } catch (e) {
          continue;
        }
      }

      List<TrialBalanceItem> items = [];
      int codeCounter = 5100;

      expenseCategories.forEach((category, amount) {
        if (amount > 0) {
          items.add(
            TrialBalanceItem(
              accountName: category,
              accountCode: codeCounter.toString(),
              debitAmount: amount,
              creditAmount: 0,
              accountType: AccountCategory.expenses,
              description: 'مصروفات $category للفترة',
            ),
          );
          codeCounter++;
        }
      });

      return items;
    } catch (e) {
      log('خطأ في جمع بيانات المصروفات: $e');
      return [];
    }
  }

  /// جمع بيانات المخزون
  Future<List<TrialBalanceItem>> _getInventoryAccounts() async {
    try {
      final productsRef =
          FirebaseDatabase.instance.ref('$constUserId/Products');
      final snapshot = await productsRef.get();

      if (!snapshot.exists) {
        return [];
      }

      double totalInventoryValue = 0;

      for (var element in snapshot.children) {
        try {
          final productData =
              ProductModel.fromJson(jsonDecode(jsonEncode(element.value)));
          final quantity = int.tryParse(productData.productStock) ?? 0;
          final purchasePrice =
              double.tryParse(productData.productPurchasePrice) ?? 0;

          totalInventoryValue += quantity * purchasePrice;
        } catch (e) {
          continue;
        }
      }

      List<TrialBalanceItem> items = [];

      if (totalInventoryValue > 0) {
        items.add(
          TrialBalanceItem(
            accountName: 'المخزون',
            accountCode: '1301',
            debitAmount: totalInventoryValue,
            creditAmount: 0,
            accountType: AccountCategory.assets,
            description: 'قيمة المخزون الحالي',
          ),
        );
      }

      return items;
    } catch (e) {
      log('خطأ في جمع بيانات المخزون: $e');
      return [];
    }
  }

  /// حفظ التقرير في Firebase
  Future<bool> saveReport(TrialBalanceReport report) async {
    try {
      final ref = FirebaseDatabase.instance
          .ref('$constUserId/$_reportsPath/${report.reportId}');
      await ref.set(report.toJson());
      log('تم حفظ تقرير ميزان المراجعة بنجاح');
      return true;
    } catch (e) {
      log('خطأ في حفظ التقرير: $e');
      return false;
    }
  }

  /// الحصول على جميع التقارير المحفوظة
  Future<List<TrialBalanceReport>> getSavedReports() async {
    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/$_reportsPath');
      final snapshot = await ref.get();

      if (!snapshot.exists) {
        return [];
      }

      List<TrialBalanceReport> reports = [];
      for (var element in snapshot.children) {
        try {
          final reportData = jsonDecode(jsonEncode(element.value));
          reports.add(TrialBalanceReport.fromJson(reportData));
        } catch (e) {
          continue;
        }
      }

      // ترتيب التقارير حسب تاريخ الإنشاء (الأحدث أولاً)
      reports.sort((a, b) => b.generatedAt.compareTo(a.generatedAt));

      return reports;
    } catch (e) {
      log('خطأ في جلب التقارير المحفوظة: $e');
      return [];
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/$_reportsPath/$reportId');
      await ref.remove();
      log('تم حذف التقرير بنجاح');
      return true;
    } catch (e) {
      log('خطأ في حذف التقرير: $e');
      return false;
    }
  }
}
