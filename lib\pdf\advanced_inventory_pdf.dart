// مولد ملف PDF لتقرير المخزون المتقدم
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/models/advanced_inventory_model.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// مولد ملف PDF لتقرير المخزون المتقدم
class AdvancedInventoryPdfGenerator {
  /// إنشاء ملف PDF
  Future<void> generatePdf(
    List<AdvancedInventoryModel> inventoryData,
    DateTime startDate,
    DateTime endDate,
    BuildContext context,
  ) async {
    final pdf = pw.Document();

    // إنشاء الخط العربي
    final arabicFont = await _getArabicFont();

    // إنشاء صفحة الغلاف
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'تقرير المخزون المتقدم',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  'الفترة: ${_formatDate(startDate)} - ${_formatDate(endDate)}',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 16,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 40),
                pw.Text(
                  'تاريخ التقرير: ${_formatDate(DateTime.now())}',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 12,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          );
        },
      ),
    );

    // إنشاء صفحة الملخص
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.all(20),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'ملخص التقرير',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'رصيد أول المدة',
                        myFormat.format(
                            _calculateTotalOpeningStockValue(inventoryData)),
                        'القيمة',
                      ),
                    ),
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'رصيد آخر المدة',
                        myFormat.format(
                            _calculateTotalClosingStockValue(inventoryData)),
                        'القيمة',
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'المشتريات',
                        myFormat.format(
                            _calculateTotalPurchasesValue(inventoryData)),
                        'القيمة',
                      ),
                    ),
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'المبيعات',
                        myFormat
                            .format(_calculateTotalSalesValue(inventoryData)),
                        'القيمة',
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryItem(
                        arabicFont,
                        'الربح الإجمالي',
                        myFormat
                            .format(_calculateTotalGrossProfit(inventoryData)),
                        'القيمة',
                      ),
                    ),
                    pw.Expanded(
                      child: pw.Container(),
                    ),
                  ],
                ),
                pw.SizedBox(height: 40),
                pw.Text(
                  'إحصائيات المخزون',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 10),
                pw.Table(
                  border: pw.TableBorder.all(),
                  children: [
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey300,
                      ),
                      children: [
                        _buildTableHeader(arabicFont, 'الفئة'),
                        _buildTableHeader(arabicFont, 'عدد الأصناف'),
                        _buildTableHeader(arabicFont, 'قيمة المخزون'),
                        _buildTableHeader(arabicFont, 'نسبة المخزون'),
                      ],
                    ),
                    ..._getCategoryStats(inventoryData).entries.map(
                          (entry) => pw.TableRow(
                            children: [
                              _buildTableCell(arabicFont, entry.key),
                              _buildTableCell(
                                  arabicFont, entry.value['count'].toString()),
                              _buildTableCell(arabicFont,
                                  myFormat.format(entry.value['value'])),
                              _buildTableCell(
                                arabicFont,
                                '${(entry.value['percentage'] as double).toStringAsFixed(2)}%',
                              ),
                            ],
                          ),
                        ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );

    // إنشاء صفحات تفاصيل المخزون
    for (var i = 0; i < inventoryData.length; i += 10) {
      final pageItems = inventoryData.skip(i).take(10).toList();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'تفاصيل المخزون',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 20),
                  pw.Table(
                    border: pw.TableBorder.all(),
                    children: [
                      pw.TableRow(
                        decoration: const pw.BoxDecoration(
                          color: PdfColors.grey300,
                        ),
                        children: [
                          _buildTableHeader(arabicFont, 'المنتج'),
                          _buildTableHeader(arabicFont, 'الفئة'),
                          _buildTableHeader(arabicFont, 'رصيد أول المدة'),
                          _buildTableHeader(arabicFont, 'رصيد آخر المدة'),
                          _buildTableHeader(arabicFont, 'المشتريات'),
                          _buildTableHeader(arabicFont, 'المبيعات'),
                        ],
                      ),
                      ...pageItems.map(
                        (item) => pw.TableRow(
                          children: [
                            _buildTableCell(arabicFont, item.productName),
                            _buildTableCell(arabicFont, item.category),
                            _buildTableCell(
                                arabicFont, item.openingStock.toString()),
                            _buildTableCell(
                                arabicFont, item.closingStock.toString()),
                            _buildTableCell(
                                arabicFont, item.purchases.toString()),
                            _buildTableCell(arabicFont, item.sales.toString()),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      );
    }

    // حفظ الملف
    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/تقرير_المخزون_المتقدم_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());

    // فتح الملف
    await OpenFile.open(file.path);

    // عرض رسالة نجاح
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// إنشاء عنصر ملخص
  pw.Widget _buildSummaryItem(
    pw.Font font,
    String title,
    String value,
    String subtitle,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            font: font,
            fontSize: 12,
            color: PdfColors.grey700,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: font,
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.Text(
          subtitle,
          style: pw.TextStyle(
            font: font,
            fontSize: 10,
            color: PdfColors.grey700,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  /// إنشاء عنوان جدول
  pw.Widget _buildTableHeader(pw.Font font, String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: 12,
          fontWeight: pw.FontWeight.bold,
        ),
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// إنشاء خلية جدول
  pw.Widget _buildTableCell(pw.Font font, String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: 10,
        ),
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// الحصول على الخط العربي
  Future<pw.Font> _getArabicFont() async {
    final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    return pw.Font.ttf(fontData.buffer.asByteData());
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  /// حساب إجمالي قيمة المخزون الافتتاحي
  double _calculateTotalOpeningStockValue(List<AdvancedInventoryModel> data) {
    return data.fold(0, (sum, item) => sum + item.openingStockValue);
  }

  /// حساب إجمالي قيمة المخزون الختامي
  double _calculateTotalClosingStockValue(List<AdvancedInventoryModel> data) {
    return data.fold(0, (sum, item) => sum + item.closingStockValue);
  }

  /// حساب إجمالي قيمة المشتريات
  double _calculateTotalPurchasesValue(List<AdvancedInventoryModel> data) {
    return data.fold(0, (sum, item) => sum + item.purchasesValue);
  }

  /// حساب إجمالي قيمة المبيعات
  double _calculateTotalSalesValue(List<AdvancedInventoryModel> data) {
    return data.fold(0, (sum, item) => sum + item.salesValue);
  }

  /// حساب إجمالي الربح الإجمالي
  double _calculateTotalGrossProfit(List<AdvancedInventoryModel> data) {
    return data.fold(0, (sum, item) => sum + item.grossProfit);
  }

  /// الحصول على إحصائيات الفئات
  Map<String, Map<String, dynamic>> _getCategoryStats(
      List<AdvancedInventoryModel> data) {
    final stats = <String, Map<String, dynamic>>{};
    final totalValue = _calculateTotalClosingStockValue(data);

    for (var item in data) {
      if (!stats.containsKey(item.category)) {
        stats[item.category] = {
          'count': 0,
          'value': 0.0,
          'percentage': 0.0,
        };
      }

      stats[item.category]!['count'] = stats[item.category]!['count'] + 1;
      stats[item.category]!['value'] =
          stats[item.category]!['value'] + item.closingStockValue;
    }

    // حساب النسبة المئوية
    for (var category in stats.keys) {
      stats[category]!['percentage'] =
          (stats[category]!['value'] / totalValue) * 100;
    }

    return stats;
  }
}
