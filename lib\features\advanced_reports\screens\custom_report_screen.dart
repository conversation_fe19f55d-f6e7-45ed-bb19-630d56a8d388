// بسم الله الرحمن الرحيم
// شاشة التقرير المخصص - تتيح للمستخدم إنشاء تقارير مخصصة

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/report_model.dart';
import '../services/report_service.dart';
import '../services/export_service.dart';
import '../widgets/report_chart.dart';
import '../widgets/report_table.dart';
import '../widgets/report_filter.dart';

/// شاشة التقرير المخصص
class CustomReportScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة التقرير المخصص
  const CustomReportScreen({
    super.key,
    this.report,
  });

  /// التقرير (اختياري)
  final ReportModel? report;

  @override
  ConsumerState<CustomReportScreen> createState() => _CustomReportScreenState();
}

class _CustomReportScreenState extends ConsumerState<CustomReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  ReportModel? _report;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  ReportParameters _parameters = const ReportParameters();

  // خيارات التقرير المخصص
  ReportType _selectedReportType = ReportType.sales;
  ReportPeriod _selectedReportPeriod = ReportPeriod.monthly;
  String? _selectedGroupBy;
  String? _selectedSortBy;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _report = widget.report;

    if (_report != null) {
      _titleController.text = _report!.title;
      _descriptionController.text = _report!.description ?? '';
      _parameters = ReportParameters.fromMap(_report!.parameters);
      _selectedReportType = _report!.type;
      _selectedReportPeriod = _report!.period;
      _selectedGroupBy = _parameters.groupBy;
      _selectedSortBy = _parameters.sortBy;
    } else {
      _titleController.text =
          'تقرير مخصص ${DateFormat('yyyy-MM-dd').format(DateTime.now())}';
    }

    if (_report == null) {
      _generateReport();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  // توليد التقرير
  Future<void> _generateReport() async {
    if (_titleController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال عنوان التقرير')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reportService = ref.read(reportServiceProvider);

      // تحديث معلمات التقرير
      final updatedParameters = ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: _parameters.categories,
        products: _parameters.products,
        customers: _parameters.customers,
        suppliers: _parameters.suppliers,
        includeVat: _parameters.includeVat,
        includeDiscounts: _parameters.includeDiscounts,
        groupBy: _selectedGroupBy,
        sortBy: _selectedSortBy,
        limit: _parameters.limit,
      );

      if (_report == null) {
        // إنشاء تقرير جديد
        _report = await reportService.createReport(
          title: _titleController.text,
          type: _selectedReportType,
          period: _selectedReportPeriod,
          description: _descriptionController.text,
          parameters: updatedParameters.toMap(),
        );
      } else {
        // تحديث التقرير الحالي
        // في الإصدار الحقيقي، يجب إضافة وظيفة لتحديث التقرير
      }

      setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تصدير التقرير
  Future<void> _exportReport(ExportFormat format) async {
    if (_report == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إنشاء التقرير أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final exportService = ref.read(exportServiceProvider);
      final filePath = await exportService.exportReport(
        report: _report!,
        format: format,
      );

      if (filePath != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم تصدير التقرير إلى: $filePath')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل في تصدير التقرير')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // مشاركة التقرير
  Future<void> _shareReport() async {
    if (_report == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إنشاء التقرير أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final exportService = ref.read(exportServiceProvider);
      await exportService.shareReport(
        report: _report!,
        format: ExportFormat.pdf,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تحديث معلمات التقرير
  void _updateParameters(ReportParameters parameters) {
    setState(() {
      _parameters = parameters;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير مخصص'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateReport,
            tooltip: 'تحديث التقرير',
          ),
          PopupMenuButton<ExportFormat>(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير التقرير',
            onSelected: _exportReport,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: ExportFormat.pdf,
                child: Text('تصدير كـ PDF'),
              ),
              const PopupMenuItem(
                value: ExportFormat.excel,
                child: Text('تصدير كـ Excel'),
              ),
              const PopupMenuItem(
                value: ExportFormat.csv,
                child: Text('تصدير كـ CSV'),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareReport,
            tooltip: 'مشاركة التقرير',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'ملخص'),
            Tab(text: 'رسم بياني'),
            Tab(text: 'جدول'),
          ],
        ),
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل التقرير...',
            )
          : Column(
              children: [
                // معلومات التقرير
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      TextField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان التقرير',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف التقرير (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),

                // خيارات التقرير المخصص
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'خيارات التقرير المخصص',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButtonFormField<ReportType>(
                                  decoration: const InputDecoration(
                                    labelText: 'نوع التقرير',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  value: _selectedReportType,
                                  items: ReportType.values.map((type) {
                                    String label;
                                    switch (type) {
                                      case ReportType.sales:
                                        label = 'تقرير المبيعات';
                                        break;
                                      case ReportType.inventory:
                                        label = 'تقرير المخزون';
                                        break;
                                      case ReportType.financial:
                                        label = 'تقرير مالي';
                                        break;
                                      case ReportType.custom:
                                        label = 'تقرير مخصص';
                                        break;
                                    }
                                    return DropdownMenuItem<ReportType>(
                                      value: type,
                                      child: Text(label),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _selectedReportType = value;
                                      });
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: DropdownButtonFormField<ReportPeriod>(
                                  decoration: const InputDecoration(
                                    labelText: 'فترة التقرير',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  value: _selectedReportPeriod,
                                  items: ReportPeriod.values.map((period) {
                                    String label;
                                    switch (period) {
                                      case ReportPeriod.daily:
                                        label = 'يومي';
                                        break;
                                      case ReportPeriod.weekly:
                                        label = 'أسبوعي';
                                        break;
                                      case ReportPeriod.monthly:
                                        label = 'شهري';
                                        break;
                                      case ReportPeriod.quarterly:
                                        label = 'ربع سنوي';
                                        break;
                                      case ReportPeriod.yearly:
                                        label = 'سنوي';
                                        break;
                                      case ReportPeriod.custom:
                                        label = 'مخصص';
                                        break;
                                    }
                                    return DropdownMenuItem<ReportPeriod>(
                                      value: period,
                                      child: Text(label),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _selectedReportPeriod = value;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButtonFormField<String?>(
                                  decoration: const InputDecoration(
                                    labelText: 'تجميع حسب',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  value: _selectedGroupBy,
                                  items: const [
                                    DropdownMenuItem<String?>(
                                      value: null,
                                      child: Text('بدون تجميع'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'category',
                                      child: Text('الفئة'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'product',
                                      child: Text('المنتج'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'date',
                                      child: Text('التاريخ'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'customer',
                                      child: Text('العميل'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedGroupBy = value;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: DropdownButtonFormField<String?>(
                                  decoration: const InputDecoration(
                                    labelText: 'ترتيب حسب',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  value: _selectedSortBy,
                                  items: const [
                                    DropdownMenuItem<String?>(
                                      value: null,
                                      child: Text('بدون ترتيب'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'value_asc',
                                      child: Text('القيمة (تصاعدي)'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'value_desc',
                                      child: Text('القيمة (تنازلي)'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'date_asc',
                                      child: Text('التاريخ (تصاعدي)'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'date_desc',
                                      child: Text('التاريخ (تنازلي)'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'name_asc',
                                      child: Text('الاسم (تصاعدي)'),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: 'name_desc',
                                      child: Text('الاسم (تنازلي)'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedSortBy = value;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // فلتر التقرير
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: ReportFilter(
                    initialParameters: _parameters,
                    onFilterChanged: _updateParameters,
                    availableCategories: const [
                      'إلكترونيات',
                      'ملابس',
                      'أدوات منزلية',
                      'مستلزمات مكتبية'
                    ],
                    availableProducts: const [
                      'هاتف ذكي',
                      'لابتوب',
                      'سماعات',
                      'شاحن',
                      'حافظة هاتف'
                    ],
                    availableCustomers: const [
                      'عميل 1',
                      'عميل 2',
                      'عميل 3',
                      'عميل 4',
                      'عميل 5'
                    ],
                    availableSuppliers: const [
                      'مورد 1',
                      'مورد 2',
                      'مورد 3',
                      'مورد 4',
                      'مورد 5'
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // محتوى التقرير
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // علامة التبويب الأولى: ملخص
                      _buildSummaryTab(),

                      // علامة التبويب الثانية: رسم بياني
                      _buildChartTab(),

                      // علامة التبويب الثالثة: جدول
                      _buildTableTab(),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _generateReport,
        tooltip: 'إنشاء التقرير',
        child: const Icon(Icons.save),
      ),
    );
  }

  // بناء علامة تبويب الملخص
  Widget _buildSummaryTab() {
    if (_report == null) {
      return const Center(
        child: Text('يرجى إنشاء التقرير أولاً'),
      );
    }

    final data = _report!.data;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الملخص
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي العناصر',
                  value: '${data['totalItems']}',
                  icon: Icons.format_list_numbered,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  title: 'القيمة الإجمالية',
                  value: '${data['totalValue']} جنيه',
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'متوسط القيمة',
                  value: '${data['averageValue']} جنيه',
                  icon: Icons.trending_up,
                  color: Colors.purple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  title: 'نوع التقرير',
                  value: _getReportTypeName(_selectedReportType),
                  icon: Icons.category,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // رسم بياني مصغر
          const Text(
            'توزيع القيم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: ReportChart(
              data: _report!.chartData,
              type: ChartType.pie,
            ),
          ),
          const SizedBox(height: 24),

          // جدول مصغر
          const Text(
            'تفاصيل البيانات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: ReportTable(
              data: _report!.tableData,
              columnLabels: const {
                'name': 'الاسم',
                'value': 'القيمة (جنيه)',
                'percentage': 'النسبة المئوية (%)',
              },
              showPagination: false,
            ),
          ),
        ],
      ),
    );
  }

  // بناء علامة تبويب الرسم البياني
  Widget _buildChartTab() {
    if (_report == null) {
      return const Center(
        child: Text('يرجى إنشاء التقرير أولاً'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // أزرار تبديل نوع الرسم البياني
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SegmentedButton<ChartType>(
                segments: const [
                  ButtonSegment<ChartType>(
                    value: ChartType.bar,
                    label: Text('شريطي'),
                    icon: Icon(Icons.bar_chart),
                  ),
                  ButtonSegment<ChartType>(
                    value: ChartType.pie,
                    label: Text('دائري'),
                    icon: Icon(Icons.pie_chart),
                  ),
                ],
                selected: {_selectedChartType},
                onSelectionChanged: (Set<ChartType> newSelection) {
                  setState(() {
                    _selectedChartType = newSelection.first;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الرسم البياني
          Expanded(
            child: ReportChart(
              data: _report!.chartData,
              type: _selectedChartType,
              title: 'توزيع القيم',
              xAxisTitle: 'الفئة',
              yAxisTitle: 'القيمة (جنيه)',
            ),
          ),
        ],
      ),
    );
  }

  // نوع الرسم البياني المحدد
  ChartType _selectedChartType = ChartType.bar;

  // بناء علامة تبويب الجدول
  Widget _buildTableTab() {
    if (_report == null) {
      return const Center(
        child: Text('يرجى إنشاء التقرير أولاً'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ReportTable(
        data: _report!.tableData,
        title: 'بيانات التقرير',
        columnLabels: const {
          'name': 'الاسم',
          'value': 'القيمة (جنيه)',
          'percentage': 'النسبة المئوية (%)',
        },
        sortable: true,
        showActions: true,
      ),
    );
  }

  // بناء بطاقة ملخص
  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.greyTextColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.sales:
        return 'تقرير المبيعات';
      case ReportType.inventory:
        return 'تقرير المخزون';
      case ReportType.financial:
        return 'تقرير مالي';
      case ReportType.custom:
        return 'تقرير مخصص';
    }
  }
}
