import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/user_log_service.dart';

/// مزود لخدمة سجلات المستخدم
final userLogServiceProvider = Provider<UserLogService>((ref) {
  return UserLogService();
});

/// مزود لجلب سجلات المستخدم
final userLogsProvider =
    FutureProvider.family<List<UserLogModel>, UserLogFilter>(
        (ref, filter) async {
  // عرض رسالة تشخيصية (مختصرة)
  debugPrint('جاري تحميل سجلات المستخدم...');

  try {
    // استخدام read بدلاً من watch لتجنب إعادة بناء المزود عند تغيير الخدمة
    final userLogService = ref.read(userLogServiceProvider);

    // استخدام محاولة واحدة فقط مع مهلة أطول
    final logs = await userLogService.getUserLogs(
      actionType: filter.actionType,
      startDate: filter.startDate,
      endDate: filter.endDate,
      userId: filter.userId,
      limit: filter.limit,
    );

    // تقليل رسائل التشخيص
    if (logs.isNotEmpty) {
      debugPrint('تم تحميل ${logs.length} سجل بنجاح');

      // طباعة معلومات تشخيصية عن السجل الأول
      if (logs.isNotEmpty) {
        final firstLog = logs.first;
        debugPrint('معلومات السجل الأول:');
        debugPrint('- المعرف: ${firstLog.id}');
        debugPrint('- نوع النشاط: ${firstLog.actionType}');
        debugPrint('- الوصف: ${firstLog.description}');
        debugPrint('- اسم المستخدم: ${firstLog.userName}');
        debugPrint('- التاريخ: ${firstLog.timestamp}');
      }
    } else {
      debugPrint('لم يتم العثور على سجلات');
    }

    return logs;
  } catch (e) {
    // تقليل رسائل التشخيص
    debugPrint('خطأ في تحميل سجلات المستخدم: $e');

    // طباعة تفاصيل الخطأ
    debugPrint('تفاصيل الخطأ: ${e.toString()}');
    if (e is Error) {
      debugPrint('مكدس الخطأ: ${e.stackTrace}');
    }

    // إرجاع قائمة فارغة في حالة الخطأ
    return [];
  }
}, dependencies: []); // إضافة dependencies فارغة لمنع إعادة البناء التلقائي

/// مزود لجلب سجلات المديونية
final dueLogsProvider = FutureProvider.family<List<UserLogModel>, DueLogFilter>(
    (ref, filter) async {
  final userLogService = ref.read(userLogServiceProvider);

  return await userLogService.getDueRelatedLogs(
    customerPhone: filter.customerPhone,
    startDate: filter.startDate,
    endDate: filter.endDate,
    limit: filter.limit,
  );
}, dependencies: []);

/// مزود لجلب سجلات المبيعات
final salesLogsProvider =
    FutureProvider.family<List<UserLogModel>, SalesLogFilter>(
        (ref, filter) async {
  final userLogService = ref.read(userLogServiceProvider);

  return await userLogService.getSalesRelatedLogs(
    customerPhone: filter.customerPhone,
    invoiceNumber: filter.invoiceNumber,
    startDate: filter.startDate,
    endDate: filter.endDate,
    limit: filter.limit,
  );
}, dependencies: []);

/// مزود لجلب سجلات المشتريات
final purchaseLogsProvider =
    FutureProvider.family<List<UserLogModel>, PurchaseLogFilter>(
        (ref, filter) async {
  final userLogService = ref.read(userLogServiceProvider);

  return await userLogService.getPurchaseRelatedLogs(
    supplierPhone: filter.supplierPhone,
    invoiceNumber: filter.invoiceNumber,
    startDate: filter.startDate,
    endDate: filter.endDate,
    limit: filter.limit,
  );
}, dependencies: []);

/// فلتر سجلات المستخدم
class UserLogFilter {
  final String? actionType;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? userId;
  final int limit;

  UserLogFilter({
    this.actionType,
    this.startDate,
    this.endDate,
    this.userId,
    this.limit = 100,
  });
}

/// فلتر سجلات المديونية
class DueLogFilter {
  final String? customerPhone;
  final DateTime? startDate;
  final DateTime? endDate;
  final int limit;

  DueLogFilter({
    this.customerPhone,
    this.startDate,
    this.endDate,
    this.limit = 100,
  });
}

/// فلتر سجلات المبيعات
class SalesLogFilter {
  final String? customerPhone;
  final String? invoiceNumber;
  final DateTime? startDate;
  final DateTime? endDate;
  final int limit;

  SalesLogFilter({
    this.customerPhone,
    this.invoiceNumber,
    this.startDate,
    this.endDate,
    this.limit = 100,
  });
}

/// فلتر سجلات المشتريات
class PurchaseLogFilter {
  final String? supplierPhone;
  final String? invoiceNumber;
  final DateTime? startDate;
  final DateTime? endDate;
  final int limit;

  PurchaseLogFilter({
    this.supplierPhone,
    this.invoiceNumber,
    this.startDate,
    this.endDate,
    this.limit = 100,
  });
}
