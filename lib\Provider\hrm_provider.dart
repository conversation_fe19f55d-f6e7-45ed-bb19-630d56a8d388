import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/repository/hrm_repo.dart';
import 'package:mobile_pos/models/hrm_models.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/constant.dart';

// Provider for employee list
final employeeListProvider = StreamProvider<List<EmployeeModel>>((ref) {
  final hrmRepo = ref.watch(hrmRepositoryProvider);
  return hrmRepo.getEmployeeList();
});

// Provider for employee repository
final hrmRepositoryProvider = Provider<HRMRepository>((ref) {
  return HRMRepository();
});

// Provider for department list
final departmentListProvider = StreamProvider<List<DepartmentModel>>((ref) {
  final hrmRepo = ref.watch(hrmRepositoryProvider);
  return hrmRepo.getDepartmentList();
});

// Provider for designation list
final designationListProvider = StreamProvider<List<DesignationModel>>((ref) {
  final hrmRepo = ref.watch(hrmRepositoryProvider);
  return hrmRepo.getDesignationList();
});

// Provider for salary list
final salaryListProvider = StreamProvider<List<SalaryModel>>((ref) {
  final hrmRepo = ref.watch(hrmRepositoryProvider);
  return hrmRepo.getSalaryList();
});

// Provider for selected salary
final selectedSalaryProvider = StateProvider<SalaryModel?>((ref) => null);

// Provider for local employee list (from user's own database path)
final localEmployeeListProvider =
    FutureProvider<List<EmployeeModel>>((ref) async {
  try {
    // الحصول على معرف المستخدم الحالي
    final userID = await getUserID();
    debugPrint('معرف المستخدم: $userID'); // للتشخيص

    // استخدام مسار المستخدم الحالي
    final snapshot = await FirebaseDatabase.instance
        .ref()
        .child(userID)
        .child('Employee')
        .get();

    debugPrint('تم الحصول على بيانات الموظفين'); // للتشخيص

    if (!snapshot.exists) {
      debugPrint('لا يوجد موظفين في قاعدة البيانات'); // للتشخيص
      return [];
    }

    final data = snapshot.value as Map<dynamic, dynamic>?;
    if (data == null) return [];

    List<EmployeeModel> employees = [];
    data.forEach((key, value) {
      if (value is Map<dynamic, dynamic>) {
        Map<String, dynamic> employeeMap = {};
        value.forEach((k, v) {
          employeeMap[k.toString()] = v;
        });

        // تحويل البيانات إلى نموذج الموظف
        try {
          employees.add(EmployeeModel(
            id: key.toString(),
            name: employeeMap['name'] ?? '',
            email: employeeMap['email'] ?? '',
            phone: employeeMap['phoneNumber'] ?? '',
            address: employeeMap['address'] ?? '',
            designation: employeeMap['designation'] ?? '',
            department: employeeMap['department'] ?? '',
            salary: (employeeMap['salary'] ?? 0).toDouble(),
            joiningDate: _parseJoiningDate(employeeMap['joiningDate']),
            imageUrl: employeeMap['imageUrl'] ?? '',
            isActive: employeeMap['isActive'] ?? true,
          ));
        } catch (e) {
          debugPrint('خطأ في تحويل بيانات الموظف: $e');
        }
      }
    });

    debugPrint('تم العثور على ${employees.length} موظف'); // للتشخيص
    return employees;
  } catch (e) {
    debugPrint('خطأ في جلب الموظفين: $e');
    return [];
  }
});

// تحليل تاريخ الانضمام بغض النظر عن نوع البيانات
DateTime _parseJoiningDate(dynamic joiningDateValue) {
  try {
    if (joiningDateValue == null) {
      return DateTime.now();
    } else if (joiningDateValue is int) {
      // إذا كان التاريخ مخزنًا كرقم صحيح
      return DateTime.fromMillisecondsSinceEpoch(joiningDateValue);
    } else if (joiningDateValue is String) {
      // إذا كان التاريخ مخزنًا كنص
      // أولاً نحاول تحليله كتاريخ ISO
      try {
        return DateTime.parse(joiningDateValue);
      } catch (e) {
        // إذا فشل، نحاول تحليله كرقم صحيح مخزن كنص
        try {
          return DateTime.fromMillisecondsSinceEpoch(
              int.parse(joiningDateValue));
        } catch (e) {
          debugPrint('فشل تحليل تاريخ الانضمام: $e');
          return DateTime.now();
        }
      }
    } else {
      // نوع غير معروف
      debugPrint(
          'نوع تاريخ الانضمام غير معروف: ${joiningDateValue.runtimeType}');
      return DateTime.now();
    }
  } catch (e) {
    debugPrint('خطأ عام في تحليل تاريخ الانضمام: $e');
    return DateTime.now();
  }
}
