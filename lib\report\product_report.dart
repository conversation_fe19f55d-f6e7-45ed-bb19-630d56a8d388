// // ignore_for_file: prefer_const_constructors, use_key_in_widget_constructors

// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'product_report_module.dart';

// class ProductReportScreen extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     final productReportProvider = Provider.of<ProductReportProvider>(context);

//     return Scaffold(
//       appBar: AppBar(title: Text('تقرير الأصناف')),
//       body: ListView.builder(
//         itemCount: productReportProvider.productReports.length,
//         itemBuilder: (context, index) {
//           final product = productReportProvider.productReports[index];
//           return ListTile(
//             title: Text(product.productName),
//             // يمكنك إضافة المزيد من التفاصيل هنا
//           );
//         },
//       ),
//     );
//   }
// }
