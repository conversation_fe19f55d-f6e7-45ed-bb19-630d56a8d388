import 'package:mobile_pos/models/chat/chat_conversation.dart';

/// نموذج المحادثة في Firebase
class FirebaseChatConversation {
  String? id; // معرف المحادثة في Firebase
  List<String> participants; // قائمة معرفات المشاركين
  String lastMessageText; // نص آخر رسالة
  DateTime lastMessageDate; // تاريخ آخر رسالة
  String lastMessageSenderId; // معرف مرسل آخر رسالة
  String lastMessageType; // نوع آخر رسالة
  bool isGroup; // هل المحادثة مجموعة
  String? groupName; // اسم المجموعة
  String? groupImage; // صورة المجموعة
  String? groupDescription; // وصف المجموعة
  List<String> groupAdmins; // قائمة معرفات مشرفي المجموعة
  List<String> muted; // قائمة معرفات المستخدمين الذين كتموا المحادثة
  List<String> pinned; // قائمة معرفات المستخدمين الذين ثبتوا المحادثة
  Map<String, int> unreadCounts; // عدد الرسائل غير المقروءة لكل مستخدم
  DateTime? createdAt; // تاريخ إنشاء المحادثة

  FirebaseChatConversation({
    this.id,
    this.participants = const [],
    this.lastMessageText = '',
    DateTime? lastMessageDate,
    this.lastMessageSenderId = '',
    this.lastMessageType = 'text',
    this.isGroup = false,
    this.groupName,
    this.groupImage,
    this.groupDescription,
    this.groupAdmins = const [],
    this.muted = const [],
    this.pinned = const [],
    this.unreadCounts = const {},
    this.createdAt,
  }) : lastMessageDate = lastMessageDate ?? DateTime.now();

  // إنشاء من Map (Firebase)
  factory FirebaseChatConversation.fromMap(
      Map<String, dynamic> map, String id) {
    return FirebaseChatConversation(
      id: id,
      participants: List<String>.from(map['participants'] ?? []),
      lastMessageText: map['lastMessageText'] ?? '',
      lastMessageDate: map['lastMessageDate'] != null
          ? DateTime.parse(map['lastMessageDate'])
          : DateTime.now(),
      lastMessageSenderId: map['lastMessageSenderId'] ?? '',
      lastMessageType: map['lastMessageType'] ?? 'text',
      isGroup: map['isGroup'] ?? false,
      groupName: map['groupName'],
      groupImage: map['groupImage'],
      groupDescription: map['groupDescription'],
      groupAdmins: List<String>.from(map['groupAdmins'] ?? []),
      muted: List<String>.from(map['muted'] ?? []),
      pinned: List<String>.from(map['pinned'] ?? []),
      unreadCounts: (map['unreadCounts'] as Map<dynamic, dynamic>?)?.map(
            (key, value) => MapEntry(key.toString(), value as int),
          ) ??
          {},
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
    );
  }

  // تحويل إلى Map (Firebase)
  Map<String, dynamic> toMap() {
    return {
      'participants': participants,
      'lastMessageText': lastMessageText,
      'lastMessageDate': lastMessageDate.toIso8601String(),
      'lastMessageSenderId': lastMessageSenderId,
      'lastMessageType': lastMessageType,
      'isGroup': isGroup,
      'groupName': groupName,
      'groupImage': groupImage,
      'groupDescription': groupDescription,
      'groupAdmins': groupAdmins,
      'muted': muted,
      'pinned': pinned,
      'unreadCounts': unreadCounts,
      'createdAt':
          createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
    };
  }

  // الحصول على عدد الرسائل غير المقروءة لمستخدم معين
  int getUnreadCountForUser(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  // تعيين عدد الرسائل غير المقروءة لمستخدم معين
  void setUnreadCountForUser(String userId, int count) {
    unreadCounts[userId] = count;
  }

  // تحويل من ChatConversation إلى FirebaseChatConversation
  static FirebaseChatConversation fromChatConversation(
      ChatConversation conversation) {
    // إنشاء Map لعدد الرسائل غير المقروءة
    final Map<String, int> unreadCounts = {};

    // استخراج عدد الرسائل غير المقروءة من الحقول المخصصة
    // نستخدم unreadCounts مباشرة من ChatConversation
    if (conversation.unreadCounts.isNotEmpty) {
      unreadCounts.addAll(conversation.unreadCounts);
    }

    return FirebaseChatConversation(
      id: conversation.objectId,
      participants: conversation.participants,
      lastMessageText: conversation.lastMessageText,
      lastMessageDate: conversation.lastMessageDate,
      lastMessageSenderId: conversation.lastMessageSenderId,
      lastMessageType: conversation.lastMessageType,
      isGroup: conversation.isGroup,
      groupName: conversation.groupName,
      groupImage: conversation.groupImage?.url,
      groupDescription: conversation.groupDescription,
      groupAdmins: conversation.groupAdmins,
      muted: conversation.muted,
      pinned: conversation.pinned,
      unreadCounts: unreadCounts,
      createdAt: conversation.createdAt,
    );
  }

  // تحويل إلى ChatConversation
  ChatConversation toChatConversation() {
    final chatConversation = ChatConversation(
      objectId: id,
      participants: participants,
      lastMessageText: lastMessageText,
      lastMessageDate: lastMessageDate,
      lastMessageSenderId: lastMessageSenderId,
      isGroup: isGroup,
      groupName: groupName,
      groupImage: groupImage != null ? GroupImage(url: groupImage) : null,
      groupDescription: groupDescription,
      groupAdmins: groupAdmins,
      muted: muted,
      pinned: pinned,
      lastMessageType: lastMessageType,
      createdAt: createdAt,
    );

    // نسخ عدد الرسائل غير المقروءة
    unreadCounts.forEach((userId, count) {
      chatConversation.setUnreadCountForUser(userId, count);
    });

    return chatConversation;
  }
}
