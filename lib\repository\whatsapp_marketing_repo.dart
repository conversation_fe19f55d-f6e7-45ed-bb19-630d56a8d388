import 'package:firebase_database/firebase_database.dart';

import 'package:mobile_pos/models/whatsapp_marketing_model.dart';

class WhatsappMarketingRepository {
  final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // الحصول على قائمة الحملات
  Stream<List<WhatsappCampaignModel>> getCampaignList() {
    return _database.child('WhatsappMarketing/Campaigns').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<WhatsappCampaignModel> campaigns = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> campaignMap = {};
          value.forEach((k, v) {
            campaignMap[k.toString()] = v;
          });
          campaigns
              .add(WhatsappCampaignModel.fromMap(campaignMap, key.toString()));
        }
      });
      return campaigns;
    });
  }

  // إضافة حملة جديدة
  Future<void> addCampaign(WhatsappCampaignModel campaign) async {
    try {
      final newCampaignRef =
          _database.child('WhatsappMarketing/Campaigns').push();
      await newCampaignRef.set(campaign.toMap());
    } catch (e) {
      // خطأ في إضافة حملة
      rethrow;
    }
  }

  // تحديث حملة
  Future<void> updateCampaign(WhatsappCampaignModel campaign) async {
    try {
      await _database
          .child('WhatsappMarketing/Campaigns/${campaign.id}')
          .update(campaign.toMap());
    } catch (e) {
      // خطأ في تحديث حملة
      rethrow;
    }
  }

  // حذف حملة
  Future<void> deleteCampaign(String campaignId) async {
    try {
      await _database.child('WhatsappMarketing/Campaigns/$campaignId').remove();
    } catch (e) {
      // خطأ في حذف حملة
      rethrow;
    }
  }

  // تغيير حالة حملة
  Future<void> changeCampaignStatus(String campaignId, String status) async {
    try {
      await _database
          .child('WhatsappMarketing/Campaigns/$campaignId')
          .update({'status': status});
    } catch (e) {
      // خطأ في تغيير حالة حملة
      rethrow;
    }
  }

  // الحصول على قائمة المجموعات
  Stream<List<WhatsappGroupModel>> getGroupList() {
    return _database.child('WhatsappMarketing/Groups').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<WhatsappGroupModel> groups = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> groupMap = {};
          value.forEach((k, v) {
            groupMap[k.toString()] = v;
          });
          groups.add(WhatsappGroupModel.fromMap(groupMap, key.toString()));
        }
      });
      return groups;
    });
  }

  // إضافة مجموعة جديدة
  Future<void> addGroup(WhatsappGroupModel group) async {
    try {
      final newGroupRef = _database.child('WhatsappMarketing/Groups').push();
      await newGroupRef.set(group.toMap());
    } catch (e) {
      // خطأ في إضافة مجموعة
      rethrow;
    }
  }

  // تحديث مجموعة
  Future<void> updateGroup(WhatsappGroupModel group) async {
    try {
      await _database
          .child('WhatsappMarketing/Groups/${group.id}')
          .update(group.toMap());
    } catch (e) {
      // خطأ في تحديث مجموعة
      rethrow;
    }
  }

  // حذف مجموعة
  Future<void> deleteGroup(String groupId) async {
    try {
      await _database.child('WhatsappMarketing/Groups/$groupId').remove();
    } catch (e) {
      // خطأ في حذف مجموعة
      rethrow;
    }
  }

  // الحصول على قائمة جهات الاتصال
  Stream<List<WhatsappContactModel>> getContactList() {
    return _database.child('WhatsappMarketing/Contacts').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<WhatsappContactModel> contacts = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> contactMap = {};
          value.forEach((k, v) {
            contactMap[k.toString()] = v;
          });
          contactMap['id'] = key.toString();
          contacts.add(WhatsappContactModel.fromMap(contactMap));
        }
      });
      return contacts;
    });
  }

  // إضافة جهة اتصال جديدة
  Future<void> addContact(WhatsappContactModel contact) async {
    try {
      final newContactRef =
          _database.child('WhatsappMarketing/Contacts').push();
      final contactMap = contact.toMap();
      contactMap.remove('id'); // حذف المعرف لأنه سيتم إنشاؤه تلقائيًا
      await newContactRef.set(contactMap);
    } catch (e) {
      // خطأ في إضافة جهة اتصال
      rethrow;
    }
  }

  // تحديث جهة اتصال
  Future<void> updateContact(WhatsappContactModel contact) async {
    try {
      final contactMap = contact.toMap();
      contactMap.remove('id'); // حذف المعرف لأنه لا يتم تخزينه في البيانات
      await _database
          .child('WhatsappMarketing/Contacts/${contact.id}')
          .update(contactMap);
    } catch (e) {
      // خطأ في تحديث جهة اتصال
      rethrow;
    }
  }

  // حذف جهة اتصال
  Future<void> deleteContact(String contactId) async {
    try {
      await _database.child('WhatsappMarketing/Contacts/$contactId').remove();
    } catch (e) {
      // خطأ في حذف جهة اتصال
      rethrow;
    }
  }

  // إرسال رسالة واتساب (محاكاة)
  Future<Map<String, dynamic>> sendWhatsappMessage(
      WhatsappCampaignModel campaign) async {
    try {
      // هذه مجرد محاكاة لإرسال رسائل واتساب
      // في التطبيق الحقيقي، ستحتاج إلى استخدام واجهة برمجة تطبيقات واتساب أو خدمة طرف ثالث

      // تحديث حالة الحملة
      await _database
          .child('WhatsappMarketing/Campaigns/${campaign.id}')
          .update({
        'status': 'sent',
      });

      // تحديث حالة المستلمين
      List<WhatsappRecipientModel> updatedRecipients = [];
      for (var recipient in campaign.recipients) {
        // محاكاة نجاح/فشل الإرسال
        final bool isSuccess =
            DateTime.now().millisecondsSinceEpoch % 10 != 0; // 90% نجاح
        final updatedRecipient = recipient.copyWith(
          status: isSuccess ? 'sent' : 'failed',
          sentAt: DateTime.now(),
        );
        updatedRecipients.add(updatedRecipient);
      }

      // تحديث إحصائيات الحملة
      final int sent =
          updatedRecipients.where((r) => r.status == 'sent').length;
      final int failed =
          updatedRecipients.where((r) => r.status == 'failed').length;

      final Map<String, dynamic> statistics = {
        'sent': sent,
        'delivered': 0, // سيتم تحديثه لاحقًا
        'read': 0, // سيتم تحديثه لاحقًا
        'failed': failed,
      };

      await _database
          .child('WhatsappMarketing/Campaigns/${campaign.id}')
          .update({
        'recipients': updatedRecipients.map((r) => r.toMap()).toList(),
        'statistics': statistics,
      });

      return {
        'success': true,
        'sent': sent,
        'failed': failed,
        'total': updatedRecipients.length,
      };
    } catch (e) {
      // خطأ في إرسال رسائل واتساب
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
