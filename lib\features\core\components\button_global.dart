// بسم الله الرحمن الرحيم
// زر عام - يستخدم في جميع أنحاء التطبيق

import 'package:flutter/material.dart';

/// زر عام مع أيقونة
class ButtonGlobal extends StatelessWidget {
  const ButtonGlobal({
    super.key,
    required this.buttontext,
    required this.buttonDecoration,
    required this.onPressed,
    required this.buttonTextColor,
    this.icon,
  });

  final String buttontext;
  final BoxDecoration buttonDecoration;
  final VoidCallback onPressed;
  final Color buttonTextColor;
  final IconData? icon;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: 15.0, bottom: 15.0),
        decoration: buttonDecoration,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: buttonTextColor,
              ),
              const SizedBox(width: 10),
            ],
            Text(
              buttontext,
              style: TextStyle(
                color: buttonTextColor,
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// زر عام بدون أيقونة
class ButtonGlobalWithoutIcon extends StatelessWidget {
  const ButtonGlobalWithoutIcon({
    super.key,
    required this.buttontext,
    required this.buttonDecoration,
    required this.onPressed,
    required this.buttonTextColor,
  });

  final String buttontext;
  final BoxDecoration buttonDecoration;
  final VoidCallback onPressed;
  final Color buttonTextColor;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: 15.0, bottom: 15.0),
        decoration: buttonDecoration,
        child: Text(
          buttontext,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: buttonTextColor,
            fontWeight: FontWeight.bold,
            fontSize: 16.0,
          ),
        ),
      ),
    );
  }
}
