// بسم الله الرحمن الرحيم
// نماذج البيانات للدردشة - AmrDevPOS

class ChatMessage {
  final String id;
  final String text;
  final bool isMe;
  final DateTime timestamp;
  final bool isRead;
  final MessageType type;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.id,
    required this.text,
    required this.isMe,
    required this.timestamp,
    this.isRead = false,
    this.type = MessageType.text,
    this.metadata,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      text: json['text'],
      isMe: json['isMe'],
      timestamp: DateTime.parse(json['timestamp']),
      isRead: json['isRead'] ?? false,
      type: MessageType.values[json['type'] ?? 0],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'isMe': isMe,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'type': type.index,
      'metadata': metadata,
    };
  }

  ChatMessage copyWith({
    String? id,
    String? text,
    bool? isMe,
    DateTime? timestamp,
    bool? isRead,
    MessageType? type,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      text: text ?? this.text,
      isMe: isMe ?? this.isMe,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }
}

enum MessageType {
  text,
  image,
  file,
  voice,
  system,
  typing,
}

class ChatUser {
  final String id;
  final String name;
  final String? email;
  final String? phone;
  final String? avatar;
  final bool isOnline;
  final DateTime? lastSeen;
  final UserRole role;

  ChatUser({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.avatar,
    this.isOnline = false,
    this.lastSeen,
    this.role = UserRole.user,
  });

  factory ChatUser.fromJson(Map<String, dynamic> json) {
    return ChatUser(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      avatar: json['avatar'],
      isOnline: json['isOnline'] ?? false,
      lastSeen: json['lastSeen'] != null ? DateTime.parse(json['lastSeen']) : null,
      role: UserRole.values[json['role'] ?? 0],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'isOnline': isOnline,
      'lastSeen': lastSeen?.toIso8601String(),
      'role': role.index,
    };
  }
}

enum UserRole {
  user,
  admin,
  moderator,
  ai,
}

class ChatConversation {
  final String id;
  final String title;
  final List<String> participants;
  final ChatMessage? lastMessage;
  final int unreadCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ConversationType type;

  ChatConversation({
    required this.id,
    required this.title,
    required this.participants,
    this.lastMessage,
    this.unreadCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.type = ConversationType.direct,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'],
      title: json['title'],
      participants: List<String>.from(json['participants']),
      lastMessage: json['lastMessage'] != null 
          ? ChatMessage.fromJson(json['lastMessage']) 
          : null,
      unreadCount: json['unreadCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      type: ConversationType.values[json['type'] ?? 0],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'participants': participants,
      'lastMessage': lastMessage?.toJson(),
      'unreadCount': unreadCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'type': type.index,
    };
  }
}

enum ConversationType {
  direct,
  group,
  ai,
  support,
}

class AIModel {
  final String id;
  final String name;
  final String description;
  final bool isAvailable;
  final Map<String, dynamic> capabilities;

  AIModel({
    required this.id,
    required this.name,
    required this.description,
    this.isAvailable = true,
    this.capabilities = const {},
  });

  factory AIModel.fromJson(Map<String, dynamic> json) {
    return AIModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      isAvailable: json['isAvailable'] ?? true,
      capabilities: json['capabilities'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isAvailable': isAvailable,
      'capabilities': capabilities,
    };
  }
}

class BusinessDataContext {
  final Map<String, dynamic> salesData;
  final Map<String, dynamic> purchasesData;
  final Map<String, dynamic> inventoryData;
  final Map<String, dynamic> customersData;
  final Map<String, dynamic> suppliersData;
  final Map<String, dynamic> financialData;
  final DateTime lastUpdated;

  BusinessDataContext({
    this.salesData = const {},
    this.purchasesData = const {},
    this.inventoryData = const {},
    this.customersData = const {},
    this.suppliersData = const {},
    this.financialData = const {},
    required this.lastUpdated,
  });

  factory BusinessDataContext.fromJson(Map<String, dynamic> json) {
    return BusinessDataContext(
      salesData: json['salesData'] ?? {},
      purchasesData: json['purchasesData'] ?? {},
      inventoryData: json['inventoryData'] ?? {},
      customersData: json['customersData'] ?? {},
      suppliersData: json['suppliersData'] ?? {},
      financialData: json['financialData'] ?? {},
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'salesData': salesData,
      'purchasesData': purchasesData,
      'inventoryData': inventoryData,
      'customersData': customersData,
      'suppliersData': suppliersData,
      'financialData': financialData,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  String toContextString() {
    String context = 'البيانات التجارية الحالية:\n';
    
    if (salesData.isNotEmpty) {
      context += 'المبيعات: ${salesData.toString()}\n';
    }
    
    if (purchasesData.isNotEmpty) {
      context += 'المشتريات: ${purchasesData.toString()}\n';
    }
    
    if (inventoryData.isNotEmpty) {
      context += 'المخزون: ${inventoryData.toString()}\n';
    }
    
    if (customersData.isNotEmpty) {
      context += 'العملاء: ${customersData.toString()}\n';
    }
    
    if (suppliersData.isNotEmpty) {
      context += 'الموردين: ${suppliersData.toString()}\n';
    }
    
    if (financialData.isNotEmpty) {
      context += 'البيانات المالية: ${financialData.toString()}\n';
    }
    
    context += 'آخر تحديث: ${lastUpdated.toString()}\n';
    
    return context;
  }
}
