// بسم الله الرحمن الرحيم
// خدمة الباركود - مسؤولة عن مسح وإنشاء الباركود

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:mobile_scanner/mobile_scanner.dart' as scanner; // غير مستخدم حاليًا
import 'package:barcode/barcode.dart';
import 'package:image/image.dart' as img;

import '../models/product_model.dart';
import 'inventory_service.dart';

/// خدمة الباركود
class BarcodeService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة الباركود
  static final BarcodeService _instance = BarcodeService._internal();
  factory BarcodeService() => _instance;
  BarcodeService._internal();

  // خدمة المخزون
  final InventoryService _inventoryService = InventoryService();

  // تدفق نتائج المسح
  final _scanResultsController = StreamController<String>.broadcast();

  /// الحصول على تدفق نتائج المسح
  Stream<String> get scanResults => _scanResultsController.stream;

  /// معالجة نتيجة المسح
  void processScanResult(String barcode) {
    _scanResultsController.add(barcode);
  }

  /// البحث عن منتج بالباركود
  Future<ProductModel?> findProductByBarcode(String barcode) async {
    try {
      return await _inventoryService.getProductByBarcode(barcode);
    } catch (e) {
      debugPrint('خطأ في البحث عن منتج بالباركود: $e');
      return null;
    }
  }

  /// إنشاء باركود
  Future<String?> generateBarcode({
    required String data,
    required BarcodeType type,
    double width = 200,
    double height = 80,
    bool withText = true,
  }) async {
    try {
      // اختيار نوع الباركود
      final Barcode bc = _getBarcodeType(type);

      // إنشاء الباركود
      final svg = bc.toSvg(
        data,
        width: width,
        height: height,
        fontHeight: withText ? 14 : 0,
      );

      // حفظ الباركود كملف SVG
      final directory = await getTemporaryDirectory();
      final filePath =
          '${directory.path}/barcode_${DateTime.now().millisecondsSinceEpoch}.svg';
      final file = File(filePath);
      await file.writeAsString(svg);

      return filePath;
    } catch (e) {
      debugPrint('خطأ في إنشاء الباركود: $e');
      return null;
    }
  }

  /// إنشاء باركود كصورة PNG
  Future<String?> generateBarcodePng({
    required String data,
    required BarcodeType type,
    double width = 200,
    double height = 80,
    bool withText = true,
  }) async {
    try {
      // اختيار نوع الباركود
      final Barcode bc = _getBarcodeType(type);

      // إنشاء الباركود
      // إنشاء الباركود
      // نحن لا نستخدم متغير svg هنا، لكن في التطبيق الحقيقي سنستخدمه لتحويل SVG إلى PNG
      bc.toSvg(
        data,
        width: width,
        height: height,
        fontHeight: withText ? 14 : 0,
      );

      // تحويل SVG إلى PNG (في الإصدار الحقيقي، يجب استخدام مكتبة لتحويل SVG إلى PNG)
      // هنا نستخدم طريقة بسيطة لإنشاء صورة PNG
      final image = img.Image(width: width.toInt(), height: height.toInt());
      // تلوين الصورة باللون الأبيض
      img.fill(image, color: img.ColorRgb8(255, 255, 255));

      // حفظ الصورة كملف PNG
      final directory = await getTemporaryDirectory();
      final filePath =
          '${directory.path}/barcode_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(filePath);
      await file.writeAsBytes(img.encodePng(image));

      return filePath;
    } catch (e) {
      debugPrint('خطأ في إنشاء الباركود كصورة PNG: $e');
      return null;
    }
  }

  /// التحقق من صحة الباركود
  bool isValidBarcode(String barcode, BarcodeType type) {
    try {
      final bc = _getBarcodeType(type);
      return bc.isValid(barcode);
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الباركود: $e');
      return false;
    }
  }

  /// إنشاء باركود عشوائي
  String generateRandomBarcode(BarcodeType type) {
    switch (type) {
      case BarcodeType.ean13:
        // إنشاء EAN-13 عشوائي
        const prefix = '200'; // رمز الدولة (مصر)
        final random = List.generate(
            9,
            (index) =>
                (DateTime.now().millisecondsSinceEpoch % 10).toString()).join();
        final barcode = '$prefix$random';
        final checkDigit = _calculateEAN13CheckDigit(barcode);
        return '$barcode$checkDigit';
      case BarcodeType.code128:
        // إنشاء Code 128 عشوائي
        final random = DateTime.now().millisecondsSinceEpoch.toString();
        return 'P$random';
      case BarcodeType.qrCode:
        // إنشاء QR Code عشوائي
        final random = DateTime.now().millisecondsSinceEpoch.toString();
        return 'QR$random';
      default:
        // إنشاء باركود عشوائي
        final random = DateTime.now().millisecondsSinceEpoch.toString();
        return 'BC$random';
    }
  }

  /// حساب رقم التحقق لباركود EAN-13
  String _calculateEAN13CheckDigit(String barcode) {
    if (barcode.length != 12) {
      throw Exception('يجب أن يكون طول الباركود 12 رقمًا');
    }

    int sum = 0;
    for (int i = 0; i < 12; i++) {
      final digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }

    final checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit.toString();
  }

  /// الحصول على نوع الباركود
  Barcode _getBarcodeType(BarcodeType type) {
    switch (type) {
      case BarcodeType.ean13:
        return Barcode.ean13();
      case BarcodeType.code128:
        return Barcode.code128();
      case BarcodeType.qrCode:
        return Barcode.qrCode();
      case BarcodeType.code39:
        return Barcode.code39();
      case BarcodeType.upcA:
        return Barcode.upcA();
      case BarcodeType.isbn:
        return Barcode.isbn();
    }
  }

  /// إغلاق خدمة الباركود
  void dispose() {
    _scanResultsController.close();
  }
}

/// نوع الباركود
enum BarcodeType {
  /// EAN-13
  ean13,

  /// Code 128
  code128,

  /// QR Code
  qrCode,

  /// Code 39
  code39,

  /// UPC-A
  upcA,

  /// ISBN
  isbn,
}

/// مزود خدمة الباركود
final barcodeServiceProvider = Provider<BarcodeService>((ref) {
  return BarcodeService();
});

/// مزود نتائج المسح
final barcodeScanResultsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(barcodeServiceProvider);
  return service.scanResults;
});

/// مزود المنتج الذي تم مسحه
final scannedProductProvider =
    FutureProvider.family<ProductModel?, String>((ref, barcode) async {
  final service = ref.watch(barcodeServiceProvider);
  return await service.findProductByBarcode(barcode);
});
