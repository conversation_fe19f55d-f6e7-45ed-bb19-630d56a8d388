// بسم الله الرحمن الرحيم
// خدمة Gemini API - AmrDevPOS

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class GeminiService {
  static const String _apiKey = 'AIzaSyC48Cc1QaQ-YCkix4k-ERMQjCy3ahlhfqU';
  static const String _baseUrl =
      'https://generativelanguage.googleapis.com/v1beta/models';

  // النماذج المتاحة
  static const Map<String, String> availableModels = {
    'gemini-1.5-flash': 'AmrDev-v1.5 - سريع ومجاني',
    'gemini-1.5-pro': 'AmrDev-v01 - الأكثر دقة (مجاني)',
    'gemini-2.0-flash-exp': 'AmrDev-v2 - تجريبي متطور',
  };

  static String _selectedModel = 'gemini-1.5-flash';

  // تغيير النموذج المستخدم
  static void setModel(String model) {
    if (availableModels.containsKey(model)) {
      _selectedModel = model;
    }
  }

  static String get currentModel => _selectedModel;

  // إرسال رسالة للذكاء الاصطناعي
  static Future<String> sendMessage(String message, {String? context}) async {
    try {
      final url = '$_baseUrl/$_selectedModel:generateContent?key=$_apiKey';

      // إعداد السياق للنظام التجاري
      final systemContext = _buildSystemContext(context);
      final fullMessage = '$systemContext\n\nسؤال المستخدم: $message';

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'contents': [
            {
              'parts': [
                {
                  'text': fullMessage,
                }
              ]
            }
          ],
          'generationConfig': {
            'temperature': 0.8,
            'topK': 64,
            'topP': 0.95,
            'maxOutputTokens': 2048,
            'candidateCount': 1,
          },
          'safetySettings': [
            {
              'category': 'HARM_CATEGORY_HARASSMENT',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              'category': 'HARM_CATEGORY_HATE_SPEECH',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          final content = data['candidates'][0]['content'];
          if (content != null &&
              content['parts'] != null &&
              content['parts'].isNotEmpty) {
            return content['parts'][0]['text'] ??
                'عذراً، لم أتمكن من فهم طلبك.';
          }
        }

        return 'عذراً، لم أتمكن من معالجة طلبك في الوقت الحالي.';
      } else {
        debugPrint('خطأ في API: ${response.statusCode} - ${response.body}');
        return 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');
      return 'عذراً، حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
    }
  }

  // بناء سياق النظام التجاري
  static String _buildSystemContext(String? additionalContext) {
    String baseContext = '''
أنت مساعد ذكي متطور لنظام AmrDevPOS التجاري باستخدام Gemini 2.0 Flash. أنت خبير في:

1. إنشاء الفواتير والمبيعات السريعة
2. البحث الذكي في العملاء والموردين والمنتجات
3. إدارة المخزون والتحكم في الكميات
4. التحليل المالي والتقارير الفورية
5. معالجة الأوامر الصوتية بذكاء
6. التمييز التلقائي بين العملاء والموردين

قدراتك المتقدمة:
- فهم الأوامر الصوتية العربية بدقة عالية
- التعرف على الأسماء العربية والتعامل مع حرف "ل" التعريفي
- البحث الذكي في قواعد البيانات الحقيقية
- إنشاء الفواتير تلقائياً بناءً على الأوامر الصوتية
- تحليل البيانات وتقديم رؤى فورية

قواعد مهمة:
- أجب باللغة العربية فقط
- كن سريعاً ودقيقاً في الاستجابة
- استخدم البيانات الحقيقية من قاعدة البيانات فقط
- لا تخترع أسماء أو بيانات غير موجودة
- اطلب التأكيد قبل إنشاء أي فاتورة
- قدم معلومات واضحة ومفيدة

البيانات المتاحة لك:
- قاعدة بيانات العملاء الحقيقية
- قاعدة بيانات الموردين الحقيقية
- معلومات المنتجات والمخزون
- سجلات المبيعات والمشتريات
- التقارير المالية الفورية
''';

    if (additionalContext != null && additionalContext.isNotEmpty) {
      baseContext += '\n\nمعلومات إضافية:\n$additionalContext';
    }

    return baseContext;
  }

  // تحليل البيانات التجارية
  static Future<String> analyzeBusinessData(Map<String, dynamic> data) async {
    final context = _buildBusinessDataContext(data);
    return await sendMessage(
      'قم بتحليل البيانات التجارية المرفقة وقدم تقريراً مفصلاً مع التوصيات',
      context: context,
    );
  }

  // بناء سياق البيانات التجارية
  static String _buildBusinessDataContext(Map<String, dynamic> data) {
    String context = 'البيانات التجارية الحالية:\n';

    if (data.containsKey('sales')) {
      context += 'إجمالي المبيعات: ${data['sales']}\n';
    }

    if (data.containsKey('purchases')) {
      context += 'إجمالي المشتريات: ${data['purchases']}\n';
    }

    if (data.containsKey('profit')) {
      context += 'صافي الربح: ${data['profit']}\n';
    }

    if (data.containsKey('customers')) {
      context += 'عدد العملاء: ${data['customers']}\n';
    }

    if (data.containsKey('products')) {
      context += 'عدد المنتجات: ${data['products']}\n';
    }

    if (data.containsKey('inventory_value')) {
      context += 'قيمة المخزون: ${data['inventory_value']}\n';
    }

    return context;
  }

  // الحصول على اقتراحات تحسين الأعمال
  static Future<String> getBusinessSuggestions(String businessType) async {
    return await sendMessage(
      'قدم اقتراحات لتحسين الأعمال التجارية من نوع: $businessType',
    );
  }

  // تحليل اتجاهات المبيعات
  static Future<String> analyzeSalesTrends(
      List<Map<String, dynamic>> salesData) async {
    String context = 'بيانات المبيعات:\n';
    for (var sale in salesData) {
      context += 'التاريخ: ${sale['date']}, المبلغ: ${sale['amount']}\n';
    }

    return await sendMessage(
      'حلل اتجاهات المبيعات وقدم توقعات للفترة القادمة',
      context: context,
    );
  }

  // تحليل أداء المنتجات
  static Future<String> analyzeProductPerformance(
      List<Map<String, dynamic>> products) async {
    String context = 'أداء المنتجات:\n';
    for (var product in products) {
      context +=
          'المنتج: ${product['name']}, المبيعات: ${product['sales']}, المخزون: ${product['stock']}\n';
    }

    return await sendMessage(
      'حلل أداء المنتجات وحدد الأكثر ربحية والأقل أداءً',
      context: context,
    );
  }

  // تحليل سلوك العملاء
  static Future<String> analyzeCustomerBehavior(
      List<Map<String, dynamic>> customers) async {
    String context = 'بيانات العملاء:\n';
    for (var customer in customers) {
      context +=
          'العميل: ${customer['name']}, إجمالي المشتريات: ${customer['total_purchases']}, آخر زيارة: ${customer['last_visit']}\n';
    }

    return await sendMessage(
      'حلل سلوك العملاء وقدم استراتيجيات لزيادة الولاء والمبيعات',
      context: context,
    );
  }

  // دالة خاصة للمساعد الذكي السريع
  static Future<String> processQuickAICommand(String command,
      {String? context}) async {
    try {
      final url = '$_baseUrl/$_selectedModel:generateContent?key=$_apiKey';

      // سياق مخصص للمساعد الذكي السريع
      final quickAIContext = '''
أنت مساعد ذكي سريع لنظام AmrDevPOS. مهمتك:

1. فهم الأوامر الصوتية العربية بدقة
2. التعرف على أسماء العملاء والموردين
3. إنشاء الفواتير بسرعة
4. البحث في المنتجات والمخزون
5. تقديم إجابات سريعة ومفيدة

قواعد مهمة:
- أجب بإيجاز ووضوح
- استخدم البيانات الحقيقية فقط
- لا تخترع أسماء أو معلومات
- كن دقيقاً في التعرف على الأسماء العربية
- تعامل مع حرف "ل" التعريفي بذكاء

${context ?? ''}
''';

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'contents': [
            {
              'parts': [
                {
                  'text': '$quickAIContext\n\nالأمر: $command',
                }
              ]
            }
          ],
          'generationConfig': {
            'temperature': 0.9,
            'topK': 64,
            'topP': 0.95,
            'maxOutputTokens': 512, // إجابات أقصر للسرعة
            'candidateCount': 1,
          },
          'safetySettings': [
            {
              'category': 'HARM_CATEGORY_HARASSMENT',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              'category': 'HARM_CATEGORY_HATE_SPEECH',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
              'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          final content = data['candidates'][0]['content'];
          if (content != null &&
              content['parts'] != null &&
              content['parts'].isNotEmpty) {
            return content['parts'][0]['text'] ??
                'عذراً، لم أتمكن من فهم طلبك.';
          }
        }

        return 'عذراً، لم أتمكن من معالجة طلبك في الوقت الحالي.';
      } else {
        debugPrint(
            'خطأ في Quick AI API: ${response.statusCode} - ${response.body}');
        return 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
      }
    } catch (e) {
      debugPrint('خطأ في Quick AI: $e');
      return 'عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }
  }

  // التحقق من حالة API
  static Future<bool> checkApiStatus() async {
    try {
      final response = await sendMessage('مرحبا');
      return response.isNotEmpty && !response.contains('خطأ');
    } catch (e) {
      return false;
    }
  }
}
