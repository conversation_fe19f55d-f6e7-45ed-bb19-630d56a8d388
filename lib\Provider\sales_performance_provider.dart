// مزود بيانات أداء المندوبين
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Provider/transactions_provider.dart';
import 'package:mobile_pos/models/sales_performance_model.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/repository/sales_performance_repo.dart';

/// مزود بيانات أداء المندوبين
final salesPerformanceProvider = StateNotifierProvider<SalesPerformanceNotifier,
    AsyncValue<List<SalesPerformanceModel>>>((ref) {
  final transactions = ref.watch(transitionProvider);

  return SalesPerformanceNotifier(
    ref: ref,
    transactions: transactions.value ?? [],
  );
});

/// مزود حالة فترة أداء المندوبين
final salesPeriodProvider = StateProvider<SalesPeriod>((ref) {
  // افتراضياً، الفترة هي الشهر الحالي
  final now = DateTime.now();
  final startOfMonth = DateTime(now.year, now.month, 1);
  final endOfMonth = DateTime(now.year, now.month + 1, 0);

  return SalesPeriod(
    startDate: startOfMonth,
    endDate: endOfMonth,
  );
});

/// مزود أهداف المبيعات
final salesTargetProvider = StateNotifierProvider<SalesTargetNotifier,
    AsyncValue<List<SalesTargetModel>>>((ref) {
  return SalesTargetNotifier();
});

/// مزود بيانات أداء المندوبين
class SalesPerformanceNotifier
    extends StateNotifier<AsyncValue<List<SalesPerformanceModel>>> {
  final Ref _ref;
  final List<SalesTransitionModel> transactions;
  final SalesPerformanceRepo _repo = SalesPerformanceRepo();

  SalesPerformanceNotifier({
    required Ref ref,
    required this.transactions,
  })  : _ref = ref,
        super(const AsyncValue.loading()) {
    loadPerformanceData();
  }

  /// تحميل بيانات أداء المندوبين
  Future<void> loadPerformanceData() async {
    state = const AsyncValue.loading();

    try {
      final period = _ref.read(salesPeriodProvider);

      // الحصول على قائمة المستخدمين
      final users = await _repo.getUsers();

      // إنشاء نموذج أداء لكل مستخدم
      final performanceData = <SalesPerformanceModel>[];

      for (var user in users) {
        // تصفية المعاملات حسب المستخدم
        final userTransactions = transactions
            .where((transaction) => transaction.sellerName == user['name'])
            .toList();

        // إنشاء نموذج أداء المندوب
        final performance = SalesPerformanceModel.fromTransactions(
          user['id'],
          user['name'],
          user['email'],
          userTransactions,
          period.startDate,
          period.endDate,
        );

        performanceData.add(performance);
      }

      state = AsyncValue.data(performanceData);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// تحديث فترة أداء المندوبين
  Future<void> updatePeriod(DateTime startDate, DateTime endDate) async {
    _ref.read(salesPeriodProvider.notifier).state = SalesPeriod(
      startDate: startDate,
      endDate: endDate,
    );

    await loadPerformanceData();
  }

  /// الحصول على إجمالي المبيعات لجميع المندوبين
  double getTotalSalesAmount() {
    if (state.value == null) {
      return 0;
    }

    return state.value!
        .fold(0, (sum, performance) => sum + performance.totalAmount);
  }

  /// الحصول على إجمالي عدد المبيعات لجميع المندوبين
  int getTotalSalesCount() {
    if (state.value == null) {
      return 0;
    }

    return state.value!
        .fold(0, (sum, performance) => sum + performance.totalSales);
  }

  /// الحصول على إجمالي عدد العملاء لجميع المندوبين
  int getTotalCustomersCount() {
    if (state.value == null) {
      return 0;
    }

    // حساب عدد العملاء الفريدين
    final uniqueCustomers = <String>{};

    for (var performance in state.value!) {
      for (var transaction in performance.transactions) {
        if (transaction.customerPhone.isNotEmpty) {
          uniqueCustomers.add(transaction.customerPhone);
        }
      }
    }

    return uniqueCustomers.length;
  }

  /// الحصول على متوسط قيمة الطلب لجميع المندوبين
  double getAverageOrderValue() {
    final totalSales = getTotalSalesCount();
    final totalAmount = getTotalSalesAmount();

    return totalSales > 0 ? totalAmount / totalSales : 0;
  }

  /// الحصول على المبيعات اليومية لجميع المندوبين
  Map<String, double> getDailySales() {
    if (state.value == null) {
      return {};
    }

    final dailySales = <String, double>{};

    for (var performance in state.value!) {
      for (var entry in performance.dailySales.entries) {
        if (!dailySales.containsKey(entry.key)) {
          dailySales[entry.key] = 0;
        }

        dailySales[entry.key] = dailySales[entry.key]! + entry.value;
      }
    }

    return dailySales;
  }

  /// الحصول على المبيعات حسب فئة المنتج لجميع المندوبين
  Map<String, int> getProductCategorySales() {
    if (state.value == null) {
      return {};
    }

    final productCategorySales = <String, int>{};

    for (var performance in state.value!) {
      for (var entry in performance.productCategorySales.entries) {
        if (!productCategorySales.containsKey(entry.key)) {
          productCategorySales[entry.key] = 0;
        }

        productCategorySales[entry.key] =
            productCategorySales[entry.key]! + entry.value;
      }
    }

    return productCategorySales;
  }
}

/// مزود أهداف المبيعات
class SalesTargetNotifier
    extends StateNotifier<AsyncValue<List<SalesTargetModel>>> {
  final SalesPerformanceRepo _repo = SalesPerformanceRepo();

  SalesTargetNotifier() : super(const AsyncValue.loading()) {
    loadTargets();
  }

  /// تحميل أهداف المبيعات
  Future<void> loadTargets() async {
    state = const AsyncValue.loading();

    try {
      final targets = await _repo.getSalesTargets();
      state = AsyncValue.data(targets);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// إضافة هدف مبيعات جديد
  Future<void> addTarget(SalesTargetModel target) async {
    try {
      await _repo.addSalesTarget(target);
      await loadTargets();
    } catch (e) {
      // خطأ في إضافة هدف مبيعات
    }
  }

  /// تحديث هدف مبيعات
  Future<void> updateTarget(SalesTargetModel target) async {
    try {
      await _repo.updateSalesTarget(target);
      await loadTargets();
    } catch (e) {
      // خطأ في تحديث هدف مبيعات
    }
  }

  /// حذف هدف مبيعات
  Future<void> deleteTarget(String targetId) async {
    try {
      await _repo.deleteSalesTarget(targetId);
      await loadTargets();
    } catch (e) {
      // خطأ في حذف هدف مبيعات
    }
  }

  /// الحصول على أهداف مبيعات مستخدم معين
  List<SalesTargetModel> getUserTargets(String userId) {
    if (state.value == null) {
      return [];
    }

    return state.value!.where((target) => target.userId == userId).toList();
  }

  /// الحصول على أهداف المبيعات النشطة
  List<SalesTargetModel> getActiveTargets() {
    if (state.value == null) {
      return [];
    }

    final now = DateTime.now();

    return state.value!.where((target) {
      return (target.startDate.isBefore(now) ||
              target.startDate.isAtSameMomentAs(now)) &&
          (target.endDate.isAfter(now) || target.endDate.isAtSameMomentAs(now));
    }).toList();
  }
}

/// فترة أداء المندوبين
class SalesPeriod {
  final DateTime startDate;
  final DateTime endDate;

  SalesPeriod({
    required this.startDate,
    required this.endDate,
  });
}
