import 'water_filter_models.dart';

/// نموذج صلاحيات النشاط التجاري
class BusinessPermissions {
  late bool canView;
  late bool canAdd;
  late bool canEdit;
  late bool canDelete;
  late bool canManageUsers;
  late bool canViewReports;
  late bool canExportData;
  late Map<String, bool> specificPermissions;

  BusinessPermissions({
    this.canView = false,
    this.canAdd = false,
    this.canEdit = false,
    this.canDelete = false,
    this.canManageUsers = false,
    this.canViewReports = false,
    this.canExportData = false,
    Map<String, bool>? specificPermissions,
  }) : specificPermissions = specificPermissions ?? {};

  BusinessPermissions.fromJson(Map<dynamic, dynamic> json)
      : canView = json['canView'] ?? false,
        canAdd = json['canAdd'] ?? false,
        canEdit = json['canEdit'] ?? false,
        canDelete = json['canDelete'] ?? false,
        canManageUsers = json['canManageUsers'] ?? false,
        canViewReports = json['canViewReports'] ?? false,
        canExportData = json['canExportData'] ?? false,
        specificPermissions = Map<String, bool>.from(json['specificPermissions'] ?? {});

  Map<String, dynamic> toJson() => {
        'canView': canView,
        'canAdd': canAdd,
        'canEdit': canEdit,
        'canDelete': canDelete,
        'canManageUsers': canManageUsers,
        'canViewReports': canViewReports,
        'canExportData': canExportData,
        'specificPermissions': specificPermissions,
      };

  /// إنشاء صلاحيات كاملة
  factory BusinessPermissions.fullAccess() {
    return BusinessPermissions(
      canView: true,
      canAdd: true,
      canEdit: true,
      canDelete: true,
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
    );
  }

  /// إنشاء صلاحيات للقراءة فقط
  factory BusinessPermissions.readOnly() {
    return BusinessPermissions(
      canView: true,
      canAdd: false,
      canEdit: false,
      canDelete: false,
      canManageUsers: false,
      canViewReports: true,
      canExportData: false,
    );
  }

  /// إنشاء صلاحيات فلاتر المياه الأساسية
  factory BusinessPermissions.waterFiltersBasic() {
    return BusinessPermissions(
      canView: true,
      canAdd: true,
      canEdit: true,
      canDelete: false,
      canManageUsers: false,
      canViewReports: true,
      canExportData: false,
      specificPermissions: {
        'maintenance_scheduling': true,
        'customer_management': true,
        'installment_tracking': true,
        'basic_reports': true,
        'technician_assignment': false,
        'advanced_analytics': false,
        'bulk_operations': false,
      },
    );
  }

  /// إنشاء صلاحيات فلاتر المياه المتقدمة
  factory BusinessPermissions.waterFiltersAdvanced() {
    return BusinessPermissions(
      canView: true,
      canAdd: true,
      canEdit: true,
      canDelete: true,
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      specificPermissions: {
        'maintenance_scheduling': true,
        'customer_management': true,
        'installment_tracking': true,
        'basic_reports': true,
        'technician_assignment': true,
        'advanced_analytics': true,
        'bulk_operations': true,
        'system_configuration': true,
        'financial_reports': true,
        'customer_communication': true,
      },
    );
  }
}

/// نموذج حدود النشاط التجاري
class BusinessLimits {
  late int maxProducts;
  late int maxCustomers;
  late int maxUsers;
  late int maxMonthlyTransactions;
  late int maxStorageGB;
  late bool hasAdvancedReports;
  late bool hasAPIAccess;
  late bool hasCustomBranding;
  late Map<String, dynamic> specificLimits;

  BusinessLimits({
    this.maxProducts = 100,
    this.maxCustomers = 500,
    this.maxUsers = 3,
    this.maxMonthlyTransactions = 1000,
    this.maxStorageGB = 1,
    this.hasAdvancedReports = false,
    this.hasAPIAccess = false,
    this.hasCustomBranding = false,
    Map<String, dynamic>? specificLimits,
  }) : specificLimits = specificLimits ?? {};

  BusinessLimits.fromJson(Map<dynamic, dynamic> json)
      : maxProducts = json['maxProducts'] ?? 100,
        maxCustomers = json['maxCustomers'] ?? 500,
        maxUsers = json['maxUsers'] ?? 3,
        maxMonthlyTransactions = json['maxMonthlyTransactions'] ?? 1000,
        maxStorageGB = json['maxStorageGB'] ?? 1,
        hasAdvancedReports = json['hasAdvancedReports'] ?? false,
        hasAPIAccess = json['hasAPIAccess'] ?? false,
        hasCustomBranding = json['hasCustomBranding'] ?? false,
        specificLimits = Map<String, dynamic>.from(json['specificLimits'] ?? {});

  Map<String, dynamic> toJson() => {
        'maxProducts': maxProducts,
        'maxCustomers': maxCustomers,
        'maxUsers': maxUsers,
        'maxMonthlyTransactions': maxMonthlyTransactions,
        'maxStorageGB': maxStorageGB,
        'hasAdvancedReports': hasAdvancedReports,
        'hasAPIAccess': hasAPIAccess,
        'hasCustomBranding': hasCustomBranding,
        'specificLimits': specificLimits,
      };

  /// إنشاء حدود أساسية
  factory BusinessLimits.basic() {
    return BusinessLimits(
      maxProducts: 50,
      maxCustomers: 200,
      maxUsers: 2,
      maxMonthlyTransactions: 500,
      maxStorageGB: 1,
      hasAdvancedReports: false,
      hasAPIAccess: false,
      hasCustomBranding: false,
    );
  }

  /// إنشاء حدود متقدمة
  factory BusinessLimits.premium() {
    return BusinessLimits(
      maxProducts: 500,
      maxCustomers: 2000,
      maxUsers: 10,
      maxMonthlyTransactions: 5000,
      maxStorageGB: 10,
      hasAdvancedReports: true,
      hasAPIAccess: true,
      hasCustomBranding: true,
    );
  }

  /// إنشاء حدود غير محدودة
  factory BusinessLimits.unlimited() {
    return BusinessLimits(
      maxProducts: -1, // -1 يعني غير محدود
      maxCustomers: -1,
      maxUsers: -1,
      maxMonthlyTransactions: -1,
      maxStorageGB: 100,
      hasAdvancedReports: true,
      hasAPIAccess: true,
      hasCustomBranding: true,
    );
  }

  /// التحقق من عدم تجاوز الحد
  bool isWithinLimit(String limitType, int currentValue) {
    switch (limitType) {
      case 'products':
        return maxProducts == -1 || currentValue <= maxProducts;
      case 'customers':
        return maxCustomers == -1 || currentValue <= maxCustomers;
      case 'users':
        return maxUsers == -1 || currentValue <= maxUsers;
      case 'transactions':
        return maxMonthlyTransactions == -1 || currentValue <= maxMonthlyTransactions;
      default:
        return true;
    }
  }
}

/// نموذج اشتراك الأنشطة التجارية
class BusinessSubscriptionModel {
  late String id;
  late String userId;
  late String planType; // 'free', 'basic', 'premium', 'enterprise'
  late List<BusinessType> allowedBusinessTypes;
  late Map<BusinessType, BusinessPermissions> businessPermissions;
  late Map<BusinessType, BusinessLimits> businessLimits;
  late DateTime startDate;
  late DateTime endDate;
  late bool isActive;
  late double monthlyPrice;
  late double yearlyPrice;
  String? paymentMethod;
  DateTime? lastPaymentDate;
  DateTime? nextPaymentDate;
  DateTime? createdAt;
  DateTime? updatedAt;

  BusinessSubscriptionModel({
    required this.id,
    required this.userId,
    required this.planType,
    required this.allowedBusinessTypes,
    required this.businessPermissions,
    required this.businessLimits,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.monthlyPrice,
    required this.yearlyPrice,
    this.paymentMethod,
    this.lastPaymentDate,
    this.nextPaymentDate,
    this.createdAt,
    this.updatedAt,
  });

  BusinessSubscriptionModel.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        userId = json['userId'] ?? '',
        planType = json['planType'] ?? 'free',
        allowedBusinessTypes = (json['allowedBusinessTypes'] as List?)
                ?.map((type) => BusinessType.values.firstWhere(
                      (bt) => bt.id == type,
                      orElse: () => BusinessType.pos,
                    ))
                .toList() ??
            [BusinessType.pos],
        businessPermissions = (json['businessPermissions'] as Map?)?.map(
              (key, value) => MapEntry(
                BusinessType.values.firstWhere((bt) => bt.id == key),
                BusinessPermissions.fromJson(value),
              ),
            ) ??
            {},
        businessLimits = (json['businessLimits'] as Map?)?.map(
              (key, value) => MapEntry(
                BusinessType.values.firstWhere((bt) => bt.id == key),
                BusinessLimits.fromJson(value),
              ),
            ) ??
            {},
        startDate = DateTime.parse(json['startDate']),
        endDate = DateTime.parse(json['endDate']),
        isActive = json['isActive'] ?? false,
        monthlyPrice = (json['monthlyPrice'] ?? 0).toDouble(),
        yearlyPrice = (json['yearlyPrice'] ?? 0).toDouble(),
        paymentMethod = json['paymentMethod'],
        lastPaymentDate = json['lastPaymentDate'] != null
            ? DateTime.parse(json['lastPaymentDate'])
            : null,
        nextPaymentDate = json['nextPaymentDate'] != null
            ? DateTime.parse(json['nextPaymentDate'])
            : null,
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'planType': planType,
        'allowedBusinessTypes': allowedBusinessTypes.map((bt) => bt.id).toList(),
        'businessPermissions': businessPermissions.map(
          (key, value) => MapEntry(key.id, value.toJson()),
        ),
        'businessLimits': businessLimits.map(
          (key, value) => MapEntry(key.id, value.toJson()),
        ),
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'isActive': isActive,
        'monthlyPrice': monthlyPrice,
        'yearlyPrice': yearlyPrice,
        'paymentMethod': paymentMethod,
        'lastPaymentDate': lastPaymentDate?.toIso8601String(),
        'nextPaymentDate': nextPaymentDate?.toIso8601String(),
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  /// التحقق من صلاحية الوصول لنشاط معين
  bool canAccessBusiness(BusinessType businessType) {
    return isActive && allowedBusinessTypes.contains(businessType);
  }

  /// الحصول على صلاحيات نشاط معين
  BusinessPermissions? getBusinessPermissions(BusinessType businessType) {
    return businessPermissions[businessType];
  }

  /// الحصول على حدود نشاط معين
  BusinessLimits? getBusinessLimits(BusinessType businessType) {
    return businessLimits[businessType];
  }

  /// التحقق من انتهاء الاشتراك
  bool get isExpired => DateTime.now().isAfter(endDate);

  /// عدد الأيام المتبقية في الاشتراك
  int get daysRemaining => endDate.difference(DateTime.now()).inDays;
}
