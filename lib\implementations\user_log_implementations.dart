import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/user_log_service.dart';

/// تنفيذ سجلات المستخدم في وظائف المبيعات
class SalesLogImplementation {
  /// تسجيل إنشاء فاتورة مبيعات جديدة
  static Future<void> logSalesCreated({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double totalAmount,
    required List<Map<String, dynamic>> products,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.salesCreated,
        description: 'تم إنشاء فاتورة مبيعات جديدة برقم $invoiceNumber للعميل $customerName بمبلغ $totalAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'totalAmount': totalAmount,
          'products': products,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل إنشاء فاتورة المبيعات: $e');
    }
  }

  /// تسجيل تحديث فاتورة مبيعات
  static Future<void> logSalesUpdated({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double oldAmount,
    required double newAmount,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.salesUpdated,
        description: 'تم تحديث فاتورة المبيعات رقم $invoiceNumber للعميل $customerName من $oldAmount إلى $newAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'oldAmount': oldAmount,
          'newAmount': newAmount,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل تحديث فاتورة المبيعات: $e');
    }
  }

  /// تسجيل حذف فاتورة مبيعات
  static Future<void> logSalesDeleted({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double totalAmount,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.salesDeleted,
        description: 'تم حذف فاتورة المبيعات رقم $invoiceNumber للعميل $customerName بمبلغ $totalAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'totalAmount': totalAmount,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل حذف فاتورة المبيعات: $e');
    }
  }

  /// تسجيل استلام دفعة مبيعات
  static Future<void> logSalesPaymentReceived({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double amount,
    required String paymentMethod,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.salesPaymentReceived,
        description: 'تم استلام دفعة بمبلغ $amount من العميل $customerName لفاتورة المبيعات رقم $invoiceNumber',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'amount': amount,
          'paymentMethod': paymentMethod,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل استلام دفعة المبيعات: $e');
    }
  }

  /// تسجيل مرتجع مبيعات
  static Future<void> logSalesReturn({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double returnAmount,
    required List<Map<String, dynamic>> returnedProducts,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.salesReturn,
        description: 'تم إرجاع منتجات بمبلغ $returnAmount من فاتورة المبيعات رقم $invoiceNumber للعميل $customerName',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'returnAmount': returnAmount,
          'returnedProducts': returnedProducts,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل مرتجع المبيعات: $e');
    }
  }
}

/// تنفيذ سجلات المستخدم في وظائف المشتريات
class PurchaseLogImplementation {
  /// تسجيل إنشاء فاتورة مشتريات جديدة
  static Future<void> logPurchaseCreated({
    required String invoiceNumber,
    required String supplierName,
    required String supplierPhone,
    required double totalAmount,
    required List<Map<String, dynamic>> products,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.purchaseCreated,
        description: 'تم إنشاء فاتورة مشتريات جديدة برقم $invoiceNumber من المورد $supplierName بمبلغ $totalAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'supplierName': supplierName,
          'supplierPhone': supplierPhone,
          'totalAmount': totalAmount,
          'products': products,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل إنشاء فاتورة المشتريات: $e');
    }
  }

  /// تسجيل تحديث فاتورة مشتريات
  static Future<void> logPurchaseUpdated({
    required String invoiceNumber,
    required String supplierName,
    required String supplierPhone,
    required double oldAmount,
    required double newAmount,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.purchaseUpdated,
        description: 'تم تحديث فاتورة المشتريات رقم $invoiceNumber من المورد $supplierName من $oldAmount إلى $newAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'supplierName': supplierName,
          'supplierPhone': supplierPhone,
          'oldAmount': oldAmount,
          'newAmount': newAmount,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل تحديث فاتورة المشتريات: $e');
    }
  }

  /// تسجيل حذف فاتورة مشتريات
  static Future<void> logPurchaseDeleted({
    required String invoiceNumber,
    required String supplierName,
    required String supplierPhone,
    required double totalAmount,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.purchaseDeleted,
        description: 'تم حذف فاتورة المشتريات رقم $invoiceNumber من المورد $supplierName بمبلغ $totalAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'supplierName': supplierName,
          'supplierPhone': supplierPhone,
          'totalAmount': totalAmount,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل حذف فاتورة المشتريات: $e');
    }
  }

  /// تسجيل دفع فاتورة مشتريات
  static Future<void> logPurchasePaymentMade({
    required String invoiceNumber,
    required String supplierName,
    required String supplierPhone,
    required double amount,
    required String paymentMethod,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.purchasePaymentMade,
        description: 'تم دفع مبلغ $amount للمورد $supplierName لفاتورة المشتريات رقم $invoiceNumber',
        data: {
          'invoiceNumber': invoiceNumber,
          'supplierName': supplierName,
          'supplierPhone': supplierPhone,
          'amount': amount,
          'paymentMethod': paymentMethod,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل دفع فاتورة المشتريات: $e');
    }
  }

  /// تسجيل مرتجع مشتريات
  static Future<void> logPurchaseReturn({
    required String invoiceNumber,
    required String supplierName,
    required String supplierPhone,
    required double returnAmount,
    required List<Map<String, dynamic>> returnedProducts,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.purchaseReturn,
        description: 'تم إرجاع منتجات بمبلغ $returnAmount من فاتورة المشتريات رقم $invoiceNumber للمورد $supplierName',
        data: {
          'invoiceNumber': invoiceNumber,
          'supplierName': supplierName,
          'supplierPhone': supplierPhone,
          'returnAmount': returnAmount,
          'returnedProducts': returnedProducts,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل مرتجع المشتريات: $e');
    }
  }
}

/// تنفيذ سجلات المستخدم في وظائف المديونية
class DueLogImplementation {
  /// تسجيل إنشاء مديونية جديدة
  static Future<void> logDueCreated({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double dueAmount,
    required DateTime dueDate,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.dueCreated,
        description: 'تم إنشاء مديونية جديدة برقم $invoiceNumber للعميل $customerName بمبلغ $dueAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'dueAmount': dueAmount,
          'dueDate': dueDate.toIso8601String(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل إنشاء المديونية: $e');
    }
  }

  /// تسجيل تحديث مديونية
  static Future<void> logDueUpdated({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double oldAmount,
    required double newAmount,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.dueUpdated,
        description: 'تم تحديث مديونية رقم $invoiceNumber للعميل $customerName من $oldAmount إلى $newAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'oldAmount': oldAmount,
          'newAmount': newAmount,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل تحديث المديونية: $e');
    }
  }

  /// تسجيل استلام دفعة مديونية
  static Future<void> logDuePaymentReceived({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double amount,
    required double remainingAmount,
    required String paymentMethod,
    String? notes,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.duePaymentReceived,
        description: 'تم استلام دفعة بمبلغ $amount من العميل $customerName لمديونية رقم $invoiceNumber، المبلغ المتبقي: $remainingAmount',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'amount': amount,
          'remainingAmount': remainingAmount,
          'paymentMethod': paymentMethod,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل استلام دفعة المديونية: $e');
    }
  }

  /// تسجيل تذكير بمديونية
  static Future<void> logDueReminder({
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    required double dueAmount,
    required int daysOverdue,
    required WidgetRef ref,
  }) async {
    try {
      final userLogService = UserLogService();
      
      await userLogService.logUserAction(
        actionType: UserLogActionTypes.dueReminder,
        description: 'تم إرسال تذكير للعميل $customerName بمديونية رقم $invoiceNumber بمبلغ $dueAmount متأخرة $daysOverdue يوم',
        data: {
          'invoiceNumber': invoiceNumber,
          'customerName': customerName,
          'customerPhone': customerPhone,
          'dueAmount': dueAmount,
          'daysOverdue': daysOverdue,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل تذكير المديونية: $e');
    }
  }
}
