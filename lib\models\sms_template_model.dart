// لا نحتاج إلى استيراد أي مكتبة هنا

class SmsTemplateModel {
  final String? id;
  final String? saleTemplate;
  final String? saleReturnTemplate;
  final String? quotationTemplate;
  final String? purchaseTemplate;
  final String? purchaseReturnTemplate;
  final String? dueTemplate;
  final String? bulkSmsTemplate;

  SmsTemplateModel({
    this.id,
    this.saleTemplate,
    this.saleReturnTemplate,
    this.quotationTemplate,
    this.purchaseTemplate,
    this.purchaseReturnTemplate,
    this.dueTemplate,
    this.bulkSmsTemplate,
  });

  // تحويل من Firestore
  factory SmsTemplateModel.fromMap(Map<String, dynamic> map, String id) {
    return SmsTemplateModel(
      id: id,
      saleTemplate: map['saleTemplate'],
      saleReturnTemplate: map['saleReturnTemplate'],
      quotationTemplate: map['quotationTemplate'],
      purchaseTemplate: map['purchaseTemplate'],
      purchaseReturnTemplate: map['purchaseReturnTemplate'],
      dueTemplate: map['dueTemplate'],
      bulkSmsTemplate: map['bulkSmsTemplate'],
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'saleTemplate': saleTemplate,
      'saleReturnTemplate': saleReturnTemplate,
      'quotationTemplate': quotationTemplate,
      'purchaseTemplate': purchaseTemplate,
      'purchaseReturnTemplate': purchaseReturnTemplate,
      'dueTemplate': dueTemplate,
      'bulkSmsTemplate': bulkSmsTemplate,
    };
  }

  // نسخة مع تعديلات
  SmsTemplateModel copyWith({
    String? id,
    String? saleTemplate,
    String? saleReturnTemplate,
    String? quotationTemplate,
    String? purchaseTemplate,
    String? purchaseReturnTemplate,
    String? dueTemplate,
    String? bulkSmsTemplate,
  }) {
    return SmsTemplateModel(
      id: id ?? this.id,
      saleTemplate: saleTemplate ?? this.saleTemplate,
      saleReturnTemplate: saleReturnTemplate ?? this.saleReturnTemplate,
      quotationTemplate: quotationTemplate ?? this.quotationTemplate,
      purchaseTemplate: purchaseTemplate ?? this.purchaseTemplate,
      purchaseReturnTemplate:
          purchaseReturnTemplate ?? this.purchaseReturnTemplate,
      dueTemplate: dueTemplate ?? this.dueTemplate,
      bulkSmsTemplate: bulkSmsTemplate ?? this.bulkSmsTemplate,
    );
  }
}
