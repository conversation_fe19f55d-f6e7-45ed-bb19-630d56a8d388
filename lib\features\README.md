# نظام الميزات المعيارية (Modular Features System)

## نظرة عامة

نظام الميزات المعيارية هو نظام مصمم لإضافة ميزات جديدة إلى تطبيق AmrDevPOS بطريقة معيارية ومستقلة. يتيح هذا النظام إضافة وإزالة الميزات بسهولة دون التأثير على الكود الأصلي للتطبيق.

## الميزات المتاحة

1. **الملف الشخصي (User Profile)**: عرض وتعديل معلومات الملف الشخصي للمستخدم.
2. **الإشعارات (Notifications)**: إدارة وعرض الإشعارات والتنبيهات، بما في ذلك إشعارات المديونية والرسائل.
3. **المحادثات (Chat)**: التواصل مع المستخدمين الآخرين والمساعد الذكي (Chatbot) المدعوم بالذكاء الاصطناعي.

## هيكل النظام

```
features/
├── core/                  # المكونات الأساسية المشتركة بين الميزات
│   ├── auth/              # نظام المصادقة المرن
│   ├── components/        # المكونات المشتركة
│   ├── theme/             # سمات التطبيق
│   ├── utils/             # أدوات مساعدة
│   ├── feature_interface.dart # واجهة الميزات
│   └── feature_manager.dart   # مدير الميزات
│
├── user_profile/          # ميزة الملف الشخصي
│   ├── models/            # نماذج البيانات
│   ├── screens/           # شاشات الميزة
│   ├── services/          # خدمات الميزة
│   └── profile_feature.dart # نقطة دخول الميزة
│
├── notifications/         # ميزة الإشعارات
│   ├── models/            # نماذج البيانات
│   ├── screens/           # شاشات الميزة
│   ├── services/          # خدمات الميزة
│   └── notification_feature.dart # نقطة دخول الميزة
│
├── chat/                  # ميزة المحادثات
│   ├── ai/                # خدمات الذكاء الاصطناعي
│   │   ├── chatbot/       # خدمة الشات بوت
│   │   └── groq_service.dart # خدمة Groq API
│   ├── models/            # نماذج البيانات
│   ├── screens/           # شاشات الميزة
│   ├── services/          # خدمات الميزة
│   └── chat_feature.dart  # نقطة دخول الميزة
│
├── feature_app.dart       # تطبيق الميزات المستقل
├── feature_registry.dart  # سجل الميزات
└── README.md              # هذا الملف
```

## كيفية الاستخدام

### تشغيل تطبيق الميزات بشكل مستقل

يمكن تشغيل تطبيق الميزات بشكل مستقل عن التطبيق الرئيسي للاختبار والتطوير:

```dart
import 'package:flutter/material.dart';
import 'features/feature_app.dart';

void main() {
  runApp(const FeatureApp());
}
```

### دمج الميزات في التطبيق الرئيسي

لدمج الميزات في التطبيق الرئيسي، اتبع الخطوات التالية:

1. **تسجيل الميزات**: قم بتسجيل الميزات التي ترغب في استخدامها في بداية تشغيل التطبيق:

```dart
import 'features/feature_registry.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تسجيل جميع الميزات
  FeatureRegistry().registerAllFeatures();
  
  // تهيئة الميزات المفعلة
  await FeatureRegistry().initializeEnabledFeatures();
  
  runApp(MyApp());
}
```

2. **إضافة مسارات الميزات**: قم بإضافة مسارات الميزات إلى مسارات التطبيق الرئيسي:

```dart
import 'features/core/feature_manager.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // الحصول على مسارات الميزات المفعلة
    final featureRoutes = FeatureManager().getAllRoutes();
    
    // دمج مسارات الميزات مع مسارات التطبيق الرئيسي
    final routes = {
      '/': (context) => HomeScreen(),
      // مسارات التطبيق الرئيسي الأخرى
      ...featureRoutes,
    };
    
    return MaterialApp(
      routes: routes,
      // باقي إعدادات التطبيق
    );
  }
}
```

3. **إضافة أزرار التنقل**: قم بإضافة أزرار للتنقل إلى شاشات الميزات:

```dart
import 'features/core/feature_manager.dart';

class SettingsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final enabledFeatures = FeatureManager().getEnabledFeatures();
    
    return Scaffold(
      appBar: AppBar(title: Text('الإعدادات')),
      body: ListView(
        children: [
          // عناصر الإعدادات الأخرى
          
          // إضافة عناصر للميزات المفعلة
          ...enabledFeatures.map((feature) => ListTile(
            leading: Icon(feature.featureIcon),
            title: Text(feature.featureName),
            subtitle: Text(feature.featureDescription),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => feature.getMainScreen(),
                ),
              );
            },
          )),
        ],
      ),
    );
  }
}
```

## إضافة ميزة جديدة

لإضافة ميزة جديدة، اتبع الخطوات التالية:

1. قم بإنشاء مجلد جديد للميزة داخل مجلد `features`.
2. قم بإنشاء نماذج البيانات والخدمات والشاشات اللازمة للميزة.
3. قم بإنشاء ملف `feature.dart` يقوم بتنفيذ واجهة `FeatureInterface`.
4. قم بتسجيل الميزة الجديدة في `feature_registry.dart`.

مثال على تنفيذ ميزة جديدة:

```dart
import 'package:flutter/material.dart';
import '../core/feature_interface.dart';

class NewFeature implements FeatureInterface {
  static final NewFeature _instance = NewFeature._internal();
  factory NewFeature() => _instance;
  NewFeature._internal();

  bool _isEnabled = true;

  @override
  String get featureName => 'الميزة الجديدة';

  @override
  String get featureDescription => 'وصف الميزة الجديدة';

  @override
  IconData get featureIcon => Icons.star;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Widget getMainScreen() {
    return const NewFeatureScreen();
  }

  @override
  Map<String, WidgetBuilder> getRoutes() {
    return {
      '/new-feature': (context) => const NewFeatureScreen(),
    };
  }

  @override
  Future<void> initialize() async {
    // تهيئة الميزة
  }

  @override
  Future<void> dispose() async {
    // إيقاف الميزة
  }
}
```

## ملاحظات هامة

- جميع الميزات مستقلة تمامًا عن الكود الأصلي للتطبيق.
- يمكن تفعيل أو تعطيل الميزات بسهولة.
- يمكن إضافة ميزات جديدة دون التأثير على الميزات الموجودة.
- جميع الميزات تستخدم نفس سمات التطبيق لضمان تجربة مستخدم متسقة.
- يتم دعم اللغة العربية بشكل كامل في جميع الميزات.
