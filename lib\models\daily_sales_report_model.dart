import 'package:mobile_pos/model/transition_model.dart';

/// نموذج تقرير المبيعات اليومي المجمع
class DailySalesReportModel {
  final DateTime reportDate;
  final List<SalesTransitionModel> salesTransactions;
  final DailySalesSummary summary;
  final List<ProductSalesSummary> productsSummary;
  final PaymentMethodSummary paymentSummary;

  DailySalesReportModel({
    required this.reportDate,
    required this.salesTransactions,
    required this.summary,
    required this.productsSummary,
    required this.paymentSummary,
  });

  /// إنشاء تقرير من قائمة المعاملات
  factory DailySalesReportModel.fromTransactions(
    DateTime date,
    List<SalesTransitionModel> transactions,
  ) {
    // حساب الملخص العام
    final summary = DailySalesSummary.fromTransactions(transactions);

    // حساب ملخص الأصناف
    final productsSummary = _calculateProductsSummary(transactions);

    // حساب ملخص طرق الدفع
    final paymentSummary = PaymentMethodSummary.fromTransactions(transactions);

    return DailySalesReportModel(
      reportDate: date,
      salesTransactions: transactions,
      summary: summary,
      productsSummary: productsSummary,
      paymentSummary: paymentSummary,
    );
  }

  /// حساب ملخص الأصناف المباعة
  static List<ProductSalesSummary> _calculateProductsSummary(
    List<SalesTransitionModel> transactions,
  ) {
    Map<String, ProductSalesSummary> productsMap = {};

    for (var transaction in transactions) {
      if (transaction.productList != null) {
        for (var product in transaction.productList!) {
          final key = '${product.productName}_${product.productId}';

          if (productsMap.containsKey(key)) {
            // تحويل آمن لـ subTotal
            double subTotalValue = 0.0;
            if (product.subTotal != null) {
              if (product.subTotal is double) {
                subTotalValue = product.subTotal;
              } else if (product.subTotal is String) {
                subTotalValue = double.tryParse(product.subTotal) ?? 0.0;
              } else if (product.subTotal is num) {
                subTotalValue = product.subTotal.toDouble();
              }
            }

            productsMap[key] = productsMap[key]!.copyWith(
              totalQuantity: productsMap[key]!.totalQuantity + product.quantity,
              totalAmount: productsMap[key]!.totalAmount + subTotalValue,
            );
          } else {
            // تحويل آمن لـ subTotal
            double subTotalValue = 0.0;
            if (product.subTotal != null) {
              if (product.subTotal is double) {
                subTotalValue = product.subTotal;
              } else if (product.subTotal is String) {
                subTotalValue = double.tryParse(product.subTotal) ?? 0.0;
              } else if (product.subTotal is num) {
                subTotalValue = product.subTotal.toDouble();
              }
            }

            // تحويل آمن لـ unitPrice
            double unitPriceValue = 0.0;
            if (product.unitPrice != null) {
              if (product.unitPrice is double) {
                unitPriceValue = product.unitPrice;
              } else if (product.unitPrice is String) {
                unitPriceValue = double.tryParse(product.unitPrice) ?? 0.0;
              } else if (product.unitPrice is num) {
                unitPriceValue = product.unitPrice.toDouble();
              }
            }

            productsMap[key] = ProductSalesSummary(
              productName: product.productName ?? '',
              productCode: product.productId?.toString() ?? '',
              totalQuantity: product.quantity,
              unitPrice: unitPriceValue,
              totalAmount: subTotalValue,
            );
          }
        }
      }
    }

    return productsMap.values.toList()
      ..sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
  }
}

/// ملخص المبيعات اليومي
class DailySalesSummary {
  final int totalInvoices;
  final double totalSales;
  final double totalVat;
  final double totalDiscount;
  final double totalServiceCharge;
  final double netSales;
  final double totalDue;
  final double totalPaid;

  DailySalesSummary({
    required this.totalInvoices,
    required this.totalSales,
    required this.totalVat,
    required this.totalDiscount,
    required this.totalServiceCharge,
    required this.netSales,
    required this.totalDue,
    required this.totalPaid,
  });

  factory DailySalesSummary.fromTransactions(
      List<SalesTransitionModel> transactions) {
    double totalSales = 0;
    double totalVat = 0;
    double totalDiscount = 0;
    double totalServiceCharge = 0;
    double totalDue = 0;
    double totalPaid = 0;

    for (var transaction in transactions) {
      totalSales += transaction.totalAmount ?? 0;
      totalVat += transaction.vat ?? 0;
      totalDiscount += transaction.discountAmount ?? 0;
      totalServiceCharge += transaction.serviceCharge ?? 0;
      totalDue += transaction.dueAmount ?? 0;
      totalPaid +=
          (transaction.totalAmount ?? 0) - (transaction.dueAmount ?? 0);
    }

    return DailySalesSummary(
      totalInvoices: transactions.length,
      totalSales: totalSales,
      totalVat: totalVat,
      totalDiscount: totalDiscount,
      totalServiceCharge: totalServiceCharge,
      netSales: totalSales - totalDiscount,
      totalDue: totalDue,
      totalPaid: totalPaid,
    );
  }
}

/// ملخص مبيعات الأصناف
class ProductSalesSummary {
  final String productName;
  final String productCode;
  final num totalQuantity;
  final double unitPrice;
  final double totalAmount;

  ProductSalesSummary({
    required this.productName,
    required this.productCode,
    required this.totalQuantity,
    required this.unitPrice,
    required this.totalAmount,
  });

  ProductSalesSummary copyWith({
    String? productName,
    String? productCode,
    num? totalQuantity,
    double? unitPrice,
    double? totalAmount,
  }) {
    return ProductSalesSummary(
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      totalQuantity: totalQuantity ?? this.totalQuantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
    );
  }
}

/// ملخص طرق الدفع
class PaymentMethodSummary {
  final double cashSales;
  final double creditSales;
  final int cashInvoicesCount;
  final int creditInvoicesCount;

  PaymentMethodSummary({
    required this.cashSales,
    required this.creditSales,
    required this.cashInvoicesCount,
    required this.creditInvoicesCount,
  });

  factory PaymentMethodSummary.fromTransactions(
      List<SalesTransitionModel> transactions) {
    double cashSales = 0;
    double creditSales = 0;
    int cashInvoicesCount = 0;
    int creditInvoicesCount = 0;

    for (var transaction in transactions) {
      final totalAmount = transaction.totalAmount ?? 0;
      final dueAmount = transaction.dueAmount ?? 0;

      if (dueAmount == 0) {
        // مبيعات نقدية
        cashSales += totalAmount;
        cashInvoicesCount++;
      } else {
        // مبيعات آجلة
        creditSales += totalAmount;
        creditInvoicesCount++;
      }
    }

    return PaymentMethodSummary(
      cashSales: cashSales,
      creditSales: creditSales,
      cashInvoicesCount: cashInvoicesCount,
      creditInvoicesCount: creditInvoicesCount,
    );
  }
}
