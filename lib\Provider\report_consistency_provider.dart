// مزود للتحقق من اتساق بيانات التقارير
import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/model/transition_model.dart';

import 'package:mobile_pos/model/product_model.dart';

/// مزود للتحقق من اتساق بيانات التقارير
final reportConsistencyProvider = Provider<ReportConsistencyService>((ref) {
  return ReportConsistencyService(ref);
});

/// مزود لحالة التحقق من اتساق البيانات
final reportConsistencyStateProvider =
    StateProvider<ReportConsistencyState>((ref) {
  return ReportConsistencyState(
    isChecking: false,
    totalChecks: 0,
    completedChecks: 0,
    inconsistentItems: 0,
    inconsistencyResults: [],
  );
});

/// خدمة التحقق من اتساق بيانات التقارير
class ReportConsistencyService {
  final Ref _ref;

  ReportConsistencyService(this._ref);

  /// التحقق من اتساق بيانات المبيعات
  Future<void> checkSalesReportConsistency() async {
    // تحديث حالة التحقق
    _ref.read(reportConsistencyStateProvider.notifier).state =
        ReportConsistencyState(
      isChecking: true,
      totalChecks: 0,
      completedChecks: 0,
      inconsistentItems: 0,
      inconsistencyResults: [],
    );

    try {
      // الحصول على بيانات المبيعات
      final salesData = await _getSalesData();

      // تحديث إجمالي عدد العناصر للتحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                totalChecks: salesData.length,
              ));

      // التحقق من اتساق كل عنصر
      int inconsistentCount = 0;
      List<Map<String, dynamic>> inconsistencyResults = [];

      for (var sale in salesData) {
        // التحقق من اتساق بيانات المبيعات
        final isConsistent = await _verifySaleConsistency(sale);

        // إذا كانت البيانات غير متسقة، إضافتها إلى النتائج
        if (!isConsistent.isConsistent) {
          inconsistentCount++;
          inconsistencyResults.add({
            'type': 'sale',
            'id': sale.invoiceNumber,
            'date': sale.purchaseDate,
            'details': isConsistent.details,
            'data': sale,
          });
        }

        // تحديث عدد العناصر المكتملة
        _ref
            .read(reportConsistencyStateProvider.notifier)
            .update((state) => state.copyWith(
                  completedChecks: state.completedChecks + 1,
                  inconsistentItems: inconsistentCount,
                  inconsistencyResults: inconsistencyResults,
                ));
      }

      // تحديث حالة التحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المبيعات

      // تحديث حالة التحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    }
  }

  /// التحقق من اتساق بيانات المشتريات
  Future<void> checkPurchaseReportConsistency() async {
    // تحديث حالة التحقق
    _ref.read(reportConsistencyStateProvider.notifier).state =
        ReportConsistencyState(
      isChecking: true,
      totalChecks: 0,
      completedChecks: 0,
      inconsistentItems: 0,
      inconsistencyResults: [],
    );

    try {
      // الحصول على بيانات المشتريات
      final purchaseData = await _getPurchaseData();

      // تحديث إجمالي عدد العناصر للتحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                totalChecks: purchaseData.length,
              ));

      // التحقق من اتساق كل عنصر
      int inconsistentCount = 0;
      List<Map<String, dynamic>> inconsistencyResults = [];

      for (var purchase in purchaseData) {
        // التحقق من اتساق بيانات المشتريات
        final isConsistent = await _verifyPurchaseConsistency(purchase);

        // إذا كانت البيانات غير متسقة، إضافتها إلى النتائج
        if (!isConsistent.isConsistent) {
          inconsistentCount++;
          inconsistencyResults.add({
            'type': 'purchase',
            'id': purchase['invoiceNumber'],
            'date': purchase['purchaseDate'],
            'details': isConsistent.details,
            'data': purchase,
          });
        }

        // تحديث عدد العناصر المكتملة
        _ref
            .read(reportConsistencyStateProvider.notifier)
            .update((state) => state.copyWith(
                  completedChecks: state.completedChecks + 1,
                  inconsistentItems: inconsistentCount,
                  inconsistencyResults: inconsistencyResults,
                ));
      }

      // تحديث حالة التحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المشتريات

      // تحديث حالة التحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    }
  }

  /// التحقق من اتساق بيانات المخزون
  Future<void> checkInventoryReportConsistency() async {
    // تحديث حالة التحقق
    _ref.read(reportConsistencyStateProvider.notifier).state =
        ReportConsistencyState(
      isChecking: true,
      totalChecks: 0,
      completedChecks: 0,
      inconsistentItems: 0,
      inconsistencyResults: [],
    );

    try {
      // الحصول على بيانات المخزون
      final inventoryData = await _getInventoryData();

      // تحديث إجمالي عدد العناصر للتحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                totalChecks: inventoryData.length,
              ));

      // التحقق من اتساق كل عنصر
      int inconsistentCount = 0;
      List<Map<String, dynamic>> inconsistencyResults = [];

      for (var product in inventoryData) {
        // التحقق من اتساق بيانات المخزون
        final isConsistent = await _verifyInventoryConsistency(product);

        // إذا كانت البيانات غير متسقة، إضافتها إلى النتائج
        if (!isConsistent.isConsistent) {
          inconsistentCount++;
          inconsistencyResults.add({
            'type': 'inventory',
            'id': product.productCode,
            'name': product.productName,
            'details': isConsistent.details,
            'data': product,
          });
        }

        // تحديث عدد العناصر المكتملة
        _ref
            .read(reportConsistencyStateProvider.notifier)
            .update((state) => state.copyWith(
                  completedChecks: state.completedChecks + 1,
                  inconsistentItems: inconsistentCount,
                  inconsistencyResults: inconsistencyResults,
                ));
      }

      // تحديث حالة التحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المخزون

      // تحديث حالة التحقق
      _ref
          .read(reportConsistencyStateProvider.notifier)
          .update((state) => state.copyWith(
                isChecking: false,
              ));
    }
  }

  /// الحصول على بيانات المبيعات
  Future<List<SalesTransitionModel>> _getSalesData() async {
    List<SalesTransitionModel> salesData = [];

    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        for (var child in snapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));
          salesData.add(SalesTransitionModel.fromJson(data));
        }
      }
    } catch (e) {
      // خطأ في الحصول على بيانات المبيعات
    }

    return salesData;
  }

  /// الحصول على بيانات المشتريات
  Future<List<dynamic>> _getPurchaseData() async {
    List<dynamic> purchaseData = [];

    try {
      final ref =
          FirebaseDatabase.instance.ref('$constUserId/Purchase Transition');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        for (var child in snapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));
          purchaseData.add(data);
        }
      }
    } catch (e) {
      // خطأ في الحصول على بيانات المشتريات
    }

    return purchaseData;
  }

  /// الحصول على بيانات المخزون
  Future<List<ProductModel>> _getInventoryData() async {
    List<ProductModel> inventoryData = [];

    try {
      final ref = FirebaseDatabase.instance.ref('$constUserId/Products');
      final snapshot = await ref.get();

      if (snapshot.exists) {
        for (var child in snapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));
          inventoryData.add(ProductModel.fromJson(data));
        }
      }
    } catch (e) {
      // خطأ في الحصول على بيانات المخزون
    }

    return inventoryData;
  }

  /// التحقق من اتساق بيانات المبيعات
  Future<ConsistencyResult> _verifySaleConsistency(
      SalesTransitionModel sale) async {
    try {
      // التحقق من اتساق المبلغ الإجمالي
      double calculatedTotal = 0;

      if (sale.productList != null) {
        for (var product in sale.productList!) {
          double price = double.tryParse(product.subTotal) ?? 0;
          calculatedTotal += price;
        }
      }

      double storedTotal = sale.totalAmount?.toDouble() ?? 0;

      // التحقق من اتساق المبلغ الإجمالي
      if ((calculatedTotal - storedTotal).abs() > 0.01) {
        return ConsistencyResult(
          isConsistent: false,
          details:
              'المبلغ الإجمالي غير متسق. المحسوب: $calculatedTotal، المخزن: $storedTotal',
        );
      }

      return ConsistencyResult(isConsistent: true);
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المبيعات
      return ConsistencyResult(
        isConsistent: false,
        details: 'حدث خطأ أثناء التحقق: $e',
      );
    }
  }

  /// التحقق من اتساق بيانات المشتريات
  Future<ConsistencyResult> _verifyPurchaseConsistency(dynamic purchase) async {
    try {
      // التحقق من اتساق المبلغ الإجمالي
      double calculatedTotal = 0;

      if (purchase['productList'] != null) {
        for (var product in purchase['productList']) {
          double price = double.tryParse(product['subTotal'].toString()) ?? 0;
          calculatedTotal += price;
        }
      }

      double storedTotal =
          double.tryParse(purchase['totalAmount'].toString()) ?? 0;

      // التحقق من اتساق المبلغ الإجمالي
      if ((calculatedTotal - storedTotal).abs() > 0.01) {
        return ConsistencyResult(
          isConsistent: false,
          details:
              'المبلغ الإجمالي غير متسق. المحسوب: $calculatedTotal، المخزن: $storedTotal',
        );
      }

      return ConsistencyResult(isConsistent: true);
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المشتريات
      return ConsistencyResult(
        isConsistent: false,
        details: 'حدث خطأ أثناء التحقق: $e',
      );
    }
  }

  /// التحقق من اتساق بيانات المخزون
  Future<ConsistencyResult> _verifyInventoryConsistency(
      ProductModel product) async {
    try {
      // التحقق من اتساق المخزون مع حركات البيع والشراء
      int calculatedStock = await _calculateProductStock(product.productCode);
      int storedStock = int.tryParse(product.productStock) ?? 0;

      // التحقق من اتساق المخزون
      if (calculatedStock != storedStock) {
        return ConsistencyResult(
          isConsistent: false,
          details:
              'المخزون غير متسق. المحسوب: $calculatedStock، المخزن: $storedStock',
        );
      }

      return ConsistencyResult(isConsistent: true);
    } catch (e) {
      // خطأ في التحقق من اتساق بيانات المخزون
      return ConsistencyResult(
        isConsistent: false,
        details: 'حدث خطأ أثناء التحقق: $e',
      );
    }
  }

  /// حساب مخزون المنتج من حركات البيع والشراء
  Future<int> _calculateProductStock(String productCode) async {
    int stock = 0;

    try {
      // حساب المشتريات
      final purchaseRef =
          FirebaseDatabase.instance.ref('$constUserId/Purchase Transition');
      final purchaseSnapshot = await purchaseRef.get();

      if (purchaseSnapshot.exists) {
        for (var child in purchaseSnapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));

          if (data['productList'] != null) {
            for (var product in data['productList']) {
              if (product['productCode'] == productCode) {
                stock += int.tryParse(product['quantity'].toString()) ?? 0;
              }
            }
          }
        }
      }

      // حساب المبيعات
      final salesRef =
          FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
      final salesSnapshot = await salesRef.get();

      if (salesSnapshot.exists) {
        for (var child in salesSnapshot.children) {
          var data = jsonDecode(jsonEncode(child.value));

          if (data['productList'] != null) {
            for (var product in data['productList']) {
              if (product['productCode'] == productCode) {
                stock -= int.tryParse(product['quantity'].toString()) ?? 0;
              }
            }
          }
        }
      }
    } catch (e) {
      // خطأ في حساب مخزون المنتج
    }

    return stock;
  }
}

/// نتيجة التحقق من اتساق البيانات
class ConsistencyResult {
  final bool isConsistent;
  final String details;

  ConsistencyResult({
    required this.isConsistent,
    this.details = '',
  });
}

/// حالة التحقق من اتساق البيانات
class ReportConsistencyState {
  final bool isChecking;
  final int totalChecks;
  final int completedChecks;
  final int inconsistentItems;
  final List<Map<String, dynamic>> inconsistencyResults;

  ReportConsistencyState({
    required this.isChecking,
    required this.totalChecks,
    required this.completedChecks,
    required this.inconsistentItems,
    required this.inconsistencyResults,
  });

  ReportConsistencyState copyWith({
    bool? isChecking,
    int? totalChecks,
    int? completedChecks,
    int? inconsistentItems,
    List<Map<String, dynamic>>? inconsistencyResults,
  }) {
    return ReportConsistencyState(
      isChecking: isChecking ?? this.isChecking,
      totalChecks: totalChecks ?? this.totalChecks,
      completedChecks: completedChecks ?? this.completedChecks,
      inconsistentItems: inconsistentItems ?? this.inconsistentItems,
      inconsistencyResults: inconsistencyResults ?? this.inconsistencyResults,
    );
  }
}
