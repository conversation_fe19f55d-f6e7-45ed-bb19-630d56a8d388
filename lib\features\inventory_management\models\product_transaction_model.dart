// بسم الله الرحمن الرحيم
// نموذج معاملة المنتج - يمثل بيانات معاملة بيع أو شراء للمنتج

import 'dart:convert';

/// نوع المعاملة
enum TransactionType {
  /// بيع
  sale,
  /// شراء
  purchase,
  /// مرتجع بيع
  saleReturn,
  /// مرتجع شراء
  purchaseReturn,
}

/// نموذج معاملة المنتج
class ProductTransactionModel {
  /// ينشئ نموذج معاملة المنتج
  const ProductTransactionModel({
    required this.id,
    required this.productCode,
    required this.productName,
    required this.transactionType,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    required this.date,
    required this.invoiceNumber,
    required this.customerName,
    this.customerPhone,
    this.paymentType,
    this.notes,
    this.warehouseId,
    this.warehouseName,
  });

  /// معرف المعاملة
  final String id;

  /// كود المنتج
  final String productCode;

  /// اسم المنتج
  final String productName;

  /// نوع المعاملة
  final TransactionType transactionType;

  /// الكمية
  final int quantity;

  /// سعر الوحدة
  final double unitPrice;

  /// المبلغ الإجمالي
  final double totalAmount;

  /// تاريخ المعاملة
  final DateTime date;

  /// رقم الفاتورة
  final String invoiceNumber;

  /// اسم العميل/المورد
  final String customerName;

  /// هاتف العميل/المورد (اختياري)
  final String? customerPhone;

  /// نوع الدفع (اختياري)
  final String? paymentType;

  /// ملاحظات (اختياري)
  final String? notes;

  /// معرف المخزن (اختياري)
  final String? warehouseId;

  /// اسم المخزن (اختياري)
  final String? warehouseName;

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productCode': productCode,
      'productName': productName,
      'transactionType': transactionType.index,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalAmount': totalAmount,
      'date': date.millisecondsSinceEpoch,
      'invoiceNumber': invoiceNumber,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'paymentType': paymentType,
      'notes': notes,
      'warehouseId': warehouseId,
      'warehouseName': warehouseName,
    };
  }

  /// إنشاء نموذج من Map
  factory ProductTransactionModel.fromMap(Map<String, dynamic> map) {
    return ProductTransactionModel(
      id: map['id'] ?? '',
      productCode: map['productCode'] ?? '',
      productName: map['productName'] ?? '',
      transactionType: TransactionType.values[map['transactionType'] ?? 0],
      quantity: map['quantity'] ?? 0,
      unitPrice: (map['unitPrice'] ?? 0).toDouble(),
      totalAmount: (map['totalAmount'] ?? 0).toDouble(),
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] ?? 0),
      invoiceNumber: map['invoiceNumber'] ?? '',
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'],
      paymentType: map['paymentType'],
      notes: map['notes'],
      warehouseId: map['warehouseId'],
      warehouseName: map['warehouseName'],
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory ProductTransactionModel.fromJson(String source) =>
      ProductTransactionModel.fromMap(json.decode(source));

  /// إنشاء نسخة من النموذج مع تعديل بعض القيم
  ProductTransactionModel copyWith({
    String? id,
    String? productCode,
    String? productName,
    TransactionType? transactionType,
    int? quantity,
    double? unitPrice,
    double? totalAmount,
    DateTime? date,
    String? invoiceNumber,
    String? customerName,
    String? customerPhone,
    String? paymentType,
    String? notes,
    String? warehouseId,
    String? warehouseName,
  }) {
    return ProductTransactionModel(
      id: id ?? this.id,
      productCode: productCode ?? this.productCode,
      productName: productName ?? this.productName,
      transactionType: transactionType ?? this.transactionType,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
      date: date ?? this.date,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      paymentType: paymentType ?? this.paymentType,
      notes: notes ?? this.notes,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
    );
  }

  @override
  String toString() {
    return 'ProductTransactionModel(id: $id, productCode: $productCode, productName: $productName, transactionType: $transactionType, quantity: $quantity, unitPrice: $unitPrice, totalAmount: $totalAmount, date: $date, invoiceNumber: $invoiceNumber, customerName: $customerName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProductTransactionModel &&
        other.id == id &&
        other.productCode == productCode &&
        other.productName == productName &&
        other.transactionType == transactionType &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice &&
        other.totalAmount == totalAmount &&
        other.date == date &&
        other.invoiceNumber == invoiceNumber &&
        other.customerName == customerName &&
        other.customerPhone == customerPhone &&
        other.paymentType == paymentType &&
        other.notes == notes &&
        other.warehouseId == warehouseId &&
        other.warehouseName == warehouseName;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productCode.hashCode ^
        productName.hashCode ^
        transactionType.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode ^
        totalAmount.hashCode ^
        date.hashCode ^
        invoiceNumber.hashCode ^
        customerName.hashCode ^
        customerPhone.hashCode ^
        paymentType.hashCode ^
        notes.hashCode ^
        warehouseId.hashCode ^
        warehouseName.hashCode;
  }
}

/// نموذج إحصائيات المنتج
class ProductStatistics {
  /// ينشئ نموذج إحصائيات المنتج
  const ProductStatistics({
    required this.totalSalesQuantity,
    required this.totalPurchaseQuantity,
    required this.totalSalesAmount,
    required this.totalPurchaseAmount,
    required this.salesInvoiceCount,
    required this.purchaseInvoiceCount,
    required this.averageSalePrice,
    required this.averagePurchasePrice,
    required this.netProfit,
    required this.profitMargin,
    required this.turnoverRate,
  });

  /// إجمالي الكمية المباعة
  final int totalSalesQuantity;

  /// إجمالي الكمية المشتراة
  final int totalPurchaseQuantity;

  /// إجمالي قيمة المبيعات
  final double totalSalesAmount;

  /// إجمالي قيمة المشتريات
  final double totalPurchaseAmount;

  /// عدد فواتير البيع
  final int salesInvoiceCount;

  /// عدد فواتير الشراء
  final int purchaseInvoiceCount;

  /// متوسط سعر البيع
  final double averageSalePrice;

  /// متوسط سعر الشراء
  final double averagePurchasePrice;

  /// صافي الربح
  final double netProfit;

  /// هامش الربح (بالنسبة المئوية)
  final double profitMargin;

  /// معدل دوران المخزون
  final double turnoverRate;

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'totalSalesQuantity': totalSalesQuantity,
      'totalPurchaseQuantity': totalPurchaseQuantity,
      'totalSalesAmount': totalSalesAmount,
      'totalPurchaseAmount': totalPurchaseAmount,
      'salesInvoiceCount': salesInvoiceCount,
      'purchaseInvoiceCount': purchaseInvoiceCount,
      'averageSalePrice': averageSalePrice,
      'averagePurchasePrice': averagePurchasePrice,
      'netProfit': netProfit,
      'profitMargin': profitMargin,
      'turnoverRate': turnoverRate,
    };
  }

  /// إنشاء نموذج من Map
  factory ProductStatistics.fromMap(Map<String, dynamic> map) {
    return ProductStatistics(
      totalSalesQuantity: map['totalSalesQuantity'] ?? 0,
      totalPurchaseQuantity: map['totalPurchaseQuantity'] ?? 0,
      totalSalesAmount: (map['totalSalesAmount'] ?? 0).toDouble(),
      totalPurchaseAmount: (map['totalPurchaseAmount'] ?? 0).toDouble(),
      salesInvoiceCount: map['salesInvoiceCount'] ?? 0,
      purchaseInvoiceCount: map['purchaseInvoiceCount'] ?? 0,
      averageSalePrice: (map['averageSalePrice'] ?? 0).toDouble(),
      averagePurchasePrice: (map['averagePurchasePrice'] ?? 0).toDouble(),
      netProfit: (map['netProfit'] ?? 0).toDouble(),
      profitMargin: (map['profitMargin'] ?? 0).toDouble(),
      turnoverRate: (map['turnoverRate'] ?? 0).toDouble(),
    );
  }

  @override
  String toString() {
    return 'ProductStatistics(totalSalesQuantity: $totalSalesQuantity, totalPurchaseQuantity: $totalPurchaseQuantity, totalSalesAmount: $totalSalesAmount, totalPurchaseAmount: $totalPurchaseAmount, netProfit: $netProfit, profitMargin: $profitMargin)';
  }
}
