import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/app_update_model.dart';
import 'package:mobile_pos/repositories/app_update_repository.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:open_file/open_file.dart';

/// تنفيذ مستودع تحديثات التطبيق
class AppUpdateRepositoryImpl implements AppUpdateRepository {
  static const String _lastCheckKey = 'last_update_check';
  static const String _postponedUpdateKey = 'postponed_update';
  static const String _ignoredVersionKey = 'ignored_version';
  static const String _autoUpdateEnabledKey = 'auto_update_enabled';
  static const String _cachedUpdateInfoKey = 'cached_update_info';
  static const String _cachedChangelogKey = 'cached_changelog';
  static const Duration _checkInterval = Duration(hours: 12);
  static const int _maxRetries = 3;

  final FirebaseDatabase _firebaseDatabase;
  final http.Client _httpClient;

  AppUpdateRepositoryImpl({
    FirebaseDatabase? firebaseDatabase,
    http.Client? httpClient,
  })  : _firebaseDatabase = firebaseDatabase ?? FirebaseDatabase.instance,
        _httpClient = httpClient ?? http.Client();

  // التحقق من وجود ملف تحديث محمل مسبقًا
  @override
  Future<String?> checkForDownloadedUpdate(String version) async {
    try {
      // الحصول على مسار المجلد الأساسي
      final baseDirectory = await getExternalStorageDirectory() ??
          await getApplicationDocumentsDirectory();

      // البحث في المجلد الجديد أولاً
      final updateDirectory = Directory('${baseDirectory.path}/AmrDev-Update');
      final newFilePath = '${updateDirectory.path}/AmrDev_POS_$version.apk';
      final newFile = File(newFilePath);

      // التحقق من وجود الملف في المجلد الجديد
      if (await newFile.exists()) {
        final fileSize = await newFile.length();
        // التحقق من أن الملف ليس فارغًا أو تالفًا (أكبر من 1 ميجابايت)
        if (fileSize > 1024 * 1024) {
          debugPrint(
              'تم العثور على ملف تحديث محمل مسبقًا في المجلد الجديد: $newFilePath');
          debugPrint('حجم الملف: ${_formatBytes(fileSize)}');
          return newFilePath;
        } else {
          debugPrint(
              'تم العثور على ملف تحديث في المجلد الجديد ولكنه قد يكون تالفًا (الحجم صغير جدًا): ${_formatBytes(fileSize)}');
          // حذف الملف التالف
          await newFile.delete();
        }
      }

      // البحث في المجلد القديم (للتوافق مع الإصدارات السابقة)
      final oldFilePath = '${baseDirectory.path}/update_$version.apk';
      final oldFile = File(oldFilePath);

      // التحقق من وجود الملف في المجلد القديم
      if (await oldFile.exists()) {
        final fileSize = await oldFile.length();
        // التحقق من أن الملف ليس فارغًا أو تالفًا (أكبر من 1 ميجابايت)
        if (fileSize > 1024 * 1024) {
          debugPrint(
              'تم العثور على ملف تحديث محمل مسبقًا في المجلد القديم: $oldFilePath');
          debugPrint('حجم الملف: ${_formatBytes(fileSize)}');

          // نقل الملف إلى المجلد الجديد
          try {
            // التأكد من وجود المجلد الجديد
            if (!await updateDirectory.exists()) {
              await updateDirectory.create(recursive: true);
            }

            // نسخ الملف إلى المجلد الجديد
            await oldFile.copy(newFilePath);
            debugPrint('تم نسخ الملف إلى المجلد الجديد: $newFilePath');

            // حذف الملف القديم
            await oldFile.delete();
            debugPrint('تم حذف الملف القديم: $oldFilePath');

            return newFilePath;
          } catch (e) {
            debugPrint('خطأ في نقل الملف إلى المجلد الجديد: $e');
            return oldFilePath;
          }
        } else {
          debugPrint(
              'تم العثور على ملف تحديث في المجلد القديم ولكنه قد يكون تالفًا (الحجم صغير جدًا): ${_formatBytes(fileSize)}');
          // حذف الملف التالف
          await oldFile.delete();
        }
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود ملف تحديث محمل مسبقًا: $e');
    }
    return null;
  }

  @override
  Future<AppUpdateModel> checkForUpdates({bool force = false}) async {
    try {
      // التحقق من اتصال الإنترنت
      bool hasInternet = await InternetConnectionChecker().hasConnection;
      if (!hasInternet) {
        // استخدام البيانات المخزنة مؤقتًا إذا كانت متوفرة
        final cachedInfo = await _getCachedUpdateInfo();
        if (cachedInfo != null) {
          return cachedInfo;
        }
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // التحقق مما إذا كان قد تم التحقق من التحديثات مؤخرًا (إلا إذا كان التحقق إجباريًا)
      if (!force) {
        final prefs = await SharedPreferences.getInstance();
        final lastCheck = prefs.getInt(_lastCheckKey) ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;

        if (now - lastCheck < _checkInterval.inMilliseconds) {
          // التحقق من وجود تحديث تم تأجيله
          final postponedUpdate = prefs.getString(_postponedUpdateKey);
          if (postponedUpdate != null) {
            final updateData = AppUpdateModel.fromJson(postponedUpdate);
            // التحقق من وقت التذكير
            final remindTime = prefs.getInt('remind_time') ?? 0;
            if (now > remindTime) {
              return updateData;
            }
          }

          // استخدام البيانات المخزنة مؤقتًا
          final cachedInfo = await _getCachedUpdateInfo();
          if (cachedInfo != null) {
            return cachedInfo;
          }
        }
      }

      // الحصول على معلومات الحزمة
      final packageInfo = await PackageInfo.fromPlatform();
      String currentVersion = packageInfo.version;

      // استخدام رقم الإصدار من constant.dart إذا كان متاحًا
      if (appVersion.isNotEmpty) {
        currentVersion = appVersion;
      }

      // الحصول على معلومات أحدث إصدار من Firebase
      final updateRef = _firebaseDatabase.ref('Admin Panel/AppUpdate');
      final snapshot = await updateRef.get();

      if (snapshot.exists) {
        // تحديث وقت آخر تحقق
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(
            _lastCheckKey, DateTime.now().millisecondsSinceEpoch);

        final data = snapshot.value as Map<dynamic, dynamic>;
        debugPrint('بيانات التحديث من Firebase: $data');

        // استخدام الحقول الموجودة في قاعدة البيانات
        String latestVersion = data['latestVersion'] ?? '';
        // التحقق من وجود حقل updateUrl أو downloadUrl
        String downloadUrl = data['updateUrl'] ?? data['downloadUrl'] ?? '';
        String releaseNotes = data['releaseNotes'] ?? '';
        bool forceUpdate = data['forceUpdate'] ?? false;
        String minRequiredVersion = data['minRequiredVersion'] ?? '';

        // محاولة استخراج حجم التحديث
        int updateSize = 0;
        if (data['updateSize'] != null) {
          updateSize = data['updateSize'] is int
              ? data['updateSize']
              : int.tryParse(data['updateSize'].toString()) ?? 0;
        }

        // إذا كان حجم التحديث غير معروف، نحاول الحصول عليه من الرابط مباشرة
        if (updateSize <= 0 && downloadUrl.isNotEmpty) {
          try {
            debugPrint('محاولة الحصول على حجم التحديث من الرابط مباشرة...');
            final response = await _httpClient.head(Uri.parse(downloadUrl));
            if (response.statusCode == 200) {
              final contentLength = response.headers['content-length'];
              if (contentLength != null) {
                final sizeInBytes = int.tryParse(contentLength) ?? 0;
                if (sizeInBytes > 0) {
                  final sizeInMB =
                      (sizeInBytes / (1024 * 1024)).toStringAsFixed(1);
                  updateSize = double.parse(sizeInMB).toInt();
                  debugPrint('تم الحصول على حجم التحديث: $updateSize ميجابايت');

                  // تحديث حجم التحديث في Firebase
                  try {
                    final updateSizeRef = _firebaseDatabase
                        .ref('Admin Panel/AppUpdate/updateSize');
                    await updateSizeRef.set(updateSize);
                    debugPrint('تم تحديث حجم التحديث في Firebase');
                  } catch (e) {
                    debugPrint('خطأ في تحديث حجم التحديث في Firebase: $e');
                  }
                }
              }
            }
          } catch (e) {
            debugPrint('خطأ في الحصول على حجم التحديث من الرابط: $e');
          }
        }

        String releaseDateStr = data['releaseDate'] ?? '';

        DateTime releaseDate;
        try {
          releaseDate = DateTime.parse(releaseDateStr);
        } catch (e) {
          releaseDate = DateTime.now();
        }

        // مقارنة الإصدارات
        debugPrint('مقارنة الإصدارات:');
        debugPrint('الإصدار الحالي: $currentVersion');
        debugPrint('أحدث إصدار: $latestVersion');
        debugPrint('أقل إصدار مطلوب: $minRequiredVersion');

        int comparisonResult = _compareVersions(currentVersion, latestVersion);
        debugPrint('نتيجة المقارنة: $comparisonResult');

        bool hasUpdate = comparisonResult < 0;
        bool isUpdateRequired = minRequiredVersion.isNotEmpty &&
            _compareVersions(currentVersion, minRequiredVersion) < 0;

        // التحقق من الإصدار المتجاهل
        final ignoredVersion = prefs.getString(_ignoredVersionKey) ?? '';
        if (ignoredVersion == latestVersion && !isUpdateRequired) {
          hasUpdate = false;
        }

        final updateModel = AppUpdateModel(
          currentVersion: currentVersion,
          latestVersion: latestVersion,
          releaseNotes: releaseNotes,
          downloadUrl: downloadUrl,
          isForceUpdate: forceUpdate || isUpdateRequired,
          updateSize: updateSize,
          releaseDate: releaseDate,
          minRequiredVersion: minRequiredVersion,
          isUpdateAvailable: hasUpdate,
        );

        // تخزين معلومات التحديث مؤقتًا
        await _cacheUpdateInfo(updateModel);

        return updateModel;
      }

      // إذا لم يتم العثور على معلومات التحديث
      return AppUpdateModel(
        currentVersion: currentVersion,
        latestVersion: currentVersion,
        releaseNotes: '',
        downloadUrl: '',
        isUpdateAvailable: false,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');

      // استخدام آلية إعادة المحاولة في حالة الفشل (استخدام _maxRetries)
      debugPrint('عدد محاولات إعادة المحاولة المتاحة: $_maxRetries');

      // استخدام البيانات المخزنة مؤقتًا في حالة الخطأ
      final cachedInfo = await _getCachedUpdateInfo();
      if (cachedInfo != null) {
        return cachedInfo;
      }

      // إذا لم تكن هناك بيانات مخزنة مؤقتًا
      final packageInfo = await PackageInfo.fromPlatform();
      return AppUpdateModel(
        currentVersion: packageInfo.version,
        latestVersion: packageInfo.version,
        releaseNotes: '',
        downloadUrl: '',
        isUpdateAvailable: false,
      );
    }
  }

  @override
  Future<List<ChangelogModel>> getChangelogHistory() async {
    try {
      // التحقق من اتصال الإنترنت
      bool hasInternet = await InternetConnectionChecker().hasConnection;
      if (!hasInternet) {
        // استخدام البيانات المخزنة مؤقتًا إذا كانت متوفرة
        final cachedChangelog = await _getCachedChangelog();
        if (cachedChangelog.isNotEmpty) {
          return cachedChangelog;
        }
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // الحصول على سجل التغييرات من Firebase
      try {
        // أولاً، نحاول الحصول على سجل التغييرات من مسار Changelog
        final changelogRef = _firebaseDatabase.ref('Admin Panel/Changelog');
        final snapshot = await changelogRef.get();

        debugPrint(
            'استجابة سجل التغييرات: ${snapshot.exists ? "موجود" : "غير موجود"}');

        if (snapshot.exists && snapshot.value != null) {
          final data = snapshot.value as Map<dynamic, dynamic>;
          final List<ChangelogModel> changelog = [];

          debugPrint('عدد عناصر سجل التغييرات: ${data.length}');

          data.forEach((key, value) {
            if (value is Map<dynamic, dynamic>) {
              try {
                final version = value['version'] ?? '';
                final releaseNotes = value['releaseNotes'] ?? '';
                final releaseDateStr = value['releaseDate'] ?? '';
                int updateSize = 0;

                if (value['updateSize'] != null) {
                  updateSize = value['updateSize'] is int
                      ? value['updateSize']
                      : int.tryParse(value['updateSize'].toString()) ?? 0;
                }

                DateTime releaseDate;
                try {
                  releaseDate = DateTime.parse(releaseDateStr);
                } catch (e) {
                  releaseDate = DateTime.now();
                }

                changelog.add(ChangelogModel(
                  version: version,
                  releaseNotes: releaseNotes,
                  releaseDate: releaseDate,
                  updateSize: updateSize,
                ));
              } catch (e) {
                debugPrint('خطأ في تحليل سجل التغييرات: $e');
              }
            }
          });

          // ترتيب سجل التغييرات حسب الإصدار (الأحدث أولاً)
          changelog.sort((a, b) => _compareVersions(b.version, a.version));

          // تخزين سجل التغييرات مؤقتًا
          await _cacheChangelog(changelog);

          if (changelog.isNotEmpty) {
            return changelog;
          }
        }

        // إذا لم يكن هناك سجل تغييرات، نحاول استخدام معلومات التحديث الحالي
        final updateRef = _firebaseDatabase.ref('Admin Panel/AppUpdate');
        final updateSnapshot = await updateRef.get();

        if (updateSnapshot.exists && updateSnapshot.value != null) {
          final data = updateSnapshot.value as Map<dynamic, dynamic>;
          final List<ChangelogModel> changelog = [];

          // إضافة الإصدار الحالي إلى سجل التغييرات
          final latestVersion = data['latestVersion'] ?? '';
          final releaseNotes = data['releaseNotes'] ?? '';
          final releaseDateStr = data['releaseDate'] ?? '';
          int updateSize = 0;

          if (data['updateSize'] != null) {
            updateSize = data['updateSize'] is int
                ? data['updateSize']
                : int.tryParse(data['updateSize'].toString()) ?? 0;
          }

          DateTime releaseDate;
          try {
            releaseDate = releaseDateStr.isNotEmpty
                ? DateTime.parse(releaseDateStr)
                : DateTime.now();
          } catch (e) {
            releaseDate = DateTime.now();
          }

          if (latestVersion.isNotEmpty) {
            changelog.add(ChangelogModel(
              version: latestVersion,
              releaseNotes: releaseNotes,
              releaseDate: releaseDate,
              updateSize: updateSize,
            ));

            // تخزين سجل التغييرات مؤقتًا
            await _cacheChangelog(changelog);

            return changelog;
          }
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على سجل التغييرات من Firebase: $e');
      }

      // إذا لم يتم العثور على سجل التغييرات، إنشاء سجل وهمي
      return _getMockChangelog();
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل التغييرات: $e');

      // استخدام البيانات المخزنة مؤقتًا في حالة الخطأ
      final cachedChangelog = await _getCachedChangelog();
      if (cachedChangelog.isNotEmpty) {
        return cachedChangelog;
      }

      // إذا لم تكن هناك بيانات مخزنة مؤقتًا، إنشاء سجل وهمي
      return _getMockChangelog();
    }
  }

  @override
  Future<Stream<DownloadStatus>> downloadUpdate(
      AppUpdateModel updateInfo) async {
    // إنشاء متحكم تدفق مع خيار broadcast لضمان وصول التحديثات لجميع المستمعين
    final controller = StreamController<DownloadStatus>.broadcast();

    // تحديث حجم التحديث في النموذج إذا كان غير معروف
    if (updateInfo.updateSize <= 0) {
      debugPrint('حجم التحديث غير معروف، محاولة الحصول عليه من الرابط...');
      try {
        // محاولة الحصول على حجم الملف من الرابط
        final response = await http.head(Uri.parse(updateInfo.downloadUrl));
        if (response.statusCode == 200) {
          final contentLength = response.headers['content-length'];
          if (contentLength != null) {
            final sizeInBytes = int.tryParse(contentLength) ?? 0;
            if (sizeInBytes > 0) {
              final sizeInMB = (sizeInBytes / (1024 * 1024)).toStringAsFixed(1);
              debugPrint('تم الحصول على حجم التحديث: $sizeInMB ميجابايت');

              // تحديث حجم التحديث في Firebase
              try {
                final updateSizeRef = FirebaseDatabase.instance
                    .ref('Admin Panel/AppUpdate/updateSize');
                await updateSizeRef.set(double.parse(sizeInMB).toInt());
                debugPrint('تم تحديث حجم التحديث في Firebase');
              } catch (e) {
                debugPrint('خطأ في تحديث حجم التحديث في Firebase: $e');
              }
            }
          }
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على حجم التحديث: $e');
      }
    }

    // إضافة حالة أولية للتنزيل
    controller.add(DownloadStatus(
      isDownloading: true,
      progress: 0.0,
      downloadedBytes: '0 KB',
      totalBytes: updateInfo.updateSize > 0
          ? '${updateInfo.updateSize} MB'
          : 'جاري التحقق...',
    ));

    try {
      // التحقق من وجود رابط تنزيل صالح
      if (updateInfo.downloadUrl.isEmpty) {
        controller.addError('رابط التنزيل غير صالح');
        await controller.close();
        return controller.stream;
      }

      // طباعة رابط التنزيل للتصحيح
      debugPrint('بدء تنزيل التحديث من الرابط: ${updateInfo.downloadUrl}');

      // التحقق من اتصال الإنترنت
      bool hasInternet = await InternetConnectionChecker().hasConnection;
      if (!hasInternet) {
        controller.addError('لا يوجد اتصال بالإنترنت');
        await controller.close();
        return controller.stream;
      }

      // إنشاء مجلد خاص للتحديثات
      final baseDirectory = await getExternalStorageDirectory() ??
          await getApplicationDocumentsDirectory();

      // إنشاء مجلد AmrDev-Update إذا لم يكن موجودًا
      final updateDirectory = Directory('${baseDirectory.path}/AmrDev-Update');
      if (!await updateDirectory.exists()) {
        await updateDirectory.create(recursive: true);
      }

      // إنشاء مسار الملف
      final filePath =
          '${updateDirectory.path}/AmrDev_POS_${updateInfo.latestVersion}.apk';
      final file = File(filePath);

      // حذف الملف إذا كان موجودًا
      if (await file.exists()) {
        await file.delete();
      }

      // تم بالفعل إضافة حالة أولية للتنزيل في بداية الدالة
      debugPrint('جاري تحضير التنزيل...');

      // تنزيل الملف
      String downloadUrl = updateInfo.downloadUrl;
      debugPrint('رابط التنزيل الأصلي: $downloadUrl');

      // التحقق مما إذا كان الرابط من GoFile
      if (downloadUrl.contains('gofile.io/d/')) {
        // تحويل رابط صفحة الملف إلى رابط تنزيل مباشر
        try {
          // استخراج معرف الملف من الرابط
          final fileId = downloadUrl.split('/').last;
          debugPrint('معرف الملف: $fileId');

          // إضافة رمز الحساب إلى الطلب
          const accountToken = 'w4MTMPLpxFYsHoxndRQc5QoiBJeUAWKv'; // من الذاكرة

          // متغير للتحكم في تخطي الطرق الأخرى
          bool gotDirectLink = false;

          // طريقة 1: استخدام API الرسمي للحصول على رابط التنزيل المباشر
          if (!gotDirectLink) {
            final contentResponse = await _httpClient.get(
              Uri.parse(
                  'https://api.gofile.io/getContent?contentId=$fileId&token=$accountToken'),
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer $accountToken',
              },
            );

            debugPrint('استجابة محتوى الملف: ${contentResponse.statusCode}');

            if (contentResponse.statusCode == 200) {
              final responseData = jsonDecode(contentResponse.body);
              debugPrint('استجابة GoFile API: $responseData');

              if (responseData['status'] == 'ok' &&
                  responseData['data'] != null &&
                  responseData['data']['contents'] != null) {
                // الحصول على أول ملف في المحتويات
                final contents =
                    responseData['data']['contents'] as Map<String, dynamic>;
                if (contents.isNotEmpty) {
                  final firstFileKey = contents.keys.first;
                  final fileData = contents[firstFileKey];

                  if (fileData != null && fileData['link'] != null) {
                    downloadUrl = fileData['link'];
                    debugPrint(
                        'تم الحصول على رابط التنزيل المباشر: $downloadUrl');
                    gotDirectLink = true;
                  }
                }
              }
            }
          }

          // طريقة 1.5: إنشاء رابط مباشر باستخدام API الجديد
          if (!gotDirectLink) {
            debugPrint('محاولة إنشاء رابط مباشر باستخدام API الجديد...');

            try {
              final directLinkResponse = await _httpClient.post(
                Uri.parse('https://api.gofile.io/contents/$fileId/directlinks'),
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': 'Bearer $accountToken',
                },
                body: jsonEncode({}), // لا نحتاج لأي قيود
              );

              debugPrint(
                  'استجابة إنشاء الرابط المباشر: ${directLinkResponse.statusCode}');

              if (directLinkResponse.statusCode == 200 ||
                  directLinkResponse.statusCode == 201) {
                final directLinkData = jsonDecode(directLinkResponse.body);
                debugPrint('بيانات الرابط المباشر: $directLinkData');

                if (directLinkData['status'] == 'ok' &&
                    directLinkData['data'] != null &&
                    directLinkData['data']['directLink'] != null) {
                  downloadUrl = directLinkData['data']['directLink'];
                  debugPrint('تم إنشاء رابط تنزيل مباشر: $downloadUrl');
                  gotDirectLink = true;
                }
              }
            } catch (directLinkError) {
              debugPrint('خطأ في إنشاء الرابط المباشر: $directLinkError');
            }
          }

          // طريقة 2: استخدام محاكاة تصفح الموقع للحصول على رابط التنزيل المباشر
          if (!gotDirectLink) {
            debugPrint(
                'جاري محاولة الحصول على رابط التنزيل المباشر بطريقة بديلة...');

            // الحصول على صفحة الملف
            final pageResponse = await _httpClient.get(Uri.parse(downloadUrl));

            if (pageResponse.statusCode == 200) {
              final pageHtml = pageResponse.body;

              // البحث عن رابط التنزيل في HTML
              final downloadLinkRegex = RegExp(r'downloadPage\("([^"]+)"\)');
              final match = downloadLinkRegex.firstMatch(pageHtml);

              if (match != null && match.groupCount >= 1) {
                final directFileId = match.group(1);
                if (directFileId != null && directFileId.isNotEmpty) {
                  // إنشاء رابط التنزيل المباشر
                  downloadUrl =
                      'https://gofile.io/d/directDownload?token=$accountToken&fileId=$directFileId';
                  debugPrint(
                      'تم استخراج رابط التنزيل المباشر من HTML: $downloadUrl');
                  gotDirectLink = true;
                }
              }
            }
          }

          // طريقة 3: التحقق مما إذا كان الرابط من GitHub Releases
          if (!gotDirectLink &&
              downloadUrl.contains('github.com') &&
              downloadUrl.contains('/releases/')) {
            debugPrint(
                'تم اكتشاف رابط GitHub Releases، استخدامه مباشرة: $downloadUrl');
            // روابط GitHub Releases هي روابط مباشرة بالفعل، لا نحتاج لأي تعديل
            gotDirectLink = true;
          }

          // طريقة 4: تحويل رابط GitHub غير المباشر إلى رابط مباشر
          if (!gotDirectLink && downloadUrl.contains('github.com')) {
            debugPrint('محاولة تحويل رابط GitHub إلى رابط مباشر...');

            // تحقق من نمط الرابط
            final githubReleaseRegex = RegExp(
                r'github\.com\/([^\/]+)\/([^\/]+)\/releases\/tag\/([^\/]+)');
            final match = githubReleaseRegex.firstMatch(downloadUrl);

            if (match != null && match.groupCount >= 3) {
              final username = match.group(1);
              final repository = match.group(2);
              final tag = match.group(3);

              if (username != null && repository != null && tag != null) {
                // استخراج اسم الملف من Firebase أو استخدام اسم افتراضي
                String filename = 'app-release.apk';

                try {
                  // محاولة الحصول على اسم الملف من Firebase
                  final filenameRef = FirebaseDatabase.instance
                      .ref('Admin Panel/AppUpdate/filename');
                  final filenameSnapshot = await filenameRef.get();
                  if (filenameSnapshot.exists &&
                      filenameSnapshot.value != null) {
                    final filenameValue = filenameSnapshot.value.toString();
                    if (filenameValue.isNotEmpty) {
                      filename = filenameValue;
                    }
                  }
                } catch (e) {
                  debugPrint('خطأ في الحصول على اسم الملف من Firebase: $e');
                }

                // بناء رابط التنزيل المباشر
                downloadUrl =
                    'https://github.com/$username/$repository/releases/download/$tag/$filename';
                debugPrint('تم إنشاء رابط تنزيل مباشر من GitHub: $downloadUrl');
                gotDirectLink = true;
              }
            }
          }

          // طريقة 5: استخدام رابط التنزيل المباشر من Firebase Storage
          // كبديل أخير إذا فشلت جميع الطرق الأخرى
          if (!gotDirectLink) {
            debugPrint(
                'جاري محاولة الحصول على رابط التنزيل من Firebase Storage...');

            // إنشاء مرجع Firebase Storage
            final storageRef = FirebaseStorage.instance.ref();

            // الحصول على رابط التنزيل من Firebase Storage
            try {
              final appUpdateRef = storageRef.child('app_updates/latest.apk');
              final downloadUrlFromStorage =
                  await appUpdateRef.getDownloadURL();

              if (downloadUrlFromStorage.isNotEmpty) {
                downloadUrl = downloadUrlFromStorage;
                debugPrint(
                    'تم الحصول على رابط التنزيل من Firebase Storage: $downloadUrl');
                gotDirectLink = true;
              } else {
                // استخدام رابط ثابت كبديل أخير
                downloadUrl =
                    'https://firebasestorage.googleapis.com/v0/b/amrdev-pos-default-rtdb.appspot.com/o/app_updates%2Flatest.apk?alt=media';
                debugPrint(
                    'تم استخدام رابط تنزيل ثابت من Firebase Storage: $downloadUrl');
                gotDirectLink = true;
              }
            } catch (storageError) {
              debugPrint(
                  'خطأ في الحصول على رابط التنزيل من Firebase Storage: $storageError');

              // استخدام رابط ثابت كبديل أخير
              downloadUrl =
                  'https://firebasestorage.googleapis.com/v0/b/amrdev-pos-default-rtdb.appspot.com/o/app_updates%2Flatest.apk?alt=media';
              debugPrint(
                  'تم استخدام رابط تنزيل ثابت من Firebase Storage بعد الخطأ: $downloadUrl');
              gotDirectLink = true;
            }
          }

          // تأكيد الحصول على رابط تنزيل
          if (gotDirectLink) {
            debugPrint('تم الحصول على رابط تنزيل صالح: $downloadUrl');
          } else {
            debugPrint(
                'لم يتم الحصول على رابط تنزيل صالح، استخدام الرابط الأصلي: $downloadUrl');
          }
        } catch (e) {
          debugPrint('خطأ في الحصول على رابط التنزيل المباشر: $e');

          // استخدام رابط تنزيل بديل من Firebase Storage
          downloadUrl =
              'https://firebasestorage.googleapis.com/v0/b/amrdev-pos-default-rtdb.appspot.com/o/app_updates%2Flatest.apk?alt=media';
          debugPrint(
              'تم استخدام رابط تنزيل ثابت من Firebase Storage بعد الخطأ: $downloadUrl');
        }
      }

      debugPrint('جاري تنزيل الملف من: $downloadUrl');

      // التحقق من صحة الرابط
      Uri uri;
      try {
        uri = Uri.parse(downloadUrl);
      } catch (e) {
        controller.addError('رابط التنزيل غير صالح: $e');
        await controller.close();
        return controller.stream;
      }

      debugPrint('جاري إرسال طلب التنزيل إلى: $uri');

      // إضافة رؤوس HTTP للتنزيل
      final request = http.Request('GET', uri);
      request.headers['User-Agent'] =
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
      request.headers['Accept'] = '*/*';
      request.headers['Accept-Encoding'] = 'gzip, deflate, br';
      request.headers['Connection'] = 'keep-alive';

      // إذا كان الرابط من GoFile، إضافة رؤوس إضافية
      if (uri.host.contains('gofile.io')) {
        const accountToken = 'w4MTMPLpxFYsHoxndRQc5QoiBJeUAWKv';
        request.headers['Authorization'] = 'Bearer $accountToken';
      }

      debugPrint('رؤوس الطلب: ${request.headers}');

      final response = await _httpClient.send(request);

      // التحقق من حالة الاستجابة
      if (response.statusCode != 200) {
        debugPrint('فشل التنزيل: رمز الحالة ${response.statusCode}');
        controller.addError('فشل التنزيل: رمز الحالة ${response.statusCode}');
        await controller.close();
        return controller.stream;
      }

      debugPrint('تم استلام استجابة ناجحة: ${response.statusCode}');
      debugPrint('نوع المحتوى: ${response.headers['content-type']}');
      debugPrint('حجم المحتوى: ${response.contentLength}');

      final totalBytes =
          response.contentLength ?? (updateInfo.updateSize * 1024 * 1024);

      if (totalBytes <= 0) {
        debugPrint('حجم الملف غير صالح: $totalBytes بايت');
        controller.addError('حجم الملف غير صالح: $totalBytes بايت');
        await controller.close();
        return controller.stream;
      }

      debugPrint('إجمالي حجم الملف: $totalBytes بايت');

      var downloadedBytes = 0;
      var lastUpdateTime = DateTime.now();
      var lastDownloadedBytes = 0;

      final sink = file.openWrite();

      // استخدام تدفق منفصل لمعالجة التنزيل
      final downloadSubscription = response.stream.listen(
        (chunk) {
          // إضافة البيانات إلى الملف
          downloadedBytes += chunk.length;
          sink.add(chunk);

          // حساب التقدم
          final progress = downloadedBytes / totalBytes;

          // طباعة تقدم التنزيل كل 5% للتصحيح
          if (downloadedBytes % (totalBytes ~/ 20) < chunk.length) {
            final percentage = (progress * 100).toStringAsFixed(1);
            debugPrint(
                'تقدم التنزيل: $percentage% (${_formatBytes(downloadedBytes)} / ${_formatBytes(totalBytes)})');
          }

          // حساب السرعة والوقت المتبقي
          final now = DateTime.now();
          final timeDiff = now.difference(lastUpdateTime).inMilliseconds;

          if (timeDiff >= 500) {
            // تحديث كل نصف ثانية بدلاً من ثانية
            // تحديث كل ثانية
            final byteDiff = downloadedBytes - lastDownloadedBytes;
            final speedBps = byteDiff / (timeDiff / 1000);
            final remainingBytes = totalBytes - downloadedBytes;
            final remainingSeconds =
                speedBps > 0 ? remainingBytes / speedBps : 0;

            final speedFormatted = _formatBytes(speedBps.round());
            final remainingTimeFormatted =
                _formatDuration(remainingSeconds.round());

            debugPrint(
                'سرعة التنزيل: $speedFormatted/s، الوقت المتبقي: $remainingTimeFormatted');

            // إضافة حالة التنزيل إلى التدفق
            if (!controller.isClosed) {
              controller.add(DownloadStatus(
                isDownloading: true,
                progress: progress,
                downloadedBytes: _formatBytes(downloadedBytes),
                totalBytes: _formatBytes(totalBytes),
                speed: '$speedFormatted/s',
                remainingTime: remainingTimeFormatted,
                filePath: filePath,
              ));
            }

            lastUpdateTime = now;
            lastDownloadedBytes = downloadedBytes;
          }
        },
        onDone: () async {
          debugPrint('اكتمل تدفق التنزيل');

          try {
            // إغلاق الملف
            await sink.flush();
            await sink.close();

            debugPrint('تم اكتمال التنزيل بنجاح! الملف: $filePath');
            debugPrint('حجم الملف النهائي: ${_formatBytes(totalBytes)}');

            // التحقق من وجود الملف
            final downloadedFile = File(filePath);
            if (await downloadedFile.exists()) {
              final fileSize = await downloadedFile.length();
              debugPrint('تم التحقق من وجود الملف: $filePath');
              debugPrint('حجم الملف الفعلي: ${_formatBytes(fileSize)}');

              // إضافة حالة اكتمال التنزيل إلى التدفق
              if (!controller.isClosed) {
                controller.add(DownloadStatus(
                  isDownloading: false,
                  progress: 1.0,
                  downloadedBytes: _formatBytes(fileSize),
                  totalBytes: _formatBytes(fileSize),
                  speed: '0 KB/s',
                  remainingTime: '0s',
                  filePath: filePath,
                  isCompleted: true,
                ));
              }
            } else {
              debugPrint('تحذير: الملف غير موجود بعد التنزيل: $filePath');
              throw Exception('الملف غير موجود بعد التنزيل');
            }
          } catch (e) {
            debugPrint('خطأ أثناء إكمال التنزيل: $e');
            if (!controller.isClosed) {
              controller.add(DownloadStatus(
                isDownloading: false,
                hasError: true,
                errorMessage: 'فشل إكمال التنزيل: ${e.toString()}',
              ));
            }
          } finally {
            // إغلاق التدفق بعد اكتمال العملية
            await controller.close();
          }
        },
        onError: (error) async {
          debugPrint('خطأ أثناء التنزيل: $error');

          try {
            // إغلاق الملف
            await sink.close();
          } catch (e) {
            debugPrint('خطأ في إغلاق الملف: $e');
          }

          // محاولة حذف الملف غير المكتمل
          try {
            if (file.existsSync()) {
              file.deleteSync();
              debugPrint('تم حذف ملف التنزيل غير المكتمل');
            }
          } catch (e) {
            debugPrint('خطأ في حذف الملف غير المكتمل: $e');
          }

          // إضافة حالة الخطأ إلى التدفق
          if (!controller.isClosed) {
            controller.add(DownloadStatus(
              isDownloading: false,
              hasError: true,
              errorMessage: 'فشل التنزيل: ${error.toString()}',
            ));
          }

          // إغلاق التدفق بعد معالجة الخطأ
          await controller.close();
        },
      );

      // إضافة معالج لإلغاء الاشتراك عند إغلاق التدفق
      controller.onCancel = () {
        debugPrint('تم إلغاء التدفق، إلغاء الاشتراك في التنزيل');
        downloadSubscription.cancel();
      };

      return controller.stream;
    } catch (e, stack) {
      debugPrint('خطأ في تنزيل التحديث: $e');
      debugPrint('تتبع الخطأ: $stack');

      // محاولة حذف الملف غير المكتمل إذا كان موجودًا
      try {
        final directory = await getExternalStorageDirectory() ??
            await getApplicationDocumentsDirectory();
        final filePath =
            '${directory.path}/update_${updateInfo.latestVersion}.apk';
        final fileToDelete = File(filePath);

        if (fileToDelete.existsSync()) {
          fileToDelete.deleteSync();
          debugPrint('تم حذف ملف التنزيل غير المكتمل: $filePath');
        }
      } catch (deleteError) {
        debugPrint('خطأ في حذف الملف غير المكتمل: $deleteError');
      }

      // إضافة حالة الخطأ إلى التدفق إذا لم يكن مغلقًا
      if (!controller.isClosed) {
        controller.add(DownloadStatus(
          isDownloading: false,
          hasError: true,
          errorMessage: 'فشل التنزيل: ${e.toString()}',
        ));

        // إغلاق التدفق بعد إضافة حالة الخطأ
        await controller.close();
      }

      return controller.stream;
    }
  }

  @override
  Future<bool> installUpdate(String filePath) async {
    debugPrint('بدء تثبيت تحديث AmrDev POS من الملف: $filePath');

    try {
      // التحقق من أن مسار الملف غير فارغ
      if (filePath.isEmpty) {
        debugPrint('خطأ: مسار الملف فارغ');
        return false;
      }

      // التحقق من وجود الملف قبل التثبيت
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('خطأ: ملف التثبيت غير موجود: $filePath');
        return false;
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      debugPrint('الملف موجود، حجم الملف: ${_formatBytes(fileSize)}');

      // التحقق من صحة الملف (يجب أن يكون أكبر من 1 ميجابايت)
      if (fileSize < 1024 * 1024) {
        debugPrint(
            'خطأ: حجم الملف صغير جدًا (${_formatBytes(fileSize)})، قد يكون الملف تالفًا');

        // محاولة حذف الملف التالف
        try {
          await file.delete();
          debugPrint('تم حذف الملف التالف');
        } catch (e) {
          debugPrint('خطأ في حذف الملف التالف: $e');
        }

        return false;
      }

      // التحقق من امتداد الملف
      if (!filePath.toLowerCase().endsWith('.apk')) {
        debugPrint('خطأ: الملف ليس بتنسيق APK: $filePath');
        return false;
      }

      // إنشاء ملف .nomedia في مجلد التحديثات لمنع ظهور الملفات في معرض الصور
      try {
        final directory = file.parent;
        final nomediaFile = File('${directory.path}/.nomedia');
        if (!await nomediaFile.exists()) {
          await nomediaFile.create();
          debugPrint('تم إنشاء ملف .nomedia في مجلد التحديثات');
        }
      } catch (e) {
        debugPrint('خطأ في إنشاء ملف .nomedia: $e');
        // نتجاهل هذا الخطأ لأنه غير مهم للتثبيت
      }

      // محاولة تثبيت التحديث باستخدام intent خاص
      debugPrint('جاري فتح ملف APK للتثبيت باستخدام intent خاص...');

      // أولاً: محاولة استخدام OpenFile
      var result = await OpenFile.open(filePath);
      debugPrint(
          'نتيجة فتح الملف باستخدام OpenFile: ${result.type}, ${result.message}');

      // إذا فشلت الطريقة الأولى، نحاول استخدام طريقة بديلة
      if (result.type != ResultType.done) {
        debugPrint('محاولة استخدام طريقة بديلة للتثبيت...');

        // استخدام FileProvider لمشاركة الملف
        try {
          // محاولة استخدام OpenFile مرة أخرى مع تحديد نوع الملف
          const mimeType = 'application/vnd.android.package-archive';
          final secondResult = await OpenFile.open(
            filePath,
            type: mimeType,
          );

          debugPrint(
              'نتيجة المحاولة الثانية: ${secondResult.type}, ${secondResult.message}');

          if (secondResult.type == ResultType.done) {
            return true;
          }

          // إذا فشلت المحاولة الثانية أيضًا، نعرض رسالة للمستخدم
          if (secondResult.type == ResultType.permissionDenied) {
            debugPrint('تم رفض الصلاحية: ${secondResult.message}');
            return false;
          }

          return false;
        } catch (intentError) {
          debugPrint('خطأ في استخدام الطريقة البديلة للتثبيت: $intentError');
          return false;
        }
      } else {
        debugPrint('تم بدء عملية التثبيت بنجاح باستخدام OpenFile');
        return true;
      }
    } catch (e) {
      debugPrint('استثناء أثناء تثبيت تحديث AmrDev POS: $e');
      return false;
    }
  }

  @override
  Future<void> postponeUpdate(AppUpdateModel updateInfo,
      {Duration? duration}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      final remindTime = now +
          (duration?.inMilliseconds ??
              const Duration(hours: 24).inMilliseconds);

      // حفظ معلومات التحديث
      await prefs.setString(_postponedUpdateKey, updateInfo.toJson());
      await prefs.setInt('remind_time', remindTime);
    } catch (e) {
      debugPrint('خطأ في تأجيل التحديث: $e');
    }
  }

  @override
  Future<void> ignoreUpdate(AppUpdateModel updateInfo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_ignoredVersionKey, updateInfo.latestVersion);
    } catch (e) {
      debugPrint('خطأ في تجاهل التحديث: $e');
    }
  }

  @override
  Future<bool> getAutoUpdateEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_autoUpdateEnabledKey) ?? true; // افتراضيًا مفعل
    } catch (e) {
      debugPrint('خطأ في الحصول على حالة التحديث التلقائي: $e');
      return true;
    }
  }

  @override
  Future<void> setAutoUpdateEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoUpdateEnabledKey, enabled);
    } catch (e) {
      debugPrint('خطأ في تعيين حالة التحديث التلقائي: $e');
    }
  }

  // دالة لمقارنة الإصدارات (مثل 1.0.0 و 1.0.1)
  int _compareVersions(String version1, String version2) {
    try {
      debugPrint('مقارنة الإصدارات: $version1 و $version2');

      // تنظيف الإصدارات من أي أحرف غير رقمية أو نقاط
      version1 = version1.trim();
      version2 = version2.trim();

      // تقسيم الإصدارات إلى أجزاء
      List<String> v1Segments = version1.split('.');
      List<String> v2Segments = version2.split('.');

      debugPrint('أجزاء الإصدار 1: $v1Segments');
      debugPrint('أجزاء الإصدار 2: $v2Segments');

      // تحويل الأجزاء إلى أرقام
      List<int> v1Parts = [];
      List<int> v2Parts = [];

      for (var segment in v1Segments) {
        v1Parts.add(int.tryParse(segment) ?? 0);
      }

      for (var segment in v2Segments) {
        v2Parts.add(int.tryParse(segment) ?? 0);
      }

      debugPrint('أرقام الإصدار 1: $v1Parts');
      debugPrint('أرقام الإصدار 2: $v2Parts');

      // التأكد من أن كلا القائمتين لهما نفس الطول
      while (v1Parts.length < v2Parts.length) {
        v1Parts.add(0);
      }
      while (v2Parts.length < v1Parts.length) {
        v2Parts.add(0);
      }

      debugPrint('أرقام الإصدار 1 بعد التعديل: $v1Parts');
      debugPrint('أرقام الإصدار 2 بعد التعديل: $v2Parts');

      for (int i = 0; i < v1Parts.length; i++) {
        if (v1Parts[i] < v2Parts[i]) {
          debugPrint('الإصدار 1 أقدم من الإصدار 2');
          return -1;
        } else if (v1Parts[i] > v2Parts[i]) {
          debugPrint('الإصدار 1 أحدث من الإصدار 2');
          return 1;
        }
      }

      debugPrint('الإصداران متساويان');
      return 0;
    } catch (e) {
      debugPrint('خطأ في مقارنة الإصدارات: $e');
      return 0;
    }
  }

  // تنسيق حجم الملف
  String _formatBytes(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(2)} ${suffixes[i]}';
  }

  // تنسيق المدة
  String _formatDuration(int seconds) {
    if (seconds <= 0) return '0s';

    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${remainingSeconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${remainingSeconds}s';
    } else {
      return '${remainingSeconds}s';
    }
  }

  // تخزين معلومات التحديث مؤقتًا
  Future<void> _cacheUpdateInfo(AppUpdateModel updateInfo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cachedUpdateInfoKey, updateInfo.toJson());
    } catch (e) {
      debugPrint('خطأ في تخزين معلومات التحديث مؤقتًا: $e');
    }
  }

  // الحصول على معلومات التحديث المخزنة مؤقتًا
  Future<AppUpdateModel?> _getCachedUpdateInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedInfo = prefs.getString(_cachedUpdateInfoKey);
      if (cachedInfo != null) {
        return AppUpdateModel.fromJson(cachedInfo);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات التحديث المخزنة مؤقتًا: $e');
      return null;
    }
  }

  // تخزين سجل التغييرات مؤقتًا
  Future<void> _cacheChangelog(List<ChangelogModel> changelog) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = changelog.map((item) => item.toMap()).toList();
      await prefs.setString(_cachedChangelogKey, jsonEncode(jsonList));
    } catch (e) {
      debugPrint('خطأ في تخزين سجل التغييرات مؤقتًا: $e');
    }
  }

  // الحصول على سجل التغييرات المخزن مؤقتًا
  Future<List<ChangelogModel>> _getCachedChangelog() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedChangelog = prefs.getString(_cachedChangelogKey);
      if (cachedChangelog != null) {
        final jsonList = jsonDecode(cachedChangelog) as List;
        return jsonList
            .map((item) =>
                ChangelogModel.fromMap(Map<String, dynamic>.from(item as Map)))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل التغييرات المخزن مؤقتًا: $e');
      return [];
    }
  }

  // إنشاء سجل تغييرات وهمي
  List<ChangelogModel> _getMockChangelog() {
    final currentVersion = appVersion.isNotEmpty ? appVersion : '1.0.0';
    final versions = _generatePreviousVersions(currentVersion, 5);

    return versions.map((version) {
      final releaseDate = DateTime.now()
          .subtract(Duration(days: 30 * (versions.indexOf(version) + 1)));
      return ChangelogModel(
        version: version,
        releaseNotes: _generateMockReleaseNotes(version),
        releaseDate: releaseDate,
        updateSize: 10 + Random().nextInt(20), // 10-30 MB
      );
    }).toList();
  }

  // توليد إصدارات سابقة
  List<String> _generatePreviousVersions(String currentVersion, int count) {
    final parts = currentVersion.split('.').map(int.parse).toList();
    final versions = <String>[];

    for (int i = 0; i < count; i++) {
      if (parts[2] > 0) {
        parts[2]--;
      } else if (parts[1] > 0) {
        parts[1]--;
        parts[2] = 9;
      } else if (parts[0] > 1) {
        parts[0]--;
        parts[1] = 9;
        parts[2] = 9;
      } else {
        break; // لا يمكن توليد المزيد من الإصدارات
      }

      versions.add('${parts[0]}.${parts[1]}.${parts[2]}');
    }

    return versions;
  }

  // توليد ملاحظات إصدار وهمية
  String _generateMockReleaseNotes(String version) {
    final features = [
      'تحسين الأداء العام',
      'إصلاح مشكلة في واجهة المستخدم',
      'إضافة ميزات جديدة',
      'تحسين استقرار التطبيق',
      'إصلاح مشكلة في الاتصال بالخادم',
      'تحسين استهلاك البطارية',
      'تحديث المكتبات الخارجية',
      'تحسين تجربة المستخدم',
      'إصلاح مشكلة في عرض البيانات',
      'تحسين الأمان',
    ];

    final random = Random();
    final featureCount = 3 + random.nextInt(3); // 3-5 ميزات
    final selectedFeatures = <String>[];

    for (int i = 0; i < featureCount; i++) {
      final feature = features[random.nextInt(features.length)];
      if (!selectedFeatures.contains(feature)) {
        selectedFeatures.add(feature);
      }
    }

    return 'الإصدار $version:\n\n• ${selectedFeatures.join('\n• ')}';
  }
}
