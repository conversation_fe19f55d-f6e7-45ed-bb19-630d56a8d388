// ignore_for_file: unused_local_variable

import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
// import 'package:mobile_pos/Screens/Authentication/register_form.dart';
import 'package:mobile_pos/repository/login_repo.dart';
import 'package:mobile_pos/implementations/auth_log_implementations.dart';
import 'package:nb_utils/nb_utils.dart';
import '../../constant.dart';
// import 'forgot_password.dart';
import 'phone.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  bool showPassword = true;
  late String email, password;
  GlobalKey<FormState> globalKey = GlobalKey<FormState>();

  bool validateAndSave() {
    final form = globalKey.currentState;
    if (form!.validate()) {
      form.save();
      return true;
    }
    return false;
  }

  @override
  void initState() {
    getConnectivity();
    checkInternet();
    super.initState();
  }

  late StreamSubscription subscription;
  bool isDeviceConnected = false;
  bool isAlertSet = false;

  // getConnectivity() => subscription = Connectivity().onConnectivityChanged.listen(
  //       (ConnectivityResult result) async {
  //         isDeviceConnected = await InternetConnectionChecker().hasConnection;
  //         if (!isDeviceConnected && isAlertSet == false) {
  //           showDialogBox();
  //           setState(() => isAlertSet = true);
  //         }R
  //       },
  //     );
  void connectivityCallback(List<ConnectivityResult> results) async {
    // Since it's likely that only one result will be received,
    // you can handle just the first one.
    ConnectivityResult result = results.first;

    isDeviceConnected = await InternetConnectionChecker().hasConnection;
    if (!isDeviceConnected && !isAlertSet) {
      showDialogBox();
      setState(() => isAlertSet = true);
    }
  }

  getConnectivity() {
    subscription = Connectivity().onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      connectivityCallback(results);
    });
  }

  checkInternet() async {
    isDeviceConnected = await InternetConnectionChecker().hasConnection;
    if (!isDeviceConnected) {
      showDialogBox();
      setState(() => isAlertSet = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white,
                Colors.grey.shade100,
                Colors.grey.shade200,
              ],
            ),
          ),
          child: Consumer(
            builder: (context, ref, child) {
              final loginProvider = ref.watch(logInProvider);

              return Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: kMainColor.withAlpha(50),
                              blurRadius: 20,
                              spreadRadius: 5,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 80,
                          backgroundColor: Colors.white,
                          child: ClipOval(
                            child: Image.asset(
                              loginScreenLogo,
                              height: 160,
                              width: 160,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Form(
                          key: globalKey,
                          child: Column(
                            children: [
                              const SizedBox(height: 20),
                              TextFormField(
                                keyboardType: TextInputType.text,
                                style: const TextStyle(
                                  color:
                                      kPremiumPlanColor, // لون النص اللي بتكتبه
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: const BorderSide(
                                      color: kBorderColorTextField,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: const BorderSide(
                                      color: kBorderColorTextField,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: const BorderSide(
                                      color: kMainColor,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: Colors.black12,
                                  labelText: 'اسم المستخدم',
                                  labelStyle: const TextStyle(
                                    color: kPremiumPlanColor,
                                  ),
                                  hintText: 'اكتب اسم المستخدم بتاعك',
                                  prefixIcon: const Icon(
                                    Icons.person,
                                    color: kMainColor,
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 15,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'لازم تكتب اسم المستخدم يا باشا';
                                  } else if (value.contains('@')) {
                                    return 'اكتب اسم المستخدم بس من غير @amrdev.com';
                                  } else if (value.contains(' ')) {
                                    return 'متكتبش مسافات في اسم المستخدم';
                                  }
                                  return null;
                                },
                                onSaved: (value) {
                                  // إضافة @amrdev.com تلقائياً
                                  loginProvider.email = '${value!}@amrdev.com';
                                },
                              ),
                              const SizedBox(height: 20),
                              TextFormField(
                                keyboardType: TextInputType.text,
                                obscureText: showPassword,
                                style: const TextStyle(
                                  color:
                                      kPremiumPlanColor, // لون النص اللي بتكتبه
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: const BorderSide(
                                      color: kPremiumPlanColor,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: const BorderSide(
                                      color: kPremiumPlanColor,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: const BorderSide(
                                      color: kMainColor,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: Colors.black12,
                                  labelText: 'الرقم السري',
                                  labelStyle: const TextStyle(
                                    color: kPremiumPlanColor,
                                  ),
                                  hintText: 'اكتب الرقم السري بتاعك',
                                  prefixIcon: const Icon(
                                    Icons.lock,
                                    color: kMainColor,
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 15,
                                  ),
                                  suffixIcon: IconButton(
                                    onPressed: () {
                                      setState(() {
                                        showPassword = !showPassword;
                                      });
                                    },
                                    icon: Icon(
                                      showPassword
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: kMainColor,
                                    ),
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'لازم تكتب الرقم السري يا باشا';
                                  } else if (value.length < 4) {
                                    return 'الرقم السري لازم يكون أكتر من 4 حروف';
                                  }
                                  return null;
                                },
                                onSaved: (value) {
                                  loginProvider.password = value!;
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Row(
                      //   children: [
                      //     const Spacer(),
                      //     TextButton(
                      //       onPressed: () {
                      //         const ForgotPassword().launch(context);
                      //         // Navigator.pushNamed(context, '/forgotPassword');
                      //       },
                      //       child: Text(
                      //         lang.S.of(context).forgotPasswords,
                      //         style: GoogleFonts.poppins(
                      //           color: kGreyTextColor,
                      //           fontSize: 15.0,
                      //         ),
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: ElevatedButton(
                          onPressed: () {
                            if (validateAndSave()) {
                              // Llamar a la función de inicio de sesión
                              loginProvider.signIn(context);

                              // Registrar el inicio de sesión en los logs
                              try {
                                AuthLogImplementation.logUserLogin(
                                  email: loginProvider.email,
                                );
                              } catch (e) {
                                debugPrint(
                                  'Error al registrar el inicio de sesión: $e',
                                );
                              }
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: kMainColor,
                            foregroundColor: Colors.white,
                            elevation: 5,
                            shadowColor: kMainColor.withAlpha(128),
                            minimumSize: const Size(double.infinity, 55),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                          child: const Text(
                            'دخول',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      // زر تسجيل الدخول برقم الهاتف
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 5,
                        ),
                        child: TextButton.icon(
                          onPressed: () {
                            const PhoneAuth().launch(context);
                          },
                          icon: const Icon(
                            Icons.phone_android,
                            color: kMainColor,
                          ),
                          label: Text(
                            'دخول بالموبايل',
                            style: GoogleFonts.poppins(
                              color: kMainColor,
                              fontSize: 15.0,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'معندكش حساب؟',
                            style: GoogleFonts.poppins(
                              color: kGreyTextColor,
                              fontSize: 15.0,
                            ),
                          ),
                          // TextButton(
                          //   onPressed: () {
                          //     // Navigator.pushNamed(context, '/signup');
                          //     const RegisterScreen().launch(context);
                          //   },
                          //   child: Text(
                          //     lang.S.of(context).register,
                          //     style: GoogleFonts.poppins(
                          //       color: kMainColor,
                          //       fontSize: 15.0,
                          //       fontWeight: FontWeight.bold,
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  showDialogBox() => showCupertinoDialog<String>(
        context: context,
        builder: (BuildContext context) => CupertinoAlertDialog(
          title: const Text('مفيش نت'),
          content: const Text('اتأكد من الواي فاي أو الداتا بتاعتك'),
          actions: <Widget>[
            TextButton(
              onPressed: () async {
                Navigator.pop(context, 'Cancel');
                setState(() => isAlertSet = false);
                isDeviceConnected =
                    await InternetConnectionChecker().hasConnection;
                if (!isDeviceConnected && isAlertSet == false) {
                  showDialogBox();
                  setState(() => isAlertSet = true);
                }
              },
              child: const Text('حاول تاني'),
            ),
          ],
        ),
      );
}
