// بسم الله الرحمن الرحيم
// سجل الميزات - يسجل جميع الميزات المتاحة في النظام

import 'package:flutter/foundation.dart';
import 'core/feature_interface.dart';
import 'core/feature_manager.dart';
import 'user_profile/profile_feature.dart';
import 'notifications/notification_feature.dart';
// import 'chat/chat_feature.dart';
import 'advanced_reports/advanced_reports_feature.dart';
import 'inventory_management/inventory_management_feature.dart';

/// سجل الميزات - يسجل جميع الميزات المتاحة في النظام
class FeatureRegistry {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من سجل الميزات
  static final FeatureRegistry _instance = FeatureRegistry._internal();
  factory FeatureRegistry() => _instance;
  FeatureRegistry._internal();

  // قائمة الميزات المتاحة
  final List<FeatureInterface> _availableFeatures = [
    ProfileFeature(),
    NotificationFeature(),
    // ChatFeature(),
    AdvancedReportsFeature(),
    InventoryManagementFeature(),
  ];

  // الحصول على قائمة الميزات المتاحة
  List<FeatureInterface> get availableFeatures => _availableFeatures;

  /// تسجيل جميع الميزات في مدير الميزات
  void registerAllFeatures() {
    final featureManager = FeatureManager();

    for (var feature in _availableFeatures) {
      featureManager.registerFeature(feature);
    }
  }

  /// تهيئة جميع الميزات المفعلة
  Future<void> initializeEnabledFeatures() async {
    try {
      final featureManager = FeatureManager();
      await featureManager.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة الميزات المفعلة: $e');
      // استمر في التنفيذ حتى مع وجود خطأ
    }
  }
}
