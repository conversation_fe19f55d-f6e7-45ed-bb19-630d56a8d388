// import 'package:parse_server_sdk/parse_server_sdk.dart';

// /// نموذج مجموعة الدردشة في Back4App
// class ChatGroup extends ParseObject implements ParseCloneable {
//   // اسم الجدول في Back4App
//   static const String _keyTableName = 'ChatGroup';

//   // أسماء الحقول
//   static const String keyName = 'name';
//   static const String keyDescription = 'description';
//   static const String keyImage = 'image';
//   static const String keyMembers = 'members';
//   static const String keyAdmins = 'admins';
//   static const String keyCreatedBy = 'createdBy';
//   static const String keySettings = 'settings';
//   static const String keyConversationId = 'conversationId';

//   // المُنشئ
//   ChatGroup() : super(_keyTableName);
//   ChatGroup.clone() : this();

//   @override
//   ChatGroup clone(Map<String, dynamic> map) => ChatGroup.clone()..fromJson(map);

//   // الحصول على اسم المجموعة
//   String get name => get<String>(keyName) ?? '';
//   set name(String value) => set<String>(keyName, value);

//   // الحصول على وصف المجموعة
//   String get description => get<String>(keyDescription) ?? '';
//   set description(String value) => set<String>(keyDescription, value);

//   // الحصول على صورة المجموعة
//   ParseFile? get image => get<ParseFile?>(keyImage);
//   set image(ParseFile? value) => set<ParseFile?>(keyImage, value);

//   // الحصول على أعضاء المجموعة
//   List<String> get members => get<List<dynamic>>(keyMembers)?.cast<String>() ?? [];
//   set members(List<String> value) => set<List<String>>(keyMembers, value);

//   // الحصول على مشرفي المجموعة
//   List<String> get admins => get<List<dynamic>>(keyAdmins)?.cast<String>() ?? [];
//   set admins(List<String> value) => set<List<String>>(keyAdmins, value);

//   // الحصول على منشئ المجموعة
//   String get createdBy => get<String>(keyCreatedBy) ?? '';
//   set createdBy(String value) => set<String>(keyCreatedBy, value);

//   // الحصول على إعدادات المجموعة
//   Map<String, dynamic> get settings => get<Map<String, dynamic>>(keySettings) ?? {};
//   set settings(Map<String, dynamic> value) => set<Map<String, dynamic>>(keySettings, value);

//   // الحصول على معرف المحادثة
//   String get conversationId => get<String>(keyConversationId) ?? '';
//   set conversationId(String value) => set<String>(keyConversationId, value);

//   // تحويل النموذج إلى Map
//   Map<String, dynamic> toMap() {
//     return {
//       'objectId': objectId,
//       'name': name,
//       'description': description,
//       'image': image?.url,
//       'members': members,
//       'admins': admins,
//       'createdBy': createdBy,
//       'settings': settings,
//       'conversationId': conversationId,
//       'createdAt': createdAt?.toIso8601String(),
//     };
//   }
// }
