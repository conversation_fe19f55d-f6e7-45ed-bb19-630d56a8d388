import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/personal_information_model.dart';
import 'package:mobile_pos/repository/profile_details_repo.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mobile_pos/currency.dart';

/// مزود معلومات المستخدم الحالي
final currentUserProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  // الحصول على معرف المستخدم
  final prefs = await SharedPreferences.getInstance();
  final userId = constUserId; // استخدام معرف المستخدم الثابت
  final userTitle = prefs.getString('userTitle') ?? '';

  // الحصول على معلومات الملف الشخصي
  final profileRepo = ProfileRepo();
  final PersonalInformationModel profileInfo = await profileRepo.getDetails();

  // إعداد معلومات المستخدم
  final Map<String, dynamic> userInfo = {
    'userId': userId,
    'userName': userTitle.isNotEmpty
        ? userTitle
        : (profileInfo.companyName ?? 'مستخدم'),
    'userImage': profileInfo.pictureUrl ?? '',
    'isSubUser': prefs.getBool('isSubUser') ?? false,
  };

  return userInfo;
});

/// مزود اسم المستخدم الحالي
final currentUserNameProvider = FutureProvider<String>((ref) async {
  final userInfo = await ref.watch(currentUserProvider.future);
  return userInfo['userName'] as String;
});

/// مزود صورة المستخدم الحالي
final currentUserImageProvider = FutureProvider<String>((ref) async {
  final userInfo = await ref.watch(currentUserProvider.future);
  return userInfo['userImage'] as String;
});
