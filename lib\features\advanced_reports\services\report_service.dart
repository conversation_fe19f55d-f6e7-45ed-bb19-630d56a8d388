// بسم الله الرحمن الرحيم
// خدمة التقارير - مسؤولة عن إنشاء وإدارة التقارير

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:firebase_database/firebase_database.dart';

import '../models/report_model.dart';
import '../../core/auth/auth_service.dart';

/// خدمة التقارير
class ReportService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة التقارير
  static final ReportService _instance = ReportService._internal();
  factory ReportService() => _instance;
  ReportService._internal();

  // مثيل Firebase Database
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // خدمة المصادقة
  final AuthService _authService = AuthService();

  // مولد UUID
  final Uuid _uuid = const Uuid();

  // الحصول على مرجع قاعدة البيانات للتقارير
  DatabaseReference _getReportsRef() {
    return _database.ref('features/reports');
  }

  // الحصول على مرجع قاعدة البيانات لتقارير المستخدم
  DatabaseReference _getUserReportsRef(String uid) {
    return _database.ref('features/user_reports/$uid');
  }

  /// إنشاء تقرير جديد
  Future<ReportModel?> createReport({
    required String title,
    required ReportType type,
    required ReportPeriod period,
    String? description,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final reportId = _uuid.v4();
      final createdAt = DateTime.now();

      // إنشاء نموذج التقرير
      final report = ReportModel(
        id: reportId,
        title: title,
        type: type,
        period: period,
        createdAt: createdAt,
        description: description,
        parameters: parameters ?? {},
        createdBy: currentUser.uid,
      );

      // حفظ التقرير في قاعدة البيانات
      await _getReportsRef().child(reportId).set(report.toMap());

      // إضافة التقرير إلى قائمة تقارير المستخدم
      await _getUserReportsRef(currentUser.uid).child(reportId).set(true);

      // توليد بيانات التقرير
      final reportWithData = await _generateReportData(report);
      if (reportWithData != null) {
        // تحديث التقرير ببيانات التقرير
        await _getReportsRef().child(reportId).update(reportWithData.toMap());
        return reportWithData;
      }

      return report;
    } catch (e) {
      debugPrint('خطأ في إنشاء تقرير جديد: $e');
      return null;
    }
  }

  /// الحصول على تقرير
  Future<ReportModel?> getReport(String reportId) async {
    try {
      final snapshot = await _getReportsRef().child(reportId).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        return ReportModel.fromMap(Map<String, dynamic>.from(data));
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير: $e');
      return null;
    }
  }

  /// الحصول على تقارير المستخدم
  Future<List<ReportModel>> getUserReports(String uid) async {
    try {
      final userReportsSnapshot = await _getUserReportsRef(uid).get();
      if (!userReportsSnapshot.exists || userReportsSnapshot.value == null) {
        return [];
      }

      final userReportsData =
          userReportsSnapshot.value as Map<dynamic, dynamic>;
      final reportIds = userReportsData.keys.cast<String>().toList();
      final reports = <ReportModel>[];

      for (var reportId in reportIds) {
        final reportSnapshot = await _getReportsRef().child(reportId).get();
        if (reportSnapshot.exists && reportSnapshot.value != null) {
          final reportData = reportSnapshot.value as Map<dynamic, dynamic>;
          reports
              .add(ReportModel.fromMap(Map<String, dynamic>.from(reportData)));
        }
      }

      // ترتيب التقارير حسب تاريخ الإنشاء (الأحدث أولاً)
      reports.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return reports;
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المستخدم: $e');
      return [];
    }
  }

  /// الحصول على تقارير المستخدم الحالي
  Future<List<ReportModel>> getCurrentUserReports() async {
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      return getUserReports(currentUser.uid);
    }
    return [];
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // حذف التقرير من قاعدة البيانات
      await _getReportsRef().child(reportId).remove();

      // حذف التقرير من قائمة تقارير المستخدم
      await _getUserReportsRef(currentUser.uid).child(reportId).remove();

      return true;
    } catch (e) {
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    }
  }

  /// توليد بيانات التقرير
  Future<ReportModel?> _generateReportData(ReportModel report) async {
    try {
      // توليد بيانات مختلفة حسب نوع التقرير
      switch (report.type) {
        case ReportType.sales:
          return _generateSalesReportData(report);
        case ReportType.inventory:
          return _generateInventoryReportData(report);
        case ReportType.financial:
          return _generateFinancialReportData(report);
        case ReportType.custom:
          return _generateCustomReportData(report);
      }
    } catch (e) {
      debugPrint('خطأ في توليد بيانات التقرير: $e');
      return null;
    }
  }

  /// توليد بيانات تقرير المبيعات
  Future<ReportModel> _generateSalesReportData(ReportModel report) async {
    // بيانات حقيقية من قاعدة البيانات
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // تحديد نطاق التاريخ بناءً على فترة التقرير
      DateTime startDate;
      DateTime endDate = DateTime.now();

      switch (report.period) {
        case ReportPeriod.daily:
          startDate = DateTime(endDate.year, endDate.month, endDate.day);
          break;
        case ReportPeriod.weekly:
          startDate = endDate.subtract(const Duration(days: 7));
          break;
        case ReportPeriod.monthly:
          startDate = DateTime(endDate.year, endDate.month, 1);
          break;
        case ReportPeriod.quarterly:
          startDate = DateTime(endDate.year, endDate.month - 3, 1);
          break;
        case ReportPeriod.yearly:
          startDate = DateTime(endDate.year, 1, 1);
          break;
        case ReportPeriod.custom:
          // استخدام التواريخ المخصصة من معلمات التقرير
          startDate = report.parameters['startDate'] != null
              ? DateTime.parse(report.parameters['startDate'])
              : DateTime(endDate.year, endDate.month, 1);
          endDate = report.parameters['endDate'] != null
              ? DateTime.parse(report.parameters['endDate'])
              : endDate;
          break;
      }

      // الحصول على بيانات المبيعات من قاعدة البيانات
      final salesRef = _database.ref('${currentUser.uid}/Sales Transition');
      final salesSnapshot = await salesRef.get();

      double totalSales = 0;
      int totalItems = 0;
      Map<String, double> productSales = {};
      Map<String, double> categorySales = {};
      List<Map<String, dynamic>> dailySales = [];
      List<Map<String, dynamic>> tableData = [];

      if (salesSnapshot.exists && salesSnapshot.value != null) {
        final salesData = salesSnapshot.value as Map<dynamic, dynamic>;

        // معالجة بيانات المبيعات
        salesData.forEach((key, value) {
          final saleData = Map<String, dynamic>.from(value as Map);
          final saleDate =
              DateTime.parse(saleData['invoiceDate'] ?? '2023-01-01');

          // التحقق من أن تاريخ البيع ضمن نطاق التقرير
          if (saleDate.isAfter(startDate) &&
              saleDate.isBefore(endDate.add(const Duration(days: 1)))) {
            // إجمالي المبيعات
            final totalAmount =
                double.tryParse(saleData['totalAmount']?.toString() ?? '0') ??
                    0;
            totalSales += totalAmount;

            // عدد العناصر
            final productList = saleData['productList'] as List<dynamic>?;
            if (productList != null) {
              totalItems += productList.length;

              // معالجة بيانات المنتجات
              for (var product in productList) {
                final productData = Map<String, dynamic>.from(product as Map);
                final productName =
                    productData['productName'] ?? 'منتج غير معروف';
                final productCategory =
                    productData['productCategory'] ?? 'فئة غير معروفة';
                final quantity =
                    int.tryParse(productData['quantity']?.toString() ?? '0') ??
                        0;
                final price = double.tryParse(
                        productData['subTotal']?.toString() ?? '0') ??
                    0;

                // تحديث مبيعات المنتج
                productSales[productName] =
                    (productSales[productName] ?? 0) + price;

                // تحديث مبيعات الفئة
                categorySales[productCategory] =
                    (categorySales[productCategory] ?? 0) + price;

                // إضافة إلى بيانات الجدول
                tableData.add({
                  'product': productName,
                  'quantity': quantity,
                  'total': price,
                });
              }
            }

            // إضافة إلى بيانات المبيعات اليومية
            final dateStr =
                '${saleDate.year}-${saleDate.month.toString().padLeft(2, '0')}-${saleDate.day.toString().padLeft(2, '0')}';
            final existingDayIndex =
                dailySales.indexWhere((day) => day['date'] == dateStr);

            if (existingDayIndex >= 0) {
              dailySales[existingDayIndex]['sales'] =
                  (dailySales[existingDayIndex]['sales'] as double) +
                      totalAmount;
            } else {
              dailySales.add({
                'date': dateStr,
                'sales': totalAmount,
              });
            }
          }
        });
      }

      // ترتيب بيانات المبيعات اليومية حسب التاريخ
      dailySales
          .sort((a, b) => (a['date'] as String).compareTo(b['date'] as String));

      // الحصول على المنتج الأكثر مبيعًا
      String topSellingProduct = 'لا يوجد';
      double topSellingProductValue = 0;
      productSales.forEach((product, sales) {
        if (sales > topSellingProductValue) {
          topSellingProduct = product;
          topSellingProductValue = sales;
        }
      });

      // الحصول على الفئة الأكثر مبيعًا
      String topSellingCategory = 'لا يوجد';
      double topSellingCategoryValue = 0;
      categorySales.forEach((category, sales) {
        if (sales > topSellingCategoryValue) {
          topSellingCategory = category;
          topSellingCategoryValue = sales;
        }
      });

      // ترتيب بيانات الجدول حسب إجمالي المبيعات (تنازليًا)
      tableData.sort(
          (a, b) => (b['total'] as double).compareTo(a['total'] as double));

      // اقتصار بيانات الجدول على أعلى 10 منتجات
      if (tableData.length > 10) {
        tableData = tableData.sublist(0, 10);
      }

      // إنشاء بيانات التقرير
      final data = {
        'totalSales': totalSales,
        'totalItems': totalItems,
        'averageSale': totalItems > 0 ? totalSales / totalItems : 0,
        'topSellingProduct': topSellingProduct,
        'topSellingCategory': topSellingCategory,
      };

      return report.copyWith(
        data: data,
        chartData: dailySales,
        tableData: tableData,
      );
    } catch (e) {
      debugPrint('خطأ في توليد بيانات تقرير المبيعات: $e');

      // إرجاع تقرير فارغ في حالة حدوث خطأ
      return report.copyWith(
        data: {
          'totalSales': 0,
          'totalItems': 0,
          'averageSale': 0,
          'topSellingProduct': 'غير متوفر',
          'topSellingCategory': 'غير متوفر',
        },
        chartData: [],
        tableData: [],
      );
    }
  }

  /// توليد بيانات تقرير المخزون
  Future<ReportModel> _generateInventoryReportData(ReportModel report) async {
    // بيانات حقيقية من قاعدة البيانات
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // الحصول على بيانات المنتجات من قاعدة البيانات
      final productsRef = _database.ref('${currentUser.uid}/Products');
      final productsSnapshot = await productsRef.get();

      int totalItems = 0;
      double totalValue = 0;
      int lowStockItems = 0;
      int outOfStockItems = 0;
      Map<String, double> categoryValues = {};
      List<Map<String, dynamic>> tableData = [];

      if (productsSnapshot.exists && productsSnapshot.value != null) {
        final productsData = productsSnapshot.value as Map<dynamic, dynamic>;

        // معالجة بيانات المنتجات
        productsData.forEach((key, productData) {
          final productMap = Map<String, dynamic>.from(productData as Map);
          final productName = productMap['productName'] ?? 'منتج غير معروف';
          final productCategory =
              productMap['productCategory'] ?? 'فئة غير معروفة';
          final stock =
              int.tryParse(productMap['productStock']?.toString() ?? '0') ?? 0;
          final price = double.tryParse(
                  productMap['productSalePrice']?.toString() ?? '0') ??
              0;
          final productValue = stock * price;

          // تحديث إجماليات المخزون
          totalItems++;
          totalValue += productValue;

          // تحديث عدد المنتجات منخفضة المخزون والمنتهية
          if (stock == 0) {
            outOfStockItems++;
          } else if (stock < 5) {
            // اعتبار المنتجات التي لديها أقل من 5 وحدات منخفضة المخزون
            lowStockItems++;
          }

          // تحديث قيم الفئات
          categoryValues[productCategory] =
              (categoryValues[productCategory] ?? 0) + productValue;

          // تحديد حالة المخزون
          String status;
          if (stock == 0) {
            status = 'نفذ';
          } else if (stock < 5) {
            status = 'منخفض';
          } else {
            status = 'متوفر';
          }

          // إضافة إلى بيانات الجدول
          tableData.add({
            'product': productName,
            'stock': stock,
            'value': productValue,
            'status': status,
          });
        });
      }

      // تحويل بيانات الفئات إلى تنسيق الرسم البياني
      List<Map<String, dynamic>> chartData = [];
      categoryValues.forEach((category, value) {
        chartData.add({
          'category': category,
          'value': value,
        });
      });

      // ترتيب بيانات الرسم البياني حسب القيمة (تنازليًا)
      chartData.sort(
          (a, b) => (b['value'] as double).compareTo(a['value'] as double));

      // اقتصار بيانات الرسم البياني على أعلى 5 فئات
      if (chartData.length > 5) {
        chartData = chartData.sublist(0, 5);
      }

      // ترتيب بيانات الجدول حسب حالة المخزون (نفذ، منخفض، متوفر) ثم حسب القيمة (تنازليًا)
      tableData.sort((a, b) {
        final statusOrder = {'نفذ': 0, 'منخفض': 1, 'متوفر': 2};
        final statusComparison = (statusOrder[a['status']] ?? 2)
            .compareTo(statusOrder[b['status']] ?? 2);
        if (statusComparison != 0) {
          return statusComparison;
        }
        return (b['value'] as double).compareTo(a['value'] as double);
      });

      // الحصول على الفئة الأعلى قيمة
      String topCategory = 'لا يوجد';
      double topCategoryValue = 0;
      categoryValues.forEach((category, value) {
        if (value > topCategoryValue) {
          topCategory = category;
          topCategoryValue = value;
        }
      });

      // إنشاء بيانات التقرير
      final data = {
        'totalItems': totalItems,
        'totalValue': totalValue,
        'lowStockItems': lowStockItems,
        'outOfStockItems': outOfStockItems,
        'topCategory': topCategory,
      };

      return report.copyWith(
        data: data,
        chartData: chartData,
        tableData: tableData,
      );
    } catch (e) {
      debugPrint('خطأ في توليد بيانات تقرير المخزون: $e');

      // إرجاع تقرير فارغ في حالة حدوث خطأ
      return report.copyWith(
        data: {
          'totalItems': 0,
          'totalValue': 0,
          'lowStockItems': 0,
          'outOfStockItems': 0,
          'topCategory': 'غير متوفر',
        },
        chartData: [],
        tableData: [],
      );
    }
  }

  /// توليد بيانات تقرير مالي
  Future<ReportModel> _generateFinancialReportData(ReportModel report) async {
    // بيانات حقيقية من قاعدة البيانات
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // تحديد نطاق التاريخ بناءً على فترة التقرير
      DateTime startDate;
      DateTime endDate = DateTime.now();

      switch (report.period) {
        case ReportPeriod.daily:
          startDate = DateTime(endDate.year, endDate.month, endDate.day);
          break;
        case ReportPeriod.weekly:
          startDate = endDate.subtract(const Duration(days: 7));
          break;
        case ReportPeriod.monthly:
          startDate = DateTime(endDate.year, endDate.month, 1);
          break;
        case ReportPeriod.quarterly:
          startDate = DateTime(endDate.year, endDate.month - 3, 1);
          break;
        case ReportPeriod.yearly:
          startDate = DateTime(endDate.year, 1, 1);
          break;
        case ReportPeriod.custom:
          // استخدام التواريخ المخصصة من معلمات التقرير
          startDate = report.parameters['startDate'] != null
              ? DateTime.parse(report.parameters['startDate'])
              : DateTime(endDate.year, endDate.month, 1);
          endDate = report.parameters['endDate'] != null
              ? DateTime.parse(report.parameters['endDate'])
              : endDate;
          break;
      }

      // الحصول على بيانات المبيعات والمشتريات والمصروفات
      double totalRevenue = 0;
      double totalExpenses = 0;
      double totalProfit = 0;
      double totalTax = 0;
      double totalDebt = 0;

      // 1. الحصول على بيانات المبيعات
      final salesRef = _database.ref('${currentUser.uid}/Sales Transition');
      final salesSnapshot = await salesRef.get();

      if (salesSnapshot.exists && salesSnapshot.value != null) {
        final salesData = salesSnapshot.value as Map<dynamic, dynamic>;

        salesData.forEach((key, value) {
          final saleData = Map<String, dynamic>.from(value as Map);
          final saleDate =
              DateTime.parse(saleData['invoiceDate'] ?? '2023-01-01');

          // التحقق من أن تاريخ البيع ضمن نطاق التقرير
          if (saleDate.isAfter(startDate) &&
              saleDate.isBefore(endDate.add(const Duration(days: 1)))) {
            // إجمالي المبيعات
            final totalAmount =
                double.tryParse(saleData['totalAmount']?.toString() ?? '0') ??
                    0;
            totalRevenue += totalAmount;

            // الضرائب
            final tax =
                double.tryParse(saleData['vat']?.toString() ?? '0') ?? 0;
            totalTax += tax;

            // الربح
            final profit =
                double.tryParse(saleData['lossProfit']?.toString() ?? '0') ?? 0;
            totalProfit += profit;
          }
        });
      }

      // 2. الحصول على بيانات المصروفات
      final expensesRef = _database.ref('${currentUser.uid}/Expense');
      final expensesSnapshot = await expensesRef.get();

      if (expensesSnapshot.exists && expensesSnapshot.value != null) {
        final expensesData = expensesSnapshot.value as Map<dynamic, dynamic>;

        expensesData.forEach((key, value) {
          final expenseData = Map<String, dynamic>.from(value as Map);
          final expenseDate =
              DateTime.parse(expenseData['date'] ?? '2023-01-01');

          // التحقق من أن تاريخ المصروف ضمن نطاق التقرير
          if (expenseDate.isAfter(startDate) &&
              expenseDate.isBefore(endDate.add(const Duration(days: 1)))) {
            // إجمالي المصروفات
            final amount =
                double.tryParse(expenseData['amount']?.toString() ?? '0') ?? 0;
            totalExpenses += amount;
          }
        });
      }

      // 3. الحصول على بيانات المديونية
      final dueRef = _database.ref('${currentUser.uid}/Due Transaction');
      final dueSnapshot = await dueRef.get();

      if (dueSnapshot.exists && dueSnapshot.value != null) {
        final dueData = dueSnapshot.value as Map<dynamic, dynamic>;

        dueData.forEach((key, value) {
          final dueItem = Map<String, dynamic>.from(value as Map);
          final dueDate = DateTime.parse(dueItem['date'] ?? '2023-01-01');

          // التحقق من أن تاريخ المديونية ضمن نطاق التقرير
          if (dueDate.isAfter(startDate) &&
              dueDate.isBefore(endDate.add(const Duration(days: 1)))) {
            // إجمالي المديونية
            final amount =
                double.tryParse(dueItem['amount']?.toString() ?? '0') ?? 0;
            totalDebt += amount;
          }
        });
      }

      // إنشاء بيانات الرسم البياني
      final chartData = [
        {'category': 'إيرادات', 'value': totalRevenue},
        {'category': 'مصروفات', 'value': totalExpenses},
        {'category': 'أرباح', 'value': totalProfit},
        {'category': 'ضرائب', 'value': totalTax},
        {'category': 'ديون', 'value': totalDebt},
      ];

      // إنشاء بيانات الجدول
      final tableData = [
        {'category': 'مبيعات', 'amount': totalRevenue, 'type': 'إيرادات'},
        {'category': 'مصروفات', 'amount': totalExpenses, 'type': 'مصروفات'},
        {'category': 'أرباح', 'amount': totalProfit, 'type': 'أرباح'},
        {'category': 'ضرائب', 'amount': totalTax, 'type': 'ضرائب'},
        {'category': 'ديون', 'amount': totalDebt, 'type': 'ديون'},
      ];

      // إنشاء بيانات التقرير
      final data = {
        'totalRevenue': totalRevenue,
        'totalExpenses': totalExpenses,
        'totalProfit': totalProfit,
        'totalTax': totalTax,
        'totalDebt': totalDebt,
      };

      return report.copyWith(
        data: data,
        chartData: chartData,
        tableData: tableData,
      );
    } catch (e) {
      debugPrint('خطأ في توليد بيانات التقرير المالي: $e');

      // إرجاع تقرير فارغ في حالة حدوث خطأ
      return report.copyWith(
        data: {
          'totalRevenue': 0,
          'totalExpenses': 0,
          'totalProfit': 0,
          'totalTax': 0,
          'totalDebt': 0,
        },
        chartData: [],
        tableData: [],
      );
    }
  }

  /// توليد بيانات تقرير مخصص
  Future<ReportModel> _generateCustomReportData(ReportModel report) async {
    // بيانات حقيقية من قاعدة البيانات
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // استخدام معلمات التقرير المخصص
      final customType = report.parameters['customType'] ?? 'sales_by_category';

      // تحديد نطاق التاريخ بناءً على فترة التقرير
      DateTime startDate;
      DateTime endDate = DateTime.now();

      switch (report.period) {
        case ReportPeriod.daily:
          startDate = DateTime(endDate.year, endDate.month, endDate.day);
          break;
        case ReportPeriod.weekly:
          startDate = endDate.subtract(const Duration(days: 7));
          break;
        case ReportPeriod.monthly:
          startDate = DateTime(endDate.year, endDate.month, 1);
          break;
        case ReportPeriod.quarterly:
          startDate = DateTime(endDate.year, endDate.month - 3, 1);
          break;
        case ReportPeriod.yearly:
          startDate = DateTime(endDate.year, 1, 1);
          break;
        case ReportPeriod.custom:
          // استخدام التواريخ المخصصة من معلمات التقرير
          startDate = report.parameters['startDate'] != null
              ? DateTime.parse(report.parameters['startDate'])
              : DateTime(endDate.year, endDate.month, 1);
          endDate = report.parameters['endDate'] != null
              ? DateTime.parse(report.parameters['endDate'])
              : endDate;
          break;
      }

      // توليد تقرير مخصص حسب النوع
      switch (customType) {
        case 'sales_by_category':
          return await _generateSalesByCategoryReport(
              report, currentUser.uid, startDate, endDate);
        case 'top_customers':
          return await _generateTopCustomersReport(
              report, currentUser.uid, startDate, endDate);
        default:
          // تقرير افتراضي
          return report.copyWith(
            data: {
              'totalItems': 0,
              'totalValue': 0,
              'averageValue': 0,
            },
            chartData: [],
            tableData: [],
          );
      }
    } catch (e) {
      debugPrint('خطأ في توليد بيانات التقرير المخصص: $e');

      // إرجاع تقرير فارغ في حالة حدوث خطأ
      return report.copyWith(
        data: {
          'totalItems': 0,
          'totalValue': 0,
          'averageValue': 0,
        },
        chartData: [],
        tableData: [],
      );
    }
  }

  /// توليد تقرير المبيعات حسب الفئة
  Future<ReportModel> _generateSalesByCategoryReport(ReportModel report,
      String userId, DateTime startDate, DateTime endDate) async {
    // الحصول على بيانات المبيعات
    final salesRef = _database.ref('$userId/Sales Transition');
    final salesSnapshot = await salesRef.get();

    Map<String, double> categorySales = {};
    double totalValue = 0;
    int totalItems = 0;

    if (salesSnapshot.exists && salesSnapshot.value != null) {
      final salesData = salesSnapshot.value as Map<dynamic, dynamic>;

      salesData.forEach((key, value) {
        final saleData = Map<String, dynamic>.from(value as Map);
        final saleDate =
            DateTime.parse(saleData['invoiceDate'] ?? '2023-01-01');

        // التحقق من أن تاريخ البيع ضمن نطاق التقرير
        if (saleDate.isAfter(startDate) &&
            saleDate.isBefore(endDate.add(const Duration(days: 1)))) {
          // معالجة بيانات المنتجات
          final productList = saleData['productList'] as List<dynamic>?;
          if (productList != null) {
            for (var product in productList) {
              final productData = Map<String, dynamic>.from(product as Map);
              final category =
                  productData['productCategory'] ?? 'فئة غير معروفة';
              final price =
                  double.tryParse(productData['subTotal']?.toString() ?? '0') ??
                      0;

              // تحديث مبيعات الفئة
              categorySales[category] = (categorySales[category] ?? 0) + price;
              totalValue += price;
              totalItems++;
            }
          }
        }
      });
    }

    // تحويل بيانات الفئات إلى تنسيق الرسم البياني والجدول
    List<Map<String, dynamic>> chartData = [];
    List<Map<String, dynamic>> tableData = [];

    categorySales.forEach((category, value) {
      // إضافة إلى بيانات الرسم البياني
      chartData.add({
        'category': category,
        'value': value,
      });

      // إضافة إلى بيانات الجدول
      tableData.add({
        'name': category,
        'value': value,
        'percentage': totalValue > 0 ? (value / totalValue * 100) : 0,
      });
    });

    // ترتيب البيانات حسب القيمة (تنازليًا)
    chartData
        .sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));
    tableData
        .sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));

    // إنشاء بيانات التقرير
    final data = {
      'totalItems': totalItems,
      'totalValue': totalValue,
      'averageValue': totalItems > 0 ? totalValue / totalItems : 0,
    };

    return report.copyWith(
      data: data,
      chartData: chartData,
      tableData: tableData,
    );
  }

  /// توليد تقرير أفضل العملاء
  Future<ReportModel> _generateTopCustomersReport(ReportModel report,
      String userId, DateTime startDate, DateTime endDate) async {
    // الحصول على بيانات المبيعات
    final salesRef = _database.ref('$userId/Sales Transition');
    final salesSnapshot = await salesRef.get();

    Map<String, Map<String, dynamic>> customerSales = {};
    double totalValue = 0;
    int totalItems = 0;

    if (salesSnapshot.exists && salesSnapshot.value != null) {
      final salesData = salesSnapshot.value as Map<dynamic, dynamic>;

      salesData.forEach((key, value) {
        final saleData = Map<String, dynamic>.from(value as Map);
        final saleDate =
            DateTime.parse(saleData['invoiceDate'] ?? '2023-01-01');

        // التحقق من أن تاريخ البيع ضمن نطاق التقرير
        if (saleDate.isAfter(startDate) &&
            saleDate.isBefore(endDate.add(const Duration(days: 1)))) {
          final customerName = saleData['customerName'] ?? 'عميل غير معروف';
          final customerPhone = saleData['customerPhone'] ?? '';
          final totalAmount =
              double.tryParse(saleData['totalAmount']?.toString() ?? '0') ?? 0;

          // تحديث مبيعات العميل
          if (!customerSales.containsKey(customerName)) {
            customerSales[customerName] = {
              'name': customerName,
              'phone': customerPhone,
              'totalSales': 0.0,
              'orderCount': 0,
            };
          }

          customerSales[customerName]!['totalSales'] =
              (customerSales[customerName]!['totalSales'] as double) +
                  totalAmount;
          customerSales[customerName]!['orderCount'] =
              (customerSales[customerName]!['orderCount'] as int) + 1;

          totalValue += totalAmount;
          totalItems++;
        }
      });
    }

    // تحويل بيانات العملاء إلى تنسيق الرسم البياني والجدول
    List<Map<String, dynamic>> chartData = [];
    List<Map<String, dynamic>> tableData = [];

    customerSales.forEach((customerName, customerData) {
      final totalSales = customerData['totalSales'] as double;

      // إضافة إلى بيانات الرسم البياني
      chartData.add({
        'category': customerName,
        'value': totalSales,
      });

      // إضافة إلى بيانات الجدول
      tableData.add({
        'name': customerName,
        'value': totalSales,
        'percentage': totalValue > 0 ? (totalSales / totalValue * 100) : 0,
        'orderCount': customerData['orderCount'],
        'phone': customerData['phone'],
      });
    });

    // ترتيب البيانات حسب القيمة (تنازليًا)
    chartData
        .sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));
    tableData
        .sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));

    // اقتصار البيانات على أفضل 10 عملاء
    if (chartData.length > 10) {
      chartData = chartData.sublist(0, 10);
    }
    if (tableData.length > 10) {
      tableData = tableData.sublist(0, 10);
    }

    // إنشاء بيانات التقرير
    final data = {
      'totalItems': totalItems,
      'totalValue': totalValue,
      'averageValue': totalItems > 0 ? totalValue / totalItems : 0,
      'customerCount': customerSales.length,
    };

    return report.copyWith(
      data: data,
      chartData: chartData,
      tableData: tableData,
    );
  }
}

/// مزود خدمة التقارير
final reportServiceProvider = Provider<ReportService>((ref) {
  return ReportService();
});

/// مزود تقارير المستخدم الحالي
final currentUserReportsProvider =
    FutureProvider<List<ReportModel>>((ref) async {
  return await ReportService().getCurrentUserReports();
});
