import 'package:firebase_database/firebase_database.dart';

import 'package:mobile_pos/models/hrm_models.dart';

class HRMRepository {
  final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // الحصول على قائمة الموظفين
  Stream<List<EmployeeModel>> getEmployeeList() {
    return _database.child('HRM/Employees').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<EmployeeModel> employees = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> employeeMap = {};
          value.forEach((k, v) {
            employeeMap[k.toString()] = v;
          });
          employees.add(EmployeeModel.fromMap(employeeMap, key.toString()));
        }
      });
      return employees;
    });
  }

  // إضافة موظف جديد
  Future<void> addEmployee(EmployeeModel employee) async {
    try {
      final newEmployeeRef = _database.child('HRM/Employees').push();
      await newEmployeeRef.set(employee.toMap());
    } catch (e) {
      // خطأ في إضافة موظف
      rethrow;
    }
  }

  // تحديث بيانات موظف
  Future<void> updateEmployee(EmployeeModel employee) async {
    try {
      await _database
          .child('HRM/Employees/${employee.id}')
          .update(employee.toMap());
    } catch (e) {
      // خطأ في تحديث بيانات موظف
      rethrow;
    }
  }

  // حذف موظف
  Future<void> deleteEmployee(String employeeId) async {
    try {
      await _database.child('HRM/Employees/$employeeId').remove();
    } catch (e) {
      // خطأ في حذف موظف
      rethrow;
    }
  }

  // الحصول على قائمة الأقسام
  Stream<List<DepartmentModel>> getDepartmentList() {
    return _database.child('HRM/Departments').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<DepartmentModel> departments = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> departmentMap = {};
          value.forEach((k, v) {
            departmentMap[k.toString()] = v;
          });
          departments.add(DepartmentModel(
            id: key.toString(),
            name: departmentMap['name'] ?? '',
            description: departmentMap['description'] ?? '',
          ));
        }
      });
      return departments;
    });
  }

  // الحصول على قائمة المسميات الوظيفية
  Stream<List<DesignationModel>> getDesignationList() {
    return _database.child('HRM/Designations').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<DesignationModel> designations = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> designationMap = {};
          value.forEach((k, v) {
            designationMap[k.toString()] = v;
          });
          designations
              .add(DesignationModel.fromMap(designationMap, key.toString()));
        }
      });
      return designations;
    });
  }

  // إضافة مسمى وظيفي جديد
  Future<void> addDesignation(DesignationModel designation) async {
    try {
      final newDesignationRef = _database.child('HRM/Designations').push();
      await newDesignationRef.set(designation.toMap());
    } catch (e) {
      // خطأ في إضافة مسمى وظيفي
      rethrow;
    }
  }

  // تحديث مسمى وظيفي
  Future<void> updateDesignation(DesignationModel designation) async {
    try {
      await _database
          .child('HRM/Designations/${designation.id}')
          .update(designation.toMap());
    } catch (e) {
      // خطأ في تحديث مسمى وظيفي
      rethrow;
    }
  }

  // حذف مسمى وظيفي
  Future<void> deleteDesignation(String designationId) async {
    try {
      await _database.child('HRM/Designations/$designationId').remove();
    } catch (e) {
      // خطأ في حذف مسمى وظيفي
      rethrow;
    }
  }

  // الحصول على قائمة الرواتب
  Stream<List<SalaryModel>> getSalaryList() {
    return _database.child('HRM/Salaries').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      List<SalaryModel> salaries = [];
      data.forEach((key, value) {
        if (value is Map<dynamic, dynamic>) {
          Map<String, dynamic> salaryMap = {};
          value.forEach((k, v) {
            salaryMap[k.toString()] = v;
          });
          salaries.add(SalaryModel.fromMap(salaryMap, key.toString()));
        }
      });
      return salaries;
    });
  }

  // إضافة راتب جديد
  Future<void> addSalary(SalaryModel salary) async {
    try {
      final newSalaryRef = _database.child('HRM/Salaries').push();
      await newSalaryRef.set(salary.toMap());
    } catch (e) {
      // خطأ في إضافة راتب
      rethrow;
    }
  }

  // تحديث راتب
  Future<void> updateSalary(SalaryModel salary) async {
    try {
      await _database.child('HRM/Salaries/${salary.id}').update(salary.toMap());
    } catch (e) {
      // خطأ في تحديث راتب
      rethrow;
    }
  }

  // حذف راتب
  Future<void> deleteSalary(String salaryId) async {
    try {
      await _database.child('HRM/Salaries/$salaryId').remove();
    } catch (e) {
      // خطأ في حذف راتب
      rethrow;
    }
  }
}
