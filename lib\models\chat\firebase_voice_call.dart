import 'package:mobile_pos/models/chat/voice_call.dart';

/// نموذج المكالمة الصوتية في Firebase
class FirebaseVoiceCall {
  String? id;
  String? callId;
  String? callerId;
  String? recipientId;
  String? status;
  DateTime? startTime;
  DateTime? endTime;
  int? duration;
  DateTime? createdAt;
  DateTime? updatedAt;

  // المُنشئ
  FirebaseVoiceCall({
    this.id,
    this.callId,
    this.callerId,
    this.recipientId,
    this.status,
    this.startTime,
    this.endTime,
    this.duration,
    this.createdAt,
    this.updatedAt,
  });

  // تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'callId': callId,
      'callerId': callerId,
      'recipientId': recipientId,
      'status': status,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // إنشاء نموذج من Map
  factory FirebaseVoiceCall.fromMap(Map<String, dynamic> map) {
    return FirebaseVoiceCall(
      id: map['id'],
      callId: map['callId'],
      callerId: map['callerId'],
      recipientId: map['recipientId'],
      status: map['status'],
      startTime:
          map['startTime'] != null ? DateTime.parse(map['startTime']) : null,
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime']) : null,
      duration: map['duration'],
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  // تحويل VoiceCall إلى FirebaseVoiceCall
  static FirebaseVoiceCall fromVoiceCall(VoiceCall call) {
    return FirebaseVoiceCall(
      id: call.objectId,
      callId: call.callId,
      callerId: call.callerId,
      recipientId: call.recipientId,
      status: call.status,
      startTime: call.startTime,
      endTime: call.endTime,
      duration: call.duration,
      createdAt: call.createdAt,
      updatedAt: call.updatedAt,
    );
  }

  // تحويل FirebaseVoiceCall إلى VoiceCall
  VoiceCall toVoiceCall() {
    return VoiceCall(
      objectId: id,
      callId: callId,
      callerId: callerId,
      recipientId: recipientId,
      status: status,
      startTime: startTime,
      endTime: endTime,
      duration: duration,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
