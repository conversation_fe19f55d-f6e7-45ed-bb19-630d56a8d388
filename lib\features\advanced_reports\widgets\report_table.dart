// بسم الله الرحمن الرحيم
// جدول التقرير - مكون يعرض بيانات التقرير في شكل جدول

import 'package:flutter/material.dart';
import 'package:data_table_2/data_table_2.dart';

/// جدول التقرير
class ReportTable extends StatefulWidget {
  /// ينشئ جدول التقرير
  const ReportTable({
    super.key,
    required this.data,
    this.title,
    this.columnLabels,
    this.onRowTap,
    this.sortable = true,
    this.showActions = true,
    this.showCheckboxes = false,
    this.showPagination = true,
    this.rowsPerPage = 10,
    this.customCellBuilder,
  });

  /// بيانات الجدول
  final List<Map<String, dynamic>> data;

  /// عنوان الجدول (اختياري)
  final String? title;

  /// تسميات الأعمدة (اختياري)
  final Map<String, String>? columnLabels;

  /// حدث النقر على الصف (اختياري)
  final Function(Map<String, dynamic>)? onRowTap;

  /// هل الجدول قابل للترتيب؟
  final bool sortable;

  /// هل يتم عرض الإجراءات؟
  final bool showActions;

  /// هل يتم عرض مربعات الاختيار؟
  final bool showCheckboxes;

  /// هل يتم عرض الترقيم؟
  final bool showPagination;

  /// عدد الصفوف في الصفحة
  final int rowsPerPage;

  /// باني خلية مخصص (اختياري)
  final Widget Function(String columnName, dynamic value)? customCellBuilder;

  @override
  State<ReportTable> createState() => _ReportTableState();
}

class _ReportTableState extends State<ReportTable> {
  int _currentPage = 0;
  int _rowsPerPage = 10;
  String? _sortColumn;
  bool _sortAscending = true;
  List<Map<String, dynamic>> _filteredData = [];
  final List<Map<String, dynamic>> _selectedRows = [];
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _rowsPerPage = widget.rowsPerPage;
    _filteredData = List.from(widget.data);
  }

  @override
  void didUpdateWidget(ReportTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      _filteredData = List.from(widget.data);
      _applySort();
      _applySearch(_searchController.text);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // تطبيق الترتيب
  void _applySort() {
    if (_sortColumn != null) {
      _filteredData.sort((a, b) {
        final aValue = a[_sortColumn];
        final bValue = b[_sortColumn];

        if (aValue == null && bValue == null) {
          return 0;
        } else if (aValue == null) {
          return _sortAscending ? -1 : 1;
        } else if (bValue == null) {
          return _sortAscending ? 1 : -1;
        }

        int comparison;
        if (aValue is num && bValue is num) {
          comparison = aValue.compareTo(bValue);
        } else {
          comparison = aValue.toString().compareTo(bValue.toString());
        }

        return _sortAscending ? comparison : -comparison;
      });
    }
  }

  // تطبيق البحث
  void _applySearch(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredData = List.from(widget.data);
        _applySort();
      });
      return;
    }

    setState(() {
      _filteredData = widget.data.where((row) {
        return row.values.any((value) {
          return value.toString().toLowerCase().contains(query.toLowerCase());
        });
      }).toList();
      _applySort();
    });
  }

  // الحصول على أسماء الأعمدة
  List<String> _getColumnNames() {
    if (widget.data.isEmpty) {
      return [];
    }
    return widget.data.first.keys.toList();
  }

  // الحصول على تسمية العمود
  String _getColumnLabel(String columnName) {
    if (widget.columnLabels != null &&
        widget.columnLabels!.containsKey(columnName)) {
      return widget.columnLabels![columnName]!;
    }
    return columnName;
  }

  // الحصول على بيانات الصفحة الحالية
  List<Map<String, dynamic>> _getCurrentPageData() {
    if (!widget.showPagination) {
      return _filteredData;
    }

    final startIndex = _currentPage * _rowsPerPage;
    final endIndex = startIndex + _rowsPerPage;

    if (startIndex >= _filteredData.length) {
      return [];
    }

    return _filteredData.sublist(
      startIndex,
      endIndex > _filteredData.length ? _filteredData.length : endIndex,
    );
  }

  // التحقق مما إذا كان الصف محددًا
  bool _isRowSelected(Map<String, dynamic> row) {
    return _selectedRows.contains(row);
  }

  // تبديل تحديد الصف
  void _toggleRowSelection(Map<String, dynamic> row) {
    setState(() {
      if (_isRowSelected(row)) {
        _selectedRows.remove(row);
      } else {
        _selectedRows.add(row);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final columnNames = _getColumnNames();
    final currentPageData = _getCurrentPageData();

    if (widget.data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان الجدول وشريط البحث
        if (widget.title != null || widget.showActions) ...[
          Row(
            children: [
              if (widget.title != null) ...[
                Text(
                  widget.title!,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
              ],
              if (widget.showActions) ...[
                SizedBox(
                  width: 200,
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      hintText: 'بحث...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                    ),
                    onChanged: _applySearch,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    setState(() {
                      _searchController.clear();
                      _filteredData = List.from(widget.data);
                      _sortColumn = null;
                      _currentPage = 0;
                    });
                  },
                  tooltip: 'تحديث',
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
        ],

        // جدول البيانات
        Expanded(
          child: DataTable2(
            columnSpacing: 12,
            horizontalMargin: 12,
            minWidth: 600,
            sortColumnIndex:
                _sortColumn != null ? columnNames.indexOf(_sortColumn!) : null,
            sortAscending: _sortAscending,
            showCheckboxColumn: widget.showCheckboxes,
            columns: [
              if (widget.showCheckboxes)
                const DataColumn2(
                  label: Text(''),
                  size: ColumnSize.S,
                ),
              ...columnNames.map((columnName) {
                return DataColumn2(
                  label: Text(_getColumnLabel(columnName)),
                  tooltip: _getColumnLabel(columnName),
                  onSort: widget.sortable
                      ? (columnIndex, ascending) {
                          setState(() {
                            _sortColumn = columnName;
                            _sortAscending = ascending;
                            _applySort();
                          });
                        }
                      : null,
                );
              }),
            ],
            rows: currentPageData.map((row) {
              return DataRow2(
                selected: _isRowSelected(row),
                onSelectChanged: widget.showCheckboxes
                    ? (selected) {
                        if (selected != null) {
                          _toggleRowSelection(row);
                        }
                      }
                    : null,
                onTap: widget.onRowTap != null
                    ? () => widget.onRowTap!(row)
                    : null,
                cells: [
                  if (widget.showCheckboxes)
                    DataCell(
                      Checkbox(
                        value: _isRowSelected(row),
                        onChanged: (selected) {
                          if (selected != null) {
                            _toggleRowSelection(row);
                          }
                        },
                      ),
                    ),
                  ...columnNames.map((columnName) {
                    final value = row[columnName];
                    if (widget.customCellBuilder != null) {
                      return DataCell(
                          widget.customCellBuilder!(columnName, value));
                    }
                    return DataCell(Text(value?.toString() ?? ''));
                  }),
                ],
              );
            }).toList(),
          ),
        ),

        // ترقيم الصفحات
        if (widget.showPagination && _filteredData.length > _rowsPerPage) ...[
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.first_page),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage = 0;
                        });
                      }
                    : null,
                tooltip: 'الصفحة الأولى',
              ),
              IconButton(
                icon: const Icon(Icons.navigate_before),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage--;
                        });
                      }
                    : null,
                tooltip: 'الصفحة السابقة',
              ),
              Text(
                'الصفحة ${_currentPage + 1} من ${(_filteredData.length / _rowsPerPage).ceil()}',
                style: const TextStyle(fontSize: 14),
              ),
              IconButton(
                icon: const Icon(Icons.navigate_next),
                onPressed: _currentPage <
                        (_filteredData.length / _rowsPerPage).ceil() - 1
                    ? () {
                        setState(() {
                          _currentPage++;
                        });
                      }
                    : null,
                tooltip: 'الصفحة التالية',
              ),
              IconButton(
                icon: const Icon(Icons.last_page),
                onPressed: _currentPage <
                        (_filteredData.length / _rowsPerPage).ceil() - 1
                    ? () {
                        setState(() {
                          _currentPage =
                              (_filteredData.length / _rowsPerPage).ceil() - 1;
                        });
                      }
                    : null,
                tooltip: 'الصفحة الأخيرة',
              ),
              const SizedBox(width: 16),
              DropdownButton<int>(
                value: _rowsPerPage,
                items: [5, 10, 20, 50, 100].map((count) {
                  return DropdownMenuItem<int>(
                    value: count,
                    child: Text('$count صف في الصفحة'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _rowsPerPage = value;
                      _currentPage = 0;
                    });
                  }
                },
              ),
            ],
          ),
        ],
      ],
    );
  }
}
