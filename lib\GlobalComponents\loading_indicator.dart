// ignore_for_file: use_super_parameters, deprecated_member_use

import 'package:flutter/material.dart';
import '../constant.dart';

/// A reusable loading indicator widget that can be used throughout the app
/// to provide a consistent loading experience
class LoadingIndicatorWidget extends StatelessWidget {
  /// Creates a loading indicator with optional customization
  const LoadingIndicatorWidget({
    Key? key,
    this.color,
    this.size = 40.0,
    this.strokeWidth = 4.0,
    this.message,
  }) : super(key: key);

  /// The color of the loading indicator. Defaults to kMainColor if not provided.
  final Color? color;

  /// The size of the loading indicator.
  final double size;

  /// The stroke width of the loading indicator.
  final double strokeWidth;

  /// An optional message to display below the loading indicator.
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(color ?? kMainColor),
              strokeWidth: strokeWidth,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: color ?? kMainColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A full screen loading indicator that blocks user interaction
class FullScreenLoadingIndicator extends StatelessWidget {
  /// Creates a full screen loading indicator with optional customization
  // ignore:
  const FullScreenLoadingIndicator({
    Key? key,
    this.color,
    this.backgroundColor,
    this.message,
  }) : super(key: key);

  /// The color of the loading indicator. Defaults to kMainColor if not provided.
  final Color? color;

  /// The background color of the loading screen. Defaults to a semi-transparent white if not provided.
  final Color? backgroundColor;

  /// An optional message to display below the loading indicator.
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? Colors.white.withOpacity(0.8),
      child: LoadingIndicatorWidget(
        color: color,
        message: message,
      ),
    );
  }
}
