// بسم الله الرحمن الرحيم
// بطاقة المنتج - مكون يعرض معلومات المنتج في شكل بطاقة

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/product_model.dart';
import '../../core/theme/app_theme.dart';

/// بطاقة المنتج
class ProductCard extends StatelessWidget {
  /// ينشئ بطاقة المنتج
  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
    this.showQuantity = true,
    this.showPrice = true,
    this.showCategory = true,
    this.categoryName,
    this.compact = false,
  });

  /// المنتج
  final ProductModel product;

  /// حدث النقر على البطاقة
  final VoidCallback? onTap;

  /// حدث تعديل المنتج
  final VoidCallback? onEdit;

  /// حدث حذف المنتج
  final VoidCallback? onDelete;

  /// هل يتم عرض الإجراءات؟
  final bool showActions;

  /// هل يتم عرض الكمية؟
  final bool showQuantity;

  /// هل يتم عرض السعر؟
  final bool showPrice;

  /// هل يتم عرض الفئة؟
  final bool showCategory;

  /// اسم الفئة (اختياري)
  final String? categoryName;

  /// هل البطاقة مضغوطة؟
  final bool compact;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(
      symbol: 'جنيه',
      decimalDigits: 2,
    );

    // تحديد لون حالة المخزون
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (product.status) {
      case ProductStatus.available:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'متوفر';
        break;
      case ProductStatus.lowStock:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        statusText = 'منخفض المخزون';
        break;
      case ProductStatus.outOfStock:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = 'نفذ من المخزون';
        break;
      case ProductStatus.inactive:
        statusColor = Colors.grey;
        statusIcon = Icons.block;
        statusText = 'غير نشط';
        break;
    }

    if (compact) {
      return Card(
        elevation: 2,
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                // صورة المنتج
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.lightGreyColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: product.imagePath != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            product.imagePath!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : const Icon(
                          Icons.inventory,
                          size: 30,
                          color: AppColors.greyTextColor,
                        ),
                ),
                const SizedBox(width: 12),

                // معلومات المنتج
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: theme.textTheme.titleMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      if (showPrice)
                        Text(
                          currencyFormat.format(product.finalPrice),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.mainColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      if (showQuantity)
                        Row(
                          children: [
                            Icon(
                              statusIcon,
                              size: 14,
                              color: statusColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${product.quantity} ${product.unit}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: statusColor,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),

                // أزرار الإجراءات
                if (showActions) ...[
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: onEdit,
                    tooltip: 'تعديل',
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: onDelete,
                    tooltip: 'حذف',
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج
            Container(
              width: double.infinity,
              height: 120,
              decoration: const BoxDecoration(
                color: AppColors.lightGreyColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
              child: product.imagePath != null
                  ? ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                      child: Image.network(
                        product.imagePath!,
                        fit: BoxFit.cover,
                      ),
                    )
                  : const Center(
                      child: Icon(
                        Icons.inventory,
                        size: 50,
                        color: AppColors.greyTextColor,
                      ),
                    ),
            ),

            // معلومات المنتج
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          product.name,
                          style: theme.textTheme.titleMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (showActions)
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.more_vert),
                          onSelected: (value) {
                            if (value == 'edit' && onEdit != null) {
                              onEdit!();
                            } else if (value == 'delete' && onDelete != null) {
                              onDelete!();
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem<String>(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit),
                                  SizedBox(width: 8),
                                  Text('تعديل'),
                                ],
                              ),
                            ),
                            const PopupMenuItem<String>(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete),
                                  SizedBox(width: 8),
                                  Text('حذف'),
                                ],
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (showCategory && categoryName != null) ...[
                    Text(
                      'الفئة: $categoryName',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.greyTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                  if (showPrice) ...[
                    Row(
                      children: [
                        Text(
                          'السعر: ',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          currencyFormat.format(product.finalPrice),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.mainColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (showQuantity) ...[
                    Row(
                      children: [
                        Text(
                          'الكمية: ',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          '${product.quantity} ${product.unit}',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          statusIcon,
                          size: 16,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          statusText,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
