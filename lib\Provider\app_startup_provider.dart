import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Provider/debt_consistency_provider.dart';

/// مزود لبدء التطبيق
final appStartupProvider = Provider<AppStartupService>((ref) {
  return AppStartupService(ref);
});

/// حالة بدء التطبيق
final appStartupStateProvider = StateProvider<AppStartupState>((ref) {
  return AppStartupState(
    isInitializing: false,
    isInitialized: false,
    error: null,
  );
});

/// خدمة بدء التطبيق
class AppStartupService {
  final Ref _ref;

  AppStartupService(this._ref);

  /// تهيئة التطبيق
  Future<void> initialize() async {
    // تحديث حالة البدء
    _ref.read(appStartupStateProvider.notifier).state = AppStartupState(
      isInitializing: true,
      isInitialized: false,
      error: null,
    );

    try {
      // تنفيذ عمليات البدء
      await _performStartupTasks();

      // تحديث حالة البدء
      _ref.read(appStartupStateProvider.notifier).state = AppStartupState(
        isInitializing: false,
        isInitialized: true,
        error: null,
      );
    } catch (e) {
      // تحديث حالة البدء
      _ref.read(appStartupStateProvider.notifier).state = AppStartupState(
        isInitializing: false,
        isInitialized: false,
        error: e.toString(),
      );
    }
  }

  /// تنفيذ عمليات البدء
  Future<void> _performStartupTasks() async {
    // التحقق من اتساق بيانات المديونية
    await _checkDebtConsistency();

    // يمكن إضافة المزيد من عمليات البدء هنا
  }

  /// التحقق من اتساق بيانات المديونية
  Future<void> _checkDebtConsistency() async {
    // الحصول على خدمة التحقق من اتساق البيانات
    final consistencyService = _ref.read(debtConsistencyProvider);

    // التحقق من اتساق بيانات المديونية
    await consistencyService.checkAllCustomersDebtConsistency();

    // الحصول على حالة التحقق
    final state = _ref.read(debtConsistencyStateProvider);

    // إذا كان هناك حالات غير متسقة، يمكن تصحيحها تلقائيًا
    if (state.inconsistentCustomers > 0) {
      // يمكن تفعيل هذا الكود لتصحيح الحالات غير المتسقة تلقائيًا
      // await consistencyService.fixAllCustomersDebtInconsistency();
    }
  }
}

/// حالة بدء التطبيق
class AppStartupState {
  final bool isInitializing;
  final bool isInitialized;
  final String? error;

  AppStartupState({
    required this.isInitializing,
    required this.isInitialized,
    required this.error,
  });
}
