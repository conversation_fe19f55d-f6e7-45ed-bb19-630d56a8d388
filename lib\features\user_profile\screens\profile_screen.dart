// بسم الله الرحمن الرحيم
// شاشة الملف الشخصي - تعرض معلومات المستخدم وتتيح تعديلها

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/user_profile_model.dart';
import '../services/user_profile_service.dart';

/// شاشة الملف الشخصي
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  static const String routeName = '/profile';

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _isEditing = false;
  bool _isLoading = false;
  File? _imageFile;
  final ImagePicker _picker = ImagePicker();

  // وحدات التحكم في النص
  late TextEditingController _nameController;
  late TextEditingController _bioController;
  late TextEditingController _addressController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _bioController = TextEditingController();
    _addressController = TextEditingController();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _bioController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  // تحميل بيانات المستخدم في وحدات التحكم
  void _loadUserData(UserProfileModel profile) {
    _nameController.text = profile.displayName ?? '';
    _bioController.text = profile.bio ?? '';
    _addressController.text = profile.address ?? '';
  }

  // اختيار صورة من المعرض
  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _imageFile = File(image.path);
      });
    }
  }

  // حفظ التغييرات
  Future<void> _saveChanges(UserProfileModel? currentProfile) async {
    if (currentProfile == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث الصورة إذا تم اختيارها
      String? photoURL = currentProfile.photoURL;
      if (_imageFile != null) {
        photoURL = await ref
            .read(userProfileServiceProvider)
            .uploadProfileImage(currentProfile.uid, _imageFile!);
      }

      // تحديث ملف المستخدم
      final updatedProfile = currentProfile.copyWith(
        displayName: _nameController.text,
        bio: _bioController.text,
        address: _addressController.text,
        photoURL: photoURL,
        lastActive: DateTime.now(),
      );

      final success = await ref
          .read(userProfileServiceProvider)
          .updateUserProfile(updatedProfile);

      if (success) {
        // تحديث مزود ملف المستخدم
        // ignore: unused_result
        ref.refresh(currentUserProfileProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حفظ التغييرات بنجاح')),
          );
          setState(() {
            _isEditing = false;
          });
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل حفظ التغييرات')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProfileAsync = ref.watch(currentUserProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        centerTitle: true,
        actions: [
          userProfileAsync.when(
            data: (profile) {
              if (profile != null) {
                return _isEditing
                    ? IconButton(
                        icon: const Icon(Icons.save),
                        onPressed: () => _saveChanges(profile),
                      )
                    : IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () {
                          _loadUserData(profile);
                          setState(() {
                            _isEditing = true;
                          });
                        },
                      );
              }
              return const SizedBox.shrink();
            },
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),
        ],
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري حفظ التغييرات...',
            )
          : userProfileAsync.when(
              data: (profile) {
                if (profile == null) {
                  return const Center(
                    child: Text('لم يتم العثور على ملف المستخدم'),
                  );
                }

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // صورة الملف الشخصي
                      GestureDetector(
                        onTap: _isEditing ? _pickImage : null,
                        child: Stack(
                          children: [
                            CircleAvatar(
                              radius: 60,
                              backgroundColor: AppColors.mainColor
                                  .withAlpha(26), // 0.1 * 255 = 26
                              backgroundImage: _imageFile != null
                                  ? FileImage(_imageFile!)
                                  : (profile.photoURL != null
                                      ? NetworkImage(profile.photoURL!)
                                      : null) as ImageProvider?,
                              child: (profile.photoURL == null &&
                                      _imageFile == null)
                                  ? const Icon(
                                      Icons.person,
                                      size: 60,
                                      color: AppColors.mainColor,
                                    )
                                  : null,
                            ),
                            if (_isEditing)
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: AppColors.mainColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // اسم المستخدم
                      _isEditing
                          ? TextField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'الاسم',
                                border: OutlineInputBorder(),
                              ),
                            )
                          : Text(
                              profile.displayName ?? 'بدون اسم',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                      const SizedBox(height: 8),

                      // معلومات الاتصال
                      if (!_isEditing) ...[
                        if (profile.email != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.email,
                                  color: AppColors.greyTextColor),
                              const SizedBox(width: 8),
                              Text(
                                profile.email!,
                                style: const TextStyle(
                                  color: AppColors.greyTextColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                        if (profile.phoneNumber != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.phone,
                                  color: AppColors.greyTextColor),
                              const SizedBox(width: 8),
                              Text(
                                profile.phoneNumber!,
                                style: const TextStyle(
                                  color: AppColors.greyTextColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                      const SizedBox(height: 24),

                      // نبذة عن المستخدم
                      const Text(
                        'نبذة عني',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _isEditing
                          ? TextField(
                              controller: _bioController,
                              maxLines: 3,
                              decoration: const InputDecoration(
                                labelText: 'نبذة عني',
                                border: OutlineInputBorder(),
                              ),
                            )
                          : Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                profile.bio ?? 'لا توجد نبذة',
                                style: const TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                      const SizedBox(height: 24),

                      // العنوان
                      const Text(
                        'العنوان',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _isEditing
                          ? TextField(
                              controller: _addressController,
                              decoration: const InputDecoration(
                                labelText: 'العنوان',
                                border: OutlineInputBorder(),
                              ),
                            )
                          : Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                profile.address ?? 'لا يوجد عنوان',
                                style: const TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                      const SizedBox(height: 24),

                      // معلومات إضافية
                      if (!_isEditing) ...[
                        const Text(
                          'معلومات إضافية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ListTile(
                          leading: const Icon(Icons.calendar_today),
                          title: const Text('تاريخ الانضمام'),
                          subtitle: Text(
                            profile.createdAt != null
                                ? '${profile.createdAt!.day}/${profile.createdAt!.month}/${profile.createdAt!.year}'
                                : 'غير معروف',
                          ),
                        ),
                        ListTile(
                          leading: const Icon(Icons.access_time),
                          title: const Text('آخر نشاط'),
                          subtitle: Text(
                            profile.lastActive != null
                                ? '${profile.lastActive!.day}/${profile.lastActive!.month}/${profile.lastActive!.year}'
                                : 'غير معروف',
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
              loading: () => const LoadingIndicatorWidget(
                message: 'جاري تحميل الملف الشخصي...',
              ),
              error: (error, stackTrace) => Center(
                child: Text('حدث خطأ: $error'),
              ),
            ),
    );
  }
}
