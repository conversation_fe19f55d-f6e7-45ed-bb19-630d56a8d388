import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';

import '../const.dart';
import '../model/customer_model.dart';

class CustomerRepo {
  Future<List<CustomerModel>> getAllCustomer() async {
    List<CustomerModel> allCustomerList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          await getUserID(); // هيحدث constUserId
        }
      }

      debugPrint('🔍 User ID: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب البيانات');
        return allCustomerList;
      }

      await FirebaseDatabase.instance
          .ref(constUserId)
          .child('Customers')
          .orderByKey()
          .get()
          .then((value) {
        debugPrint('📊 تم الحصول على البيانات من Firebase');
        debugPrint('📊 snapshot.exists: ${value.exists}');
        debugPrint('📊 عدد العناصر: ${value.children.length}');

        for (var element in value.children) {
          var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
          allCustomerList.add(data);
        }
      });

      final customerRef = FirebaseDatabase.instance.ref(constUserId).child('Customers');
      customerRef.keepSynced(true);

      debugPrint('✅ تم جلب ${allCustomerList.length} عميل بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات العملاء: $e');
    }
    return allCustomerList;
  }

  Future<List<CustomerModel>> getAllBuyer() async {
    List<CustomerModel> customerList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          await getUserID();
        }
      }

      debugPrint('🔍 User ID للمشترين: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات المشترين');
        return customerList;
      }

      await FirebaseDatabase.instance
          .ref(constUserId)
          .child('Customers')
          .orderByKey()
          .get()
          .then((value) {
        debugPrint('📊 تم الحصول على بيانات المشترين من Firebase - عدد العناصر: ${value.children.length}');

        for (var element in value.children) {
          var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
          if (data.type != "المورد") {
            customerList.add(data);
          }
        }
      });

      final customerRef = FirebaseDatabase.instance.ref(constUserId).child('Customers');
      customerRef.keepSynced(true);

      debugPrint('✅ تم جلب ${customerList.length} مشتري بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المشترين: $e');
    }
    return customerList;
  }

  Future<List<CustomerModel>> getAllSupplier() async {
    List<CustomerModel> supplierList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          await getUserID();
        }
      }

      debugPrint('🔍 User ID للموردين: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات الموردين');
        return supplierList;
      }

      await FirebaseDatabase.instance
          .ref(constUserId)
          .child('Customers')
          .orderByKey()
          .get()
          .then((value) {
        debugPrint('📊 تم الحصول على بيانات الموردين من Firebase - عدد العناصر: ${value.children.length}');

        for (var element in value.children) {
          var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
          if (data.type == "المورد") {
            supplierList.add(data);
          }
        }
      });

      final customerRef = FirebaseDatabase.instance.ref(constUserId).child('Customers');
      customerRef.keepSynced(true);

      debugPrint('✅ تم جلب ${supplierList.length} مورد بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات الموردين: $e');
    }
    return supplierList;
  }
}
