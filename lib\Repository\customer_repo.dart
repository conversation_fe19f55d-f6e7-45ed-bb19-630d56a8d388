import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/customer_model.dart';

class CustomerRepo {
  Future<List<CustomerModel>> getAllCustomer() async {
    List<CustomerModel> allCustomerList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        debugPrint('🔄 constUserId فارغ - جلب بيانات المستخدم من SharedPreferences');
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          debugPrint('🔄 constUserId ما زال فارغ - محاولة جلبه من getUserID');
          final userId = await getUserID();
          if (userId.isNotEmpty) {
            constUserId = userId;
          }
        }
      }

      debugPrint('🔍 User ID للعملاء: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات العملاء');
        return allCustomerList;
      }

      final snapshot = await FirebaseDatabaseService.ref(constUserId).child('Customers').orderByKey().get();
      debugPrint('📊 تم الحصول على البيانات من Firebase');
      debugPrint('📊 snapshot.exists: ${snapshot.exists}');
      debugPrint('📊 snapshot.value: ${snapshot.value}');
      debugPrint('📊 عدد العناصر: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
        allCustomerList.add(data);
      }

      debugPrint('✅ تم جلب ${allCustomerList.length} عميل بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات العملاء: $e');
    }
    return allCustomerList;
  }

  Future<List<CustomerModel>> getAllBuyer() async {
    List<CustomerModel> customerList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        debugPrint('🔄 constUserId فارغ - جلب بيانات المستخدم من SharedPreferences');
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          debugPrint('🔄 constUserId ما زال فارغ - محاولة جلبه من getUserID');
          final userId = await getUserID();
          if (userId.isNotEmpty) {
            constUserId = userId;
          }
        }
      }

      debugPrint('🔍 User ID للمشترين: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات المشترين');
        return customerList;
      }

      final snapshot = await FirebaseDatabaseService.ref(constUserId).child('Customers').orderByKey().get();
      debugPrint('📊 تم الحصول على بيانات المشترين من Firebase - عدد العناصر: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
        if (data.type != "المورد") {
          customerList.add(data);
        }
      }

      debugPrint('✅ تم جلب ${customerList.length} مشتري بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المشترين: $e');
    }
    return customerList;
  }

  Future<List<CustomerModel>> getAllSupplier() async {
    List<CustomerModel> supplierList = [];

    try {
      // التأكد من وجود constUserId
      if (constUserId.isEmpty) {
        debugPrint('🔄 constUserId فارغ - جلب بيانات المستخدم من SharedPreferences');
        await getUserDataFromLocal();
        if (constUserId.isEmpty) {
          debugPrint('🔄 constUserId ما زال فارغ - محاولة جلبه من getUserID');
          final userId = await getUserID();
          if (userId.isNotEmpty) {
            constUserId = userId;
          }
        }
      }

      debugPrint('🔍 User ID للموردين: $constUserId');

      if (constUserId.isEmpty) {
        debugPrint('❌ User ID فارغ - لا يمكن جلب بيانات الموردين');
        return supplierList;
      }

      final snapshot = await FirebaseDatabaseService.ref(constUserId).child('Customers').orderByKey().get();
      debugPrint('📊 تم الحصول على بيانات الموردين من Firebase - عدد العناصر: ${snapshot.children.length}');

      for (var element in snapshot.children) {
        var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
        if (data.type == "المورد") {
          supplierList.add(data);
        }
      }

      debugPrint('✅ تم جلب ${supplierList.length} مورد بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات الموردين: $e');
    }
    return supplierList;
  }
}
