// بسم الله الرحمن الرحيم
// شاشة قائمة المنتجات - تعرض قائمة المنتجات مع إمكانية البحث والتصفية

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/components/loading_indicator.dart';
import '../models/product_model.dart';

import '../services/inventory_service.dart';
import '../widgets/product_card.dart';
import 'product_details_screen.dart';
import 'add_product_screen.dart';
import 'barcode_scanner_screen.dart';

/// شاشة قائمة المنتجات
class ProductListScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة قائمة المنتجات
  const ProductListScreen({super.key});

  @override
  ConsumerState<ProductListScreen> createState() => _ProductListScreenState();
}

class _ProductListScreenState extends ConsumerState<ProductListScreen> {
  bool _isLoading = false;
  String _searchQuery = '';
  String? _selectedCategoryId;
  ProductStatus? _selectedStatus;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // تحديث البيانات
  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث مزودات البيانات
      await Future.wait([
        ref.refresh(allProductsProvider.future),
        ref.refresh(allCategoriesProvider.future),
      ]);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // البحث عن منتجات
  void _searchProducts(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  // تصفية المنتجات حسب الفئة
  void _filterByCategory(String? categoryId) {
    setState(() {
      _selectedCategoryId = categoryId;
    });
  }

  // تصفية المنتجات حسب الحالة
  void _filterByStatus(ProductStatus? status) {
    setState(() {
      _selectedStatus = status;
    });
  }

  // فتح شاشة تفاصيل المنتج
  void _openProductDetails(ProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(product: product),
      ),
    ).then((_) => _refreshData());
  }

  // فتح شاشة إضافة منتج جديد
  void _openAddProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddProductScreen(),
      ),
    ).then((_) => _refreshData());
  }

  // فتح شاشة تعديل منتج
  void _openEditProduct(ProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddProductScreen(product: product),
      ),
    ).then((_) => _refreshData());
  }

  // حذف منتج
  Future<void> _deleteProduct(ProductModel product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: Text('هل أنت متأكد من حذف المنتج "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final success =
            await ref.read(inventoryServiceProvider).deleteProduct(product.id);
        if (success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حذف المنتج بنجاح')),
            );
          }
          _refreshData();
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('فشل في حذف المنتج')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // فتح شاشة ماسح الباركود
  void _openBarcodeScanner() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BarcodeScannerScreen(),
      ),
    ).then((barcode) {
      if (barcode != null && barcode is String && barcode.isNotEmpty) {
        _searchController.text = barcode;
        _searchProducts(barcode);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final allProductsAsync = ref.watch(allProductsProvider);
    final allCategoriesAsync = ref.watch(allCategoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة المنتجات'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: _openBarcodeScanner,
            tooltip: 'مسح الباركود',
          ),
        ],
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل البيانات...',
            )
          : RefreshIndicator(
              onRefresh: _refreshData,
              child: Column(
                children: [
                  // شريط البحث والفلترة
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        // شريط البحث
                        TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'بحث عن منتج...',
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                _searchProducts('');
                              },
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onChanged: _searchProducts,
                        ),
                        const SizedBox(height: 16),

                        // فلاتر
                        Row(
                          children: [
                            // فلتر الفئة
                            Expanded(
                              child: allCategoriesAsync.when(
                                data: (categories) {
                                  return DropdownButtonFormField<String?>(
                                    decoration: const InputDecoration(
                                      labelText: 'الفئة',
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                    ),
                                    value: _selectedCategoryId,
                                    items: [
                                      const DropdownMenuItem<String?>(
                                        value: null,
                                        child: Text('جميع الفئات'),
                                      ),
                                      ...categories.map((category) {
                                        return DropdownMenuItem<String>(
                                          value: category.id,
                                          child: Text(category.name),
                                        );
                                      }),
                                    ],
                                    onChanged: _filterByCategory,
                                  );
                                },
                                loading: () =>
                                    const CircularProgressIndicator(),
                                error: (error, stackTrace) =>
                                    Text('حدث خطأ: $error'),
                              ),
                            ),
                            const SizedBox(width: 16),

                            // فلتر الحالة
                            Expanded(
                              child: DropdownButtonFormField<ProductStatus?>(
                                decoration: const InputDecoration(
                                  labelText: 'الحالة',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                ),
                                value: _selectedStatus,
                                items: const [
                                  DropdownMenuItem<ProductStatus?>(
                                    value: null,
                                    child: Text('جميع الحالات'),
                                  ),
                                  DropdownMenuItem<ProductStatus>(
                                    value: ProductStatus.available,
                                    child: Text('متوفر'),
                                  ),
                                  DropdownMenuItem<ProductStatus>(
                                    value: ProductStatus.lowStock,
                                    child: Text('منخفض المخزون'),
                                  ),
                                  DropdownMenuItem<ProductStatus>(
                                    value: ProductStatus.outOfStock,
                                    child: Text('نفذ من المخزون'),
                                  ),
                                  DropdownMenuItem<ProductStatus>(
                                    value: ProductStatus.inactive,
                                    child: Text('غير نشط'),
                                  ),
                                ],
                                onChanged: _filterByStatus,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // قائمة المنتجات
                  Expanded(
                    child: allProductsAsync.when(
                      data: (allProducts) {
                        return allCategoriesAsync.when(
                          data: (categories) {
                            // تطبيق الفلاتر
                            final filteredProducts = _filterProducts(
                              allProducts,
                              _searchQuery,
                              _selectedCategoryId,
                              _selectedStatus,
                            );

                            if (filteredProducts.isEmpty) {
                              return const Center(
                                child: Text('لا توجد منتجات مطابقة للبحث'),
                              );
                            }

                            // إنشاء خريطة للفئات
                            final categoryMap = <String, String>{};
                            for (final category in categories) {
                              categoryMap[category.id] = category.name;
                            }

                            return ListView.builder(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              itemCount: filteredProducts.length,
                              itemBuilder: (context, index) {
                                final product = filteredProducts[index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 12.0),
                                  child: ProductCard(
                                    product: product,
                                    categoryName:
                                        categoryMap[product.categoryId],
                                    onTap: () => _openProductDetails(product),
                                    onEdit: () => _openEditProduct(product),
                                    onDelete: () => _deleteProduct(product),
                                    compact: true,
                                  ),
                                );
                              },
                            );
                          },
                          loading: () => const LoadingIndicatorWidget(),
                          error: (error, stackTrace) => Text('حدث خطأ: $error'),
                        );
                      },
                      loading: () => const LoadingIndicatorWidget(),
                      error: (error, stackTrace) => Text('حدث خطأ: $error'),
                    ),
                  ),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _openAddProduct,
        tooltip: 'إضافة منتج',
        child: const Icon(Icons.add),
      ),
    );
  }

  // تصفية المنتجات
  List<ProductModel> _filterProducts(
    List<ProductModel> products,
    String searchQuery,
    String? categoryId,
    ProductStatus? status,
  ) {
    return products.where((product) {
      // تصفية حسب البحث
      final matchesSearch = searchQuery.isEmpty ||
          product.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          product.barcode.toLowerCase().contains(searchQuery.toLowerCase()) ||
          (product.description
                  ?.toLowerCase()
                  .contains(searchQuery.toLowerCase()) ??
              false);

      // تصفية حسب الفئة
      final matchesCategory =
          categoryId == null || product.categoryId == categoryId;

      // تصفية حسب الحالة
      final matchesStatus = status == null || product.status == status;

      return matchesSearch && matchesCategory && matchesStatus;
    }).toList();
  }
}
