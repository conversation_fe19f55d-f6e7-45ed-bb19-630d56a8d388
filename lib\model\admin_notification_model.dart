/// نموذج الإشعارات الإدارية الثابتة
class AdminNotificationModel {
  final String id;
  final String title;
  final String message;
  final String type; // info, warning, error, success, emergency, maintenance, update, promotional
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? actionUrl;
  final String? imageUrl;
  final Map<String, dynamic> customData;

  AdminNotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.actionUrl,
    this.imageUrl,
    this.customData = const {},
  });

  /// إنشاء نموذج من JSON
  factory AdminNotificationModel.fromJson(Map<String, dynamic> json) {
    return AdminNotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'info',
      isActive: json['isActive'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      actionUrl: json['actionUrl'],
      imageUrl: json['imageUrl'],
      customData: Map<String, dynamic>.from(json['customData'] ?? {}),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'actionUrl': actionUrl,
      'imageUrl': imageUrl,
      'customData': customData,
    };
  }

  /// نسخ النموذج مع تعديل بعض القيم
  AdminNotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? actionUrl,
    String? imageUrl,
    Map<String, dynamic>? customData,
  }) {
    return AdminNotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      customData: customData ?? this.customData,
    );
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return id.isNotEmpty && title.isNotEmpty && message.isNotEmpty;
  }

  /// التحقق من كون الإشعار طارئ
  bool get isEmergency {
    return type == 'emergency' || customData['emergency'] == true;
  }

  /// التحقق من كون الإشعار ترويجي
  bool get isPromotional {
    return type == 'promotional' || customData['promotional'] == true;
  }

  /// التحقق من انتهاء صلاحية الإشعار
  bool get isExpired {
    if (customData['expiryDate'] != null) {
      final expiryDate = DateTime.parse(customData['expiryDate']);
      return DateTime.now().isAfter(expiryDate);
    }
    return false;
  }

  /// الحصول على أولوية الإشعار
  String get priority {
    if (isEmergency) return 'high';
    if (type == 'warning' || type == 'error') return 'medium';
    return customData['priority'] ?? 'normal';
  }

  /// الحصول على لون الإشعار حسب النوع
  String get colorCode {
    switch (type) {
      case 'emergency':
        return '#FF0000'; // أحمر
      case 'error':
        return '#F44336'; // أحمر فاتح
      case 'warning':
        return '#FF9800'; // برتقالي
      case 'success':
        return '#4CAF50'; // أخضر
      case 'info':
        return '#2196F3'; // أزرق
      case 'maintenance':
        return '#9C27B0'; // بنفسجي
      case 'update':
        return '#00BCD4'; // سماوي
      case 'promotional':
        return '#FF5722'; // برتقالي محمر
      default:
        return '#757575'; // رمادي
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  String get iconCode {
    switch (type) {
      case 'emergency':
        return '🚨';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'success':
        return '✅';
      case 'info':
        return 'ℹ️';
      case 'maintenance':
        return '🔧';
      case 'update':
        return '🔄';
      case 'promotional':
        return '🎉';
      default:
        return '📢';
    }
  }

  /// تحويل النموذج إلى نص
  @override
  String toString() {
    return 'AdminNotificationModel(id: $id, title: $title, type: $type, isActive: $isActive)';
  }

  /// مقارنة النماذج
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminNotificationModel && other.id == id;
  }

  /// الحصول على hash code
  @override
  int get hashCode => id.hashCode;
}

/// أنواع الإشعارات المتاحة
class NotificationTypes {
  static const String info = 'info';
  static const String warning = 'warning';
  static const String error = 'error';
  static const String success = 'success';
  static const String emergency = 'emergency';
  static const String maintenance = 'maintenance';
  static const String update = 'update';
  static const String promotional = 'promotional';

  static const List<String> all = [
    info,
    warning,
    error,
    success,
    emergency,
    maintenance,
    update,
    promotional,
  ];

  /// الحصول على اسم النوع بالعربية
  static String getArabicName(String type) {
    switch (type) {
      case info:
        return 'معلومات';
      case warning:
        return 'تحذير';
      case error:
        return 'خطأ';
      case success:
        return 'نجاح';
      case emergency:
        return 'طوارئ';
      case maintenance:
        return 'صيانة';
      case update:
        return 'تحديث';
      case promotional:
        return 'ترويجي';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على وصف النوع
  static String getDescription(String type) {
    switch (type) {
      case info:
        return 'إشعار معلوماتي عام';
      case warning:
        return 'تحذير مهم للمستخدمين';
      case error:
        return 'إشعار خطأ أو مشكلة';
      case success:
        return 'إشعار نجاح عملية';
      case emergency:
        return 'إشعار طوارئ عاجل';
      case maintenance:
        return 'إشعار صيانة مجدولة';
      case update:
        return 'إشعار تحديث التطبيق';
      case promotional:
        return 'إشعار ترويجي أو عرض';
      default:
        return 'نوع غير محدد';
    }
  }
}
