class DeliveryCostModel {
  late String id;
  late String tripId;
  late String distanceTraveled;
  late String fuelPrice; // سعر اللتر
  late String fuelConsumption; // لتر مستهلك
  late String fuelCost; // تكلفة البنزين
  late String oilCost; // تكلفة الزيت
  late String maintenanceCost; // تكلفة الصيانة
  late String driverSalary; // راتب السائق
  late String otherCosts; // تكاليف أخرى
  late String totalCost; // إجمالي التكلفة
  late String costPerKm; // تكلفة الكيلومتر
  late String calculationDate;
  late String notes;

  DeliveryCostModel({
    required this.id,
    required this.tripId,
    required this.distanceTraveled,
    required this.fuelPrice,
    required this.fuelConsumption,
    required this.fuelCost,
    required this.oilCost,
    required this.maintenanceCost,
    required this.driverSalary,
    required this.otherCosts,
    required this.totalCost,
    required this.costPerKm,
    required this.calculationDate,
    required this.notes,
  });

  DeliveryCostModel.fromJson(Map<dynamic, dynamic> json) {
    id = json['id']?.toString() ?? '';
    tripId = json['tripId']?.toString() ?? '';
    distanceTraveled = json['distanceTraveled']?.toString() ?? '';
    fuelPrice = json['fuelPrice']?.toString() ?? '';
    fuelConsumption = json['fuelConsumption']?.toString() ?? '';
    fuelCost = json['fuelCost']?.toString() ?? '';
    oilCost = json['oilCost']?.toString() ?? '';
    maintenanceCost = json['maintenanceCost']?.toString() ?? '';
    driverSalary = json['driverSalary']?.toString() ?? '';
    otherCosts = json['otherCosts']?.toString() ?? '';
    totalCost = json['totalCost']?.toString() ?? '';
    costPerKm = json['costPerKm']?.toString() ?? '';
    calculationDate = json['calculationDate']?.toString() ?? '';
    notes = json['notes']?.toString() ?? '';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'id': id,
        'tripId': tripId,
        'distanceTraveled': distanceTraveled,
        'fuelPrice': fuelPrice,
        'fuelConsumption': fuelConsumption,
        'fuelCost': fuelCost,
        'oilCost': oilCost,
        'maintenanceCost': maintenanceCost,
        'driverSalary': driverSalary,
        'otherCosts': otherCosts,
        'totalCost': totalCost,
        'costPerKm': costPerKm,
        'calculationDate': calculationDate,
        'notes': notes,
      };
}
