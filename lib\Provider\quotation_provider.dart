import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/quotation_model.dart';
import 'package:mobile_pos/repository/quotation_repo.dart';

// Provider for quotation repository
final quotationRepositoryProvider = Provider<QuotationRepository>((ref) {
  return QuotationRepository();
});

// Provider for quotation list
final quotationListProvider = StreamProvider<List<QuotationModel>>((ref) {
  final quotationRepo = ref.watch(quotationRepositoryProvider);
  return quotationRepo.getQuotationList();
});

// Provider for selected quotation
final selectedQuotationProvider = StateProvider<QuotationModel?>((ref) => null);

// Provider for quotation filter
final quotationFilterProvider = StateProvider<String>((ref) => 'all'); // all, pending, accepted, rejected, converted

// Provider for filtered quotation list
final filteredQuotationListProvider = Provider<List<QuotationModel>>((ref) {
  final quotations = ref.watch(quotationListProvider);
  final filter = ref.watch(quotationFilterProvider);

  return quotations.when(
    data: (data) {
      if (filter == 'all') {
        return data;
      } else {
        return data.where((quotation) => quotation.status == filter).toList();
      }
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Provider for quotation notifier
final quotationNotifierProvider = StateNotifierProvider<QuotationNotifier, AsyncValue<void>>((ref) {
  final quotationRepo = ref.watch(quotationRepositoryProvider);
  return QuotationNotifier(quotationRepo);
});

// Notifier for quotation operations
class QuotationNotifier extends StateNotifier<AsyncValue<void>> {
  final QuotationRepository _quotationRepo;

  QuotationNotifier(this._quotationRepo) : super(const AsyncValue.data(null));

  // Add a new quotation
  Future<void> addQuotation(QuotationModel quotation) async {
    state = const AsyncValue.loading();
    try {
      await _quotationRepo.addQuotation(quotation);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update an existing quotation
  Future<void> updateQuotation(QuotationModel quotation) async {
    state = const AsyncValue.loading();
    try {
      await _quotationRepo.updateQuotation(quotation);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Delete a quotation
  Future<void> deleteQuotation(String quotationId) async {
    state = const AsyncValue.loading();
    try {
      await _quotationRepo.deleteQuotation(quotationId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Change quotation status
  Future<void> changeQuotationStatus(String quotationId, String status) async {
    state = const AsyncValue.loading();
    try {
      await _quotationRepo.changeQuotationStatus(quotationId, status);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Convert quotation to invoice
  Future<void> convertToInvoice(QuotationModel quotation) async {
    state = const AsyncValue.loading();
    try {
      await _quotationRepo.convertToInvoice(quotation);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
