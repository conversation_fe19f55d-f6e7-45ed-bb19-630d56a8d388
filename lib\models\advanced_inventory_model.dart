// نموذج بيانات المخزون المتقدم
import 'package:mobile_pos/model/product_model.dart';

/// نموذج بيانات المخزون المتقدم
class AdvancedInventoryModel {
  final String productCode;
  final String productName;
  final String category;
  final String brand;
  final double purchasePrice;
  final double salePrice;
  final int openingStock;
  final int closingStock;
  final int purchases;
  final int sales;
  final int adjustments;
  final DateTime periodStart;
  final DateTime periodEnd;
  final List<InventoryMovement> movements;

  AdvancedInventoryModel({
    required this.productCode,
    required this.productName,
    required this.category,
    required this.brand,
    required this.purchasePrice,
    required this.salePrice,
    required this.openingStock,
    required this.closingStock,
    required this.purchases,
    required this.sales,
    required this.adjustments,
    required this.periodStart,
    required this.periodEnd,
    required this.movements,
  });

  /// إنشاء نموذج من منتج
  factory AdvancedInventoryModel.fromProduct(
    ProductModel product,
    int openingStock,
    int closingStock,
    int purchases,
    int sales,
    int adjustments,
    DateTime periodStart,
    DateTime periodEnd,
    List<InventoryMovement> movements,
  ) {
    return AdvancedInventoryModel(
      productCode: product.productCode,
      productName: product.productName,
      category: product.productCategory,
      brand: product.brandName,
      purchasePrice: double.tryParse(product.productPurchasePrice) ?? 0,
      salePrice: double.tryParse(product.productSalePrice) ?? 0,
      openingStock: openingStock,
      closingStock: closingStock,
      purchases: purchases,
      sales: sales,
      adjustments: adjustments,
      periodStart: periodStart,
      periodEnd: periodEnd,
      movements: movements,
    );
  }

  /// حساب قيمة المخزون الافتتاحي
  double get openingStockValue => openingStock * purchasePrice;

  /// حساب قيمة المخزون الختامي
  double get closingStockValue => closingStock * purchasePrice;

  /// حساب قيمة المشتريات
  double get purchasesValue => purchases * purchasePrice;

  /// حساب قيمة المبيعات بسعر البيع
  double get salesValue => sales * salePrice;

  /// حساب قيمة المبيعات بسعر الشراء
  double get salesCostValue => sales * purchasePrice;

  /// حساب الربح الإجمالي
  double get grossProfit => salesValue - salesCostValue;

  /// حساب معدل دوران المخزون
  double get turnoverRate {
    if (openingStock == 0 && closingStock == 0) {
      return 0;
    }

    final averageStock = (openingStock + closingStock) / 2;
    if (averageStock == 0) {
      return 0;
    }

    return sales / averageStock;
  }

  /// حساب أيام المخزون
  double get daysInStock {
    if (turnoverRate == 0) {
      return 0;
    }

    final periodDays = periodEnd.difference(periodStart).inDays;
    return periodDays / turnoverRate;
  }

  /// حساب نسبة الربح
  double get profitMargin {
    if (salesValue == 0) {
      return 0;
    }

    return (grossProfit / salesValue) * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'productCode': productCode,
      'productName': productName,
      'category': category,
      'brand': brand,
      'purchasePrice': purchasePrice,
      'salePrice': salePrice,
      'openingStock': openingStock,
      'closingStock': closingStock,
      'purchases': purchases,
      'sales': sales,
      'adjustments': adjustments,
      'periodStart': periodStart.toString(),
      'periodEnd': periodEnd.toString(),
      'movements': movements.map((movement) => movement.toJson()).toList(),
      'openingStockValue': openingStockValue,
      'closingStockValue': closingStockValue,
      'purchasesValue': purchasesValue,
      'salesValue': salesValue,
      'salesCostValue': salesCostValue,
      'grossProfit': grossProfit,
      'turnoverRate': turnoverRate,
      'daysInStock': daysInStock,
      'profitMargin': profitMargin,
    };
  }
}

/// نموذج حركة المخزون
class InventoryMovement {
  final String id;
  final String type;
  final String reference;
  final int quantity;
  final double price;
  final DateTime date;
  final String notes;

  InventoryMovement({
    required this.id,
    required this.type,
    required this.reference,
    required this.quantity,
    required this.price,
    required this.date,
    required this.notes,
  });

  /// إنشاء حركة مخزون من بيانات
  factory InventoryMovement.fromJson(Map<String, dynamic> json) {
    return InventoryMovement(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      reference: json['reference'] ?? '',
      quantity: json['quantity'] ?? 0,
      price: json['price'] ?? 0.0,
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      notes: json['notes'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'reference': reference,
      'quantity': quantity,
      'price': price,
      'date': date.toString(),
      'notes': notes,
    };
  }

  /// حساب قيمة الحركة
  double get value => quantity * price;
}
