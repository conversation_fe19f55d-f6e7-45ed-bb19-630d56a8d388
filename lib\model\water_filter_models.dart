import 'package:flutter/material.dart';

/// أنواع الأنشطة التجارية
enum BusinessType {
  pos('pos', 'نقاط البيع', 'POS', Icons.point_of_sale),
  waterFilters(
      'water_filters', 'فلاتر المياه', 'Water Filters', Icons.water_drop),
  restaurant('restaurant', 'مطاعم', 'Restaurant', Icons.restaurant),
  pharmacy('pharmacy', 'صيدليات', 'Pharmacy', Icons.local_pharmacy);

  const BusinessType(this.id, this.arabicName, this.englishName, this.icon);

  final String id;
  final String arabicName;
  final String englishName;
  final IconData icon;
}

/// فئات فلاتر المياه
enum WaterFilterCategory {
  residential('residential', 'فلاتر منزلية', '3-7 مراحل'),
  commercial('commercial', 'فلاتر تجارية', 'أنظمة RO'),
  industrial('industrial', 'فلاتر صناعية', 'أنظمة كبيرة'),
  central('central', 'فلاتر مركزية', 'أنظمة المباني'),
  spareParts('spare_parts', 'قطع غيار', 'شمع وقطع');

  const WaterFilterCategory(this.id, this.arabicName, this.description);

  final String id;
  final String arabicName;
  final String description;
}

/// حالة نظام الفلتر
enum FilterSystemStatus {
  active('active', 'نشط', Colors.green),
  needsMaintenance('needs_maintenance', 'يحتاج صيانة', Colors.orange),
  overdue('overdue', 'متأخر عن الصيانة', Colors.red),
  inactive('inactive', 'غير نشط', Colors.grey);

  const FilterSystemStatus(this.id, this.arabicName, this.color);

  final String id;
  final String arabicName;
  final Color color;
}

/// نموذج منتج فلتر المياه
class WaterFilterProduct {
  late String id;
  late String name;
  late String brand;
  late WaterFilterCategory category;
  late double price;
  late int stock;
  late String description;
  late List<String> specifications;
  late int maintenanceIntervalMonths; // فترة الصيانة بالشهور
  late double maintenanceCost; // تكلفة الصيانة
  late bool isInstallationRequired; // يحتاج تركيب
  late double installationCost; // تكلفة التركيب
  String? imageUrl;
  DateTime? createdAt;
  DateTime? updatedAt;

  WaterFilterProduct({
    required this.id,
    required this.name,
    required this.brand,
    required this.category,
    required this.price,
    required this.stock,
    required this.description,
    required this.specifications,
    required this.maintenanceIntervalMonths,
    required this.maintenanceCost,
    required this.isInstallationRequired,
    required this.installationCost,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
  });

  WaterFilterProduct.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        name = json['name'] ?? '',
        brand = json['brand'] ?? '',
        category = WaterFilterCategory.values.firstWhere(
          (cat) => cat.id == json['category'],
          orElse: () => WaterFilterCategory.residential,
        ),
        price = (json['price'] ?? 0).toDouble(),
        stock = json['stock'] ?? 0,
        description = json['description'] ?? '',
        specifications = List<String>.from(json['specifications'] ?? []),
        maintenanceIntervalMonths = json['maintenanceIntervalMonths'] ?? 6,
        maintenanceCost = (json['maintenanceCost'] ?? 0).toDouble(),
        isInstallationRequired = json['isInstallationRequired'] ?? false,
        installationCost = (json['installationCost'] ?? 0).toDouble(),
        imageUrl = json['imageUrl'],
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'brand': brand,
        'category': category.id,
        'price': price,
        'stock': stock,
        'description': description,
        'specifications': specifications,
        'maintenanceIntervalMonths': maintenanceIntervalMonths,
        'maintenanceCost': maintenanceCost,
        'isInstallationRequired': isInstallationRequired,
        'installationCost': installationCost,
        'imageUrl': imageUrl,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };
}

/// نموذج عميل فلاتر المياه
class WaterFilterCustomer {
  late String id;
  late String name;
  late String phone;
  late String address;
  late String email;
  late String area; // المنطقة
  late String city; // المدينة
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;

  WaterFilterCustomer({
    required this.id,
    required this.name,
    required this.phone,
    required this.address,
    required this.email,
    required this.area,
    required this.city,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  WaterFilterCustomer.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        name = json['name'] ?? '',
        phone = json['phone'] ?? '',
        address = json['address'] ?? '',
        email = json['email'] ?? '',
        area = json['area'] ?? '',
        city = json['city'] ?? '',
        notes = json['notes'],
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'phone': phone,
        'address': address,
        'email': email,
        'area': area,
        'city': city,
        'notes': notes,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };
}

/// نموذج نظام فلتر مركب
class WaterFilterSystem {
  late String id;
  late String customerId;
  late String productId;
  late String serialNumber;
  late DateTime installationDate;
  late FilterSystemStatus status;
  late DateTime nextMaintenanceDate;
  late double totalCost;
  late double paidAmount;
  late double remainingAmount;
  late bool isUnderWarranty;
  late DateTime warrantyEndDate;
  String? installationNotes;
  String? technicianId;
  DateTime? lastMaintenanceDate;
  DateTime? createdAt;
  DateTime? updatedAt;

  WaterFilterSystem({
    required this.id,
    required this.customerId,
    required this.productId,
    required this.serialNumber,
    required this.installationDate,
    required this.status,
    required this.nextMaintenanceDate,
    required this.totalCost,
    required this.paidAmount,
    required this.remainingAmount,
    required this.isUnderWarranty,
    required this.warrantyEndDate,
    this.installationNotes,
    this.technicianId,
    this.lastMaintenanceDate,
    this.createdAt,
    this.updatedAt,
  });

  WaterFilterSystem.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        customerId = json['customerId'] ?? '',
        productId = json['productId'] ?? '',
        serialNumber = json['serialNumber'] ?? '',
        installationDate = DateTime.parse(json['installationDate']),
        status = FilterSystemStatus.values.firstWhere(
          (status) => status.id == json['status'],
          orElse: () => FilterSystemStatus.active,
        ),
        nextMaintenanceDate = DateTime.parse(json['nextMaintenanceDate']),
        totalCost = (json['totalCost'] ?? 0).toDouble(),
        paidAmount = (json['paidAmount'] ?? 0).toDouble(),
        remainingAmount = (json['remainingAmount'] ?? 0).toDouble(),
        isUnderWarranty = json['isUnderWarranty'] ?? false,
        warrantyEndDate = DateTime.parse(json['warrantyEndDate']),
        installationNotes = json['installationNotes'],
        technicianId = json['technicianId'],
        lastMaintenanceDate = json['lastMaintenanceDate'] != null
            ? DateTime.parse(json['lastMaintenanceDate'])
            : null,
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'customerId': customerId,
        'productId': productId,
        'serialNumber': serialNumber,
        'installationDate': installationDate.toIso8601String(),
        'status': status.id,
        'nextMaintenanceDate': nextMaintenanceDate.toIso8601String(),
        'totalCost': totalCost,
        'paidAmount': paidAmount,
        'remainingAmount': remainingAmount,
        'isUnderWarranty': isUnderWarranty,
        'warrantyEndDate': warrantyEndDate.toIso8601String(),
        'installationNotes': installationNotes,
        'technicianId': technicianId,
        'lastMaintenanceDate': lastMaintenanceDate?.toIso8601String(),
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  /// حساب النسبة المدفوعة
  double get paymentPercentage =>
      totalCost > 0 ? (paidAmount / totalCost) * 100 : 0;

  /// التحقق من تأخر الصيانة
  bool get isMaintenanceOverdue => DateTime.now().isAfter(nextMaintenanceDate);

  /// عدد الأيام المتبقية للصيانة
  int get daysUntilMaintenance =>
      nextMaintenanceDate.difference(DateTime.now()).inDays;
}

/// حالة القسط
enum InstallmentStatus {
  pending('pending', 'معلق', Colors.orange),
  paid('paid', 'مدفوع', Colors.green),
  overdue('overdue', 'متأخر', Colors.red),
  cancelled('cancelled', 'ملغي', Colors.grey);

  const InstallmentStatus(this.id, this.arabicName, this.color);

  final String id;
  final String arabicName;
  final Color color;
}

/// نموذج قسط
class WaterFilterInstallment {
  late String id;
  late String systemId;
  late int installmentNumber;
  late double amount;
  late DateTime dueDate;
  late InstallmentStatus status;
  DateTime? paidDate;
  double? paidAmount;
  String? paymentMethod;
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;

  WaterFilterInstallment({
    required this.id,
    required this.systemId,
    required this.installmentNumber,
    required this.amount,
    required this.dueDate,
    required this.status,
    this.paidDate,
    this.paidAmount,
    this.paymentMethod,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  WaterFilterInstallment.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        systemId = json['systemId'] ?? '',
        installmentNumber = json['installmentNumber'] ?? 1,
        amount = (json['amount'] ?? 0).toDouble(),
        dueDate = DateTime.parse(json['dueDate']),
        status = InstallmentStatus.values.firstWhere(
          (status) => status.id == json['status'],
          orElse: () => InstallmentStatus.pending,
        ),
        paidDate =
            json['paidDate'] != null ? DateTime.parse(json['paidDate']) : null,
        paidAmount = json['paidAmount']?.toDouble(),
        paymentMethod = json['paymentMethod'],
        notes = json['notes'],
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'systemId': systemId,
        'installmentNumber': installmentNumber,
        'amount': amount,
        'dueDate': dueDate.toIso8601String(),
        'status': status.id,
        'paidDate': paidDate?.toIso8601String(),
        'paidAmount': paidAmount,
        'paymentMethod': paymentMethod,
        'notes': notes,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  /// التحقق من تأخر القسط
  bool get isOverdue =>
      DateTime.now().isAfter(dueDate) && status != InstallmentStatus.paid;

  /// عدد الأيام المتأخرة
  int get daysOverdue =>
      isOverdue ? DateTime.now().difference(dueDate).inDays : 0;
}

/// حالة الصيانة
enum MaintenanceStatus {
  scheduled('scheduled', 'مجدولة', Colors.blue),
  inProgress('in_progress', 'جاري التنفيذ', Colors.orange),
  completed('completed', 'مكتملة', Colors.green),
  cancelled('cancelled', 'ملغية', Colors.red);

  const MaintenanceStatus(this.id, this.arabicName, this.color);

  final String id;
  final String arabicName;
  final Color color;
}

/// نموذج الصيانة
class WaterFilterMaintenance {
  late String id;
  late String systemId;
  late String customerId;
  late DateTime scheduledDate;
  late MaintenanceStatus status;
  late double cost;
  late String description;
  String? technicianId;
  DateTime? completedDate;
  String? workDone;
  String? partsReplaced;
  String? notes;
  List<String>? beforeImages;
  List<String>? afterImages;
  int? customerRating; // تقييم العميل من 1-5
  String? customerFeedback;
  DateTime? createdAt;
  DateTime? updatedAt;

  WaterFilterMaintenance({
    required this.id,
    required this.systemId,
    required this.customerId,
    required this.scheduledDate,
    required this.status,
    required this.cost,
    required this.description,
    this.technicianId,
    this.completedDate,
    this.workDone,
    this.partsReplaced,
    this.notes,
    this.beforeImages,
    this.afterImages,
    this.customerRating,
    this.customerFeedback,
    this.createdAt,
    this.updatedAt,
  });

  WaterFilterMaintenance.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        systemId = json['systemId'] ?? '',
        customerId = json['customerId'] ?? '',
        scheduledDate = DateTime.parse(json['scheduledDate']),
        status = MaintenanceStatus.values.firstWhere(
          (status) => status.id == json['status'],
          orElse: () => MaintenanceStatus.scheduled,
        ),
        cost = (json['cost'] ?? 0).toDouble(),
        description = json['description'] ?? '',
        technicianId = json['technicianId'],
        completedDate = json['completedDate'] != null
            ? DateTime.parse(json['completedDate'])
            : null,
        workDone = json['workDone'],
        partsReplaced = json['partsReplaced'],
        notes = json['notes'],
        beforeImages = json['beforeImages'] != null
            ? List<String>.from(json['beforeImages'])
            : null,
        afterImages = json['afterImages'] != null
            ? List<String>.from(json['afterImages'])
            : null,
        customerRating = json['customerRating'],
        customerFeedback = json['customerFeedback'],
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'systemId': systemId,
        'customerId': customerId,
        'scheduledDate': scheduledDate.toIso8601String(),
        'status': status.id,
        'cost': cost,
        'description': description,
        'technicianId': technicianId,
        'completedDate': completedDate?.toIso8601String(),
        'workDone': workDone,
        'partsReplaced': partsReplaced,
        'notes': notes,
        'beforeImages': beforeImages,
        'afterImages': afterImages,
        'customerRating': customerRating,
        'customerFeedback': customerFeedback,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  /// التحقق من تأخر الصيانة
  bool get isOverdue =>
      DateTime.now().isAfter(scheduledDate) &&
      status != MaintenanceStatus.completed &&
      status != MaintenanceStatus.cancelled;
}

// Additional Maintenance Enums
enum MaintenanceType {
  routine('routine', 'صيانة دورية', Colors.blue, Icons.schedule),
  emergency('emergency', 'صيانة طارئة', Colors.red, Icons.warning),
  preventive('preventive', 'صيانة وقائية', Colors.green, Icons.shield),
  corrective('corrective', 'صيانة إصلاحية', Colors.orange, Icons.build);

  const MaintenanceType(this.id, this.arabicName, this.color, this.icon);

  final String id;
  final String arabicName;
  final Color color;
  final IconData icon;
}

enum MaintenancePriority {
  low('low', 'منخفضة', Colors.grey),
  normal('normal', 'عادية', Colors.blue),
  high('high', 'عالية', Colors.orange),
  urgent('urgent', 'عاجلة', Colors.red);

  const MaintenancePriority(this.id, this.arabicName, this.color);

  final String id;
  final String arabicName;
  final Color color;
}

enum ScheduleStatus {
  scheduled('scheduled', 'مجدولة', Colors.blue),
  confirmed('confirmed', 'مؤكدة', Colors.green),
  inProgress('in_progress', 'جارية', Colors.orange),
  completed('completed', 'مكتملة', Colors.green),
  cancelled('cancelled', 'ملغية', Colors.red),
  postponed('postponed', 'مؤجلة', Colors.grey);

  const ScheduleStatus(this.id, this.arabicName, this.color);

  final String id;
  final String arabicName;
  final Color color;
}

/// نموذج جدولة الصيانة المتقدم
class MaintenanceSchedule {
  late String id;
  late String systemId;
  late String customerId;
  String? technicianId;
  late DateTime scheduledDate;
  late TimeOfDay scheduledTime;
  late MaintenanceType type;
  late MaintenancePriority priority;
  late ScheduleStatus status;
  late String description;
  late Duration estimatedDuration;
  double? estimatedCost;
  String? notes;
  String? customerNotes;
  bool isRecurring;
  int? recurringIntervalDays;
  DateTime? reminderSent;
  DateTime? createdAt;
  DateTime? updatedAt;

  MaintenanceSchedule({
    required this.id,
    required this.systemId,
    required this.customerId,
    this.technicianId,
    required this.scheduledDate,
    required this.scheduledTime,
    required this.type,
    required this.priority,
    required this.status,
    required this.description,
    required this.estimatedDuration,
    this.estimatedCost,
    this.notes,
    this.customerNotes,
    this.isRecurring = false,
    this.recurringIntervalDays,
    this.reminderSent,
    this.createdAt,
    this.updatedAt,
  });

  MaintenanceSchedule.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        systemId = json['systemId'] ?? '',
        customerId = json['customerId'] ?? '',
        technicianId = json['technicianId'],
        scheduledDate = DateTime.parse(json['scheduledDate']),
        scheduledTime = TimeOfDay(
          hour: json['scheduledHour'] ?? 9,
          minute: json['scheduledMinute'] ?? 0,
        ),
        type = MaintenanceType.values.firstWhere(
          (type) => type.id == json['type'],
          orElse: () => MaintenanceType.routine,
        ),
        priority = MaintenancePriority.values.firstWhere(
          (priority) => priority.id == json['priority'],
          orElse: () => MaintenancePriority.normal,
        ),
        status = ScheduleStatus.values.firstWhere(
          (status) => status.id == json['status'],
          orElse: () => ScheduleStatus.scheduled,
        ),
        description = json['description'] ?? '',
        estimatedDuration =
            Duration(minutes: json['estimatedDurationMinutes'] ?? 60),
        estimatedCost = json['estimatedCost']?.toDouble(),
        notes = json['notes'],
        customerNotes = json['customerNotes'],
        isRecurring = json['isRecurring'] ?? false,
        recurringIntervalDays = json['recurringIntervalDays'],
        reminderSent = json['reminderSent'] != null
            ? DateTime.parse(json['reminderSent'])
            : null,
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'systemId': systemId,
        'customerId': customerId,
        'technicianId': technicianId,
        'scheduledDate': scheduledDate.toIso8601String(),
        'scheduledHour': scheduledTime.hour,
        'scheduledMinute': scheduledTime.minute,
        'type': type.id,
        'priority': priority.id,
        'status': status.id,
        'description': description,
        'estimatedDurationMinutes': estimatedDuration.inMinutes,
        'estimatedCost': estimatedCost,
        'notes': notes,
        'customerNotes': customerNotes,
        'isRecurring': isRecurring,
        'recurringIntervalDays': recurringIntervalDays,
        'reminderSent': reminderSent?.toIso8601String(),
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  /// التحقق من تأخر الموعد
  bool get isOverdue {
    final now = DateTime.now();
    final scheduledDateTime = DateTime(
      scheduledDate.year,
      scheduledDate.month,
      scheduledDate.day,
      scheduledTime.hour,
      scheduledTime.minute,
    );
    return now.isAfter(scheduledDateTime) &&
        status != ScheduleStatus.completed &&
        status != ScheduleStatus.cancelled;
  }

  /// عدد الساعات المتبقية للموعد
  int get hoursUntilScheduled {
    final now = DateTime.now();
    final scheduledDateTime = DateTime(
      scheduledDate.year,
      scheduledDate.month,
      scheduledDate.day,
      scheduledTime.hour,
      scheduledTime.minute,
    );
    return scheduledDateTime.difference(now).inHours;
  }

  /// تاريخ ووقت الموعد مجمعين
  DateTime get scheduledDateTime => DateTime(
        scheduledDate.year,
        scheduledDate.month,
        scheduledDate.day,
        scheduledTime.hour,
        scheduledTime.minute,
      );
}

/// نموذج الفني
class Technician {
  late String id;
  late String name;
  late String phone;
  late String email;
  late List<String> specializations;
  late double rating;
  late bool isAvailable;
  late String location;
  late String address;
  String? profileImageUrl;
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;

  Technician({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.specializations,
    required this.rating,
    required this.isAvailable,
    required this.location,
    required this.address,
    this.profileImageUrl,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  Technician.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        name = json['name'] ?? '',
        phone = json['phone'] ?? '',
        email = json['email'] ?? '',
        specializations = List<String>.from(json['specializations'] ?? []),
        rating = (json['rating'] ?? 0.0).toDouble(),
        isAvailable = json['isAvailable'] ?? true,
        location = json['location'] ?? '',
        address = json['address'] ?? '',
        profileImageUrl = json['profileImageUrl'],
        notes = json['notes'],
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'phone': phone,
        'email': email,
        'specializations': specializations,
        'rating': rating,
        'isAvailable': isAvailable,
        'location': location,
        'address': address,
        'profileImageUrl': profileImageUrl,
        'notes': notes,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };
}

/// نموذج سجل الصيانة المتقدم
class MaintenanceRecord {
  late String id;
  late String scheduleId;
  late String systemId;
  late String technicianId;
  late DateTime startTime;
  DateTime? endTime;
  late List<String> workPerformed;
  late List<PartUsed> partsUsed;
  late List<String> issues;
  String? recommendations;
  DateTime? nextMaintenanceDate;
  late double cost;
  String? customerSignature;
  late String technicianNotes;
  late List<String> photos;
  late MaintenanceStatus status;
  int? customerRating;
  String? customerFeedback;
  DateTime? createdAt;
  DateTime? updatedAt;

  MaintenanceRecord({
    required this.id,
    required this.scheduleId,
    required this.systemId,
    required this.technicianId,
    required this.startTime,
    this.endTime,
    required this.workPerformed,
    required this.partsUsed,
    required this.issues,
    this.recommendations,
    this.nextMaintenanceDate,
    required this.cost,
    this.customerSignature,
    required this.technicianNotes,
    required this.photos,
    required this.status,
    this.customerRating,
    this.customerFeedback,
    this.createdAt,
    this.updatedAt,
  });

  MaintenanceRecord.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        scheduleId = json['scheduleId'] ?? '',
        systemId = json['systemId'] ?? '',
        technicianId = json['technicianId'] ?? '',
        startTime = DateTime.parse(json['startTime']),
        endTime =
            json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
        workPerformed = List<String>.from(json['workPerformed'] ?? []),
        partsUsed = (json['partsUsed'] as List? ?? [])
            .map((part) => PartUsed.fromJson(part))
            .toList(),
        issues = List<String>.from(json['issues'] ?? []),
        recommendations = json['recommendations'],
        nextMaintenanceDate = json['nextMaintenanceDate'] != null
            ? DateTime.parse(json['nextMaintenanceDate'])
            : null,
        cost = (json['cost'] ?? 0).toDouble(),
        customerSignature = json['customerSignature'],
        technicianNotes = json['technicianNotes'] ?? '',
        photos = List<String>.from(json['photos'] ?? []),
        status = MaintenanceStatus.values.firstWhere(
          (status) => status.id == json['status'],
          orElse: () => MaintenanceStatus.completed,
        ),
        customerRating = json['customerRating'],
        customerFeedback = json['customerFeedback'],
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'scheduleId': scheduleId,
        'systemId': systemId,
        'technicianId': technicianId,
        'startTime': startTime.toIso8601String(),
        'endTime': endTime?.toIso8601String(),
        'workPerformed': workPerformed,
        'partsUsed': partsUsed.map((part) => part.toJson()).toList(),
        'issues': issues,
        'recommendations': recommendations,
        'nextMaintenanceDate': nextMaintenanceDate?.toIso8601String(),
        'cost': cost,
        'customerSignature': customerSignature,
        'technicianNotes': technicianNotes,
        'photos': photos,
        'status': status.id,
        'customerRating': customerRating,
        'customerFeedback': customerFeedback,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };

  /// مدة العمل
  Duration? get workDuration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return null;
  }

  /// إجمالي تكلفة القطع
  double get partsCost {
    return partsUsed.fold(0.0, (sum, part) => sum + part.totalCost);
  }
}

/// نموذج القطعة المستخدمة
class PartUsed {
  late String partName;
  late int quantity;
  late double unitPrice;
  String? notes;

  PartUsed({
    required this.partName,
    required this.quantity,
    required this.unitPrice,
    this.notes,
  });

  PartUsed.fromJson(Map<dynamic, dynamic> json)
      : partName = json['partName'] ?? '',
        quantity = json['quantity'] ?? 1,
        unitPrice = (json['unitPrice'] ?? 0).toDouble(),
        notes = json['notes'];

  Map<String, dynamic> toJson() => {
        'partName': partName,
        'quantity': quantity,
        'unitPrice': unitPrice,
        'notes': notes,
      };

  /// إجمالي التكلفة
  double get totalCost => quantity * unitPrice;
}

/// فئات المصروفات
enum ExpenseCategory {
  transportation('transportation', 'مواصلات'),
  materials('materials', 'مواد خام'),
  tools('tools', 'أدوات وعدد'),
  maintenance('maintenance', 'صيانة'),
  marketing('marketing', 'تسويق'),
  office('office', 'مكتبية'),
  utilities('utilities', 'خدمات'),
  salaries('salaries', 'رواتب'),
  rent('rent', 'إيجار'),
  insurance('insurance', 'تأمين'),
  other('other', 'أخرى');

  const ExpenseCategory(this.id, this.arabicName);
  final String id;
  final String arabicName;

  static ExpenseCategory fromId(String id) {
    return ExpenseCategory.values.firstWhere(
      (category) => category.id == id,
      orElse: () => ExpenseCategory.other,
    );
  }
}

/// طرق الدفع للمصروفات
enum ExpensePaymentMethod {
  cash('cash', 'نقدي'),
  bankTransfer('bank_transfer', 'تحويل بنكي'),
  creditCard('credit_card', 'بطاقة ائتمان'),
  check('check', 'شيك'),
  other('other', 'أخرى');

  const ExpensePaymentMethod(this.id, this.arabicName);
  final String id;
  final String arabicName;

  static ExpensePaymentMethod fromId(String id) {
    return ExpensePaymentMethod.values.firstWhere(
      (method) => method.id == id,
      orElse: () => ExpensePaymentMethod.cash,
    );
  }
}

/// نموذج مصروف فلتر المياه
class WaterFilterExpense {
  late String id;
  late String title;
  late String description;
  late ExpenseCategory category;
  late double amount;
  late DateTime date;
  late ExpensePaymentMethod paymentMethod;
  String? vendorName;
  String? invoiceNumber;
  String? receiptImageUrl;
  String? notes;
  String? systemId; // ربط بنظام معين (اختياري)
  String? customerId; // ربط بعميل معين (اختياري)
  String? technicianId; // ربط بفني معين (اختياري)
  bool isRecurring;
  int? recurringIntervalDays;
  DateTime? nextRecurringDate;
  DateTime? createdAt;
  DateTime? updatedAt;

  WaterFilterExpense({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.amount,
    required this.date,
    required this.paymentMethod,
    this.vendorName,
    this.invoiceNumber,
    this.receiptImageUrl,
    this.notes,
    this.systemId,
    this.customerId,
    this.technicianId,
    this.isRecurring = false,
    this.recurringIntervalDays,
    this.nextRecurringDate,
    this.createdAt,
    this.updatedAt,
  });

  WaterFilterExpense.fromJson(Map<dynamic, dynamic> json)
      : id = json['id'] ?? '',
        title = json['title'] ?? '',
        description = json['description'] ?? '',
        category = ExpenseCategory.fromId(json['category'] ?? 'other'),
        amount = (json['amount'] ?? 0).toDouble(),
        date = DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
        paymentMethod =
            ExpensePaymentMethod.fromId(json['paymentMethod'] ?? 'cash'),
        vendorName = json['vendorName'],
        invoiceNumber = json['invoiceNumber'],
        receiptImageUrl = json['receiptImageUrl'],
        notes = json['notes'],
        systemId = json['systemId'],
        customerId = json['customerId'],
        technicianId = json['technicianId'],
        isRecurring = json['isRecurring'] ?? false,
        recurringIntervalDays = json['recurringIntervalDays'],
        nextRecurringDate = json['nextRecurringDate'] != null
            ? DateTime.parse(json['nextRecurringDate'])
            : null,
        createdAt = json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt = json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'description': description,
        'category': category.id,
        'amount': amount,
        'date': date.toIso8601String(),
        'paymentMethod': paymentMethod.id,
        'vendorName': vendorName,
        'invoiceNumber': invoiceNumber,
        'receiptImageUrl': receiptImageUrl,
        'notes': notes,
        'systemId': systemId,
        'customerId': customerId,
        'technicianId': technicianId,
        'isRecurring': isRecurring,
        'recurringIntervalDays': recurringIntervalDays,
        'nextRecurringDate': nextRecurringDate?.toIso8601String(),
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };
}
