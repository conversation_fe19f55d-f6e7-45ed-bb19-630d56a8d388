// بسم الله الرحمن الرحيم
// خدمة قاعدة البيانات للدردشة - AmrDevPOS
//
// ⚠️ تحذير: هذه الخدمة تستخدم البيانات الحقيقية من Firebase فقط
// ❌ لا تستخدم البيانات من مجلد lib/features/ (بيانات وهمية)
// ✅ تستخدم البيانات من Firebase Realtime Database

import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';

class DatabaseService {
  // الحصول على بيانات المبيعات الحقيقية
  static Future<Map<String, dynamic>> getSalesData() async {
    try {
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final snapshot = await salesRef.get();

      if (!snapshot.exists) {
        return {
          'total_sales': 0.0,
          'today_sales': 0.0,
          'weekly_sales': 0.0,
          'monthly_sales': 0.0,
          'sales_count': 0,
          'average_sale': 0.0,
          'top_selling_products': [],
          'sales_trend': 'لا توجد بيانات',
          'growth_rate': 0.0,
        };
      }

      List<dynamic> allSales = [];
      for (var element in snapshot.children) {
        try {
          var data = jsonDecode(jsonEncode(element.value));
          allSales.add(data);
        } catch (e) {
          continue;
        }
      }

      // حساب البيانات
      double totalSales = 0;
      double todaySales = 0;
      double weeklySales = 0;
      double monthlySales = 0;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekAgo = today.subtract(const Duration(days: 7));
      final monthAgo = DateTime(now.year, now.month - 1, now.day);

      Map<String, Map<String, dynamic>> productSales = {};

      for (var sale in allSales) {
        try {
          final saleDate =
              DateTime.parse(sale['purchaseDate'] ?? DateTime.now().toString());
          final amount =
              double.tryParse(sale['totalAmount']?.toString() ?? '0') ?? 0.0;

          totalSales += amount;

          if (saleDate.isAfter(today) || saleDate.isAtSameMomentAs(today)) {
            todaySales += amount;
          }

          if (saleDate.isAfter(weekAgo)) {
            weeklySales += amount;
          }

          if (saleDate.isAfter(monthAgo)) {
            monthlySales += amount;
          }

          // تجميع بيانات المنتجات
          final productList = sale['productList'] as List<dynamic>? ?? [];
          for (var product in productList) {
            final productName =
                product['productName']?.toString() ?? 'منتج غير محدد';
            final quantity =
                int.tryParse(product['quantity']?.toString() ?? '0') ?? 0;
            final productTotal =
                double.tryParse(product['subTotal']?.toString() ?? '0') ?? 0.0;

            if (productSales.containsKey(productName)) {
              productSales[productName]!['sales'] += productTotal;
              productSales[productName]!['quantity'] += quantity;
            } else {
              productSales[productName] = {
                'name': productName,
                'sales': productTotal,
                'quantity': quantity,
              };
            }
          }
        } catch (e) {
          continue;
        }
      }

      // ترتيب المنتجات حسب المبيعات
      var topProducts = productSales.values.toList();
      topProducts.sort((a, b) => b['sales'].compareTo(a['sales']));
      topProducts = topProducts.take(5).toList();

      // حساب الاتجاه ومعدل النمو
      String trend = 'مستقر';
      double growthRate = 0.0;

      if (allSales.length > 1) {
        // حساب متوسط المبيعات للنصف الأول والثاني
        final halfPoint = allSales.length ~/ 2;
        final firstHalf = allSales.take(halfPoint);
        final secondHalf = allSales.skip(halfPoint);

        double firstHalfTotal = firstHalf.fold(
            0.0,
            (sum, sale) =>
                sum +
                (double.tryParse(sale['totalAmount']?.toString() ?? '0') ??
                    0.0));
        double secondHalfTotal = secondHalf.fold(
            0.0,
            (sum, sale) =>
                sum +
                (double.tryParse(sale['totalAmount']?.toString() ?? '0') ??
                    0.0));

        if (secondHalfTotal > firstHalfTotal) {
          trend = 'متزايد';
          growthRate =
              ((secondHalfTotal - firstHalfTotal) / firstHalfTotal * 100);
        } else if (secondHalfTotal < firstHalfTotal) {
          trend = 'متناقص';
          growthRate =
              ((firstHalfTotal - secondHalfTotal) / firstHalfTotal * -100);
        }
      }

      return {
        'total_sales': totalSales,
        'today_sales': todaySales,
        'weekly_sales': weeklySales,
        'monthly_sales': monthlySales,
        'sales_count': allSales.length,
        'average_sale':
            allSales.isNotEmpty ? totalSales / allSales.length : 0.0,
        'top_selling_products': topProducts,
        'sales_trend': trend,
        'growth_rate': growthRate,
      };
    } catch (e) {
      return {
        'error': 'خطأ في جلب بيانات المبيعات: $e',
        'total_sales': 0.0,
        'today_sales': 0.0,
        'weekly_sales': 0.0,
        'monthly_sales': 0.0,
        'sales_count': 0,
        'average_sale': 0.0,
        'top_selling_products': [],
        'sales_trend': 'خطأ',
        'growth_rate': 0.0,
      };
    }
  }

  // الحصول على بيانات المشتريات الحقيقية
  static Future<Map<String, dynamic>> getPurchasesData() async {
    try {
      final purchaseRef = FirebaseDatabase.instance
          .ref(constUserId)
          .child('Purchase Transition');
      final snapshot = await purchaseRef.get();

      if (!snapshot.exists) {
        return {
          'total_purchases': 0.0,
          'today_purchases': 0.0,
          'weekly_purchases': 0.0,
          'monthly_purchases': 0.0,
          'purchases_count': 0,
          'average_purchase': 0.0,
          'top_suppliers': [],
          'pending_orders': 0,
          'overdue_payments': 0,
        };
      }

      List<dynamic> allPurchases = [];
      for (var element in snapshot.children) {
        try {
          var data = jsonDecode(jsonEncode(element.value));
          allPurchases.add(data);
        } catch (e) {
          continue;
        }
      }

      // حساب البيانات
      double totalPurchases = 0;
      double todayPurchases = 0;
      double weeklyPurchases = 0;
      double monthlyPurchases = 0;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekAgo = today.subtract(const Duration(days: 7));
      final monthAgo = DateTime(now.year, now.month - 1, now.day);

      Map<String, Map<String, dynamic>> supplierData = {};
      int pendingOrders = 0;
      int overduePayments = 0;

      for (var purchase in allPurchases) {
        try {
          final purchaseDate = DateTime.parse(
              purchase['purchaseDate'] ?? DateTime.now().toString());
          final amount =
              double.tryParse(purchase['totalAmount']?.toString() ?? '0') ??
                  0.0;

          totalPurchases += amount;

          if (purchaseDate.isAfter(today) ||
              purchaseDate.isAtSameMomentAs(today)) {
            todayPurchases += amount;
          }

          if (purchaseDate.isAfter(weekAgo)) {
            weeklyPurchases += amount;
          }

          if (purchaseDate.isAfter(monthAgo)) {
            monthlyPurchases += amount;
          }

          // تجميع بيانات الموردين
          final supplierName =
              purchase['customerName']?.toString() ?? 'مورد غير محدد';
          if (supplierData.containsKey(supplierName)) {
            supplierData[supplierName]!['purchases'] += amount;
            supplierData[supplierName]!['orders'] += 1;
          } else {
            supplierData[supplierName] = {
              'name': supplierName,
              'purchases': amount,
              'orders': 1,
            };
          }

          // حساب الطلبات المعلقة والمدفوعات المتأخرة
          final isPaid = purchase['isPaid'] == true;
          final dueAmount =
              double.tryParse(purchase['dueAmount']?.toString() ?? '0') ?? 0.0;

          if (!isPaid) {
            if (dueAmount > 0) {
              overduePayments++;
            } else {
              pendingOrders++;
            }
          }
        } catch (e) {
          continue;
        }
      }

      // ترتيب الموردين حسب المشتريات
      var topSuppliers = supplierData.values.toList();
      topSuppliers.sort((a, b) => b['purchases'].compareTo(a['purchases']));
      topSuppliers = topSuppliers.take(5).toList();

      return {
        'total_purchases': totalPurchases,
        'today_purchases': todayPurchases,
        'weekly_purchases': weeklyPurchases,
        'monthly_purchases': monthlyPurchases,
        'purchases_count': allPurchases.length,
        'average_purchase': allPurchases.isNotEmpty
            ? totalPurchases / allPurchases.length
            : 0.0,
        'top_suppliers': topSuppliers,
        'pending_orders': pendingOrders,
        'overdue_payments': overduePayments,
      };
    } catch (e) {
      return {
        'error': 'خطأ في جلب بيانات المشتريات: $e',
        'total_purchases': 0.0,
        'today_purchases': 0.0,
        'weekly_purchases': 0.0,
        'monthly_purchases': 0.0,
        'purchases_count': 0,
        'average_purchase': 0.0,
        'top_suppliers': [],
        'pending_orders': 0,
        'overdue_payments': 0,
      };
    }
  }

  // الحصول على بيانات المخزون الحقيقية
  static Future<Map<String, dynamic>> getInventoryData() async {
    try {
      final productsRef =
          FirebaseDatabase.instance.ref(constUserId).child('Products');
      final snapshot = await productsRef.get();

      if (!snapshot.exists) {
        return {
          'total_products': 0,
          'total_value': 0.0,
          'low_stock_items': 0,
          'out_of_stock_items': 0,
          'categories': [],
          'top_products': [],
          'alerts': ['لا توجد منتجات في المخزون'],
        };
      }

      List<Map<String, dynamic>> allProducts = [];
      for (var element in snapshot.children) {
        try {
          var productData = jsonDecode(jsonEncode(element.value));
          allProducts.add(productData);
        } catch (e) {
          continue;
        }
      }

      // حساب البيانات الحقيقية
      int totalProducts = allProducts.length;
      double totalValue = 0.0;
      int lowStockItems = 0;
      int outOfStockItems = 0;
      Map<String, Map<String, dynamic>> categoryData = {};
      List<String> alerts = [];

      for (var product in allProducts) {
        try {
          // حساب القيمة الإجمالية
          final stock =
              int.tryParse(product['productStock']?.toString() ?? '0') ?? 0;
          final price =
              double.tryParse(product['productSalePrice']?.toString() ?? '0') ??
                  0.0;
          totalValue += (stock * price);

          // حساب المنتجات منخفضة المخزون
          final lowStockAlert = product['lowerStockAlert'] ?? 5;
          if (stock <= lowStockAlert && stock > 0) {
            lowStockItems++;
            alerts.add(
                'منتج "${product['productName']}" مخزون منخفض ($stock قطعة)');
          } else if (stock == 0) {
            outOfStockItems++;
            alerts.add('منتج "${product['productName']}" نفد من المخزون');
          }

          // تجميع بيانات الفئات
          final category = product['productCategory']?.toString() ?? 'غير محدد';
          if (categoryData.containsKey(category)) {
            categoryData[category]!['count'] += 1;
            categoryData[category]!['value'] += (stock * price);
          } else {
            categoryData[category] = {
              'name': category,
              'count': 1,
              'value': (stock * price),
            };
          }
        } catch (e) {
          continue;
        }
      }

      // ترتيب المنتجات حسب القيمة
      allProducts.sort((a, b) {
        final aStock = int.tryParse(a['productStock']?.toString() ?? '0') ?? 0;
        final aPrice =
            double.tryParse(a['productSalePrice']?.toString() ?? '0') ?? 0.0;
        final bStock = int.tryParse(b['productStock']?.toString() ?? '0') ?? 0;
        final bPrice =
            double.tryParse(b['productSalePrice']?.toString() ?? '0') ?? 0.0;
        return (bStock * bPrice).compareTo(aStock * aPrice);
      });

      // أفضل 5 منتجات
      var topProducts = allProducts.take(5).map((product) {
        final stock =
            int.tryParse(product['productStock']?.toString() ?? '0') ?? 0;
        final price =
            double.tryParse(product['productSalePrice']?.toString() ?? '0') ??
                0.0;
        return {
          'name': product['productName'] ?? 'منتج غير محدد',
          'stock': stock,
          'value': stock * price,
        };
      }).toList();

      // ترتيب الفئات حسب القيمة
      var categories = categoryData.values.toList();
      categories.sort((a, b) => b['value'].compareTo(a['value']));

      return {
        'total_products': totalProducts,
        'total_value': totalValue,
        'low_stock_items': lowStockItems,
        'out_of_stock_items': outOfStockItems,
        'categories': categories,
        'top_products': topProducts,
        'alerts': alerts.isEmpty ? ['جميع المنتجات في حالة جيدة'] : alerts,
      };
    } catch (e) {
      debugPrint('خطأ في جلب بيانات المخزون: $e');
      return {
        'error': 'خطأ في جلب بيانات المخزون: $e',
        'total_products': 0,
        'total_value': 0.0,
        'low_stock_items': 0,
        'out_of_stock_items': 0,
        'categories': [],
        'top_products': [],
        'alerts': ['خطأ في تحميل البيانات'],
      };
    }
  }

  // الحصول على بيانات العملاء الحقيقية
  static Future<Map<String, dynamic>> getCustomersData() async {
    try {
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        return {
          'total_customers': 0,
          'active_customers': 0,
          'new_customers_this_month': 0,
          'top_customers': [],
          'customer_segments': [],
          'outstanding_dues': 0.0,
          'overdue_customers': 0,
        };
      }

      List<CustomerModel> allCustomers = [];
      for (var element in snapshot.children) {
        try {
          var data =
              CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
          allCustomers.add(data);
        } catch (e) {
          continue;
        }
      }

      // حساب العملاء النشطين والجدد
      int activeCustomers = 0;
      int newCustomersThisMonth = 0;
      int overdueCustomers = 0;
      double outstandingDues = 0.0;

      // الحصول على بيانات المبيعات لحساب أفضل العملاء
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final salesSnapshot = await salesRef.get();

      Map<String, Map<String, dynamic>> customerSales = {};

      if (salesSnapshot.exists) {
        for (var element in salesSnapshot.children) {
          try {
            var saleData = jsonDecode(jsonEncode(element.value));
            final customerName =
                saleData['customerName']?.toString() ?? 'عميل غير محدد';
            final amount =
                double.tryParse(saleData['totalAmount']?.toString() ?? '0') ??
                    0.0;
            final dueAmount =
                double.tryParse(saleData['dueAmount']?.toString() ?? '0') ??
                    0.0;

            if (customerSales.containsKey(customerName)) {
              customerSales[customerName]!['total_purchases'] += amount;
              customerSales[customerName]!['orders'] += 1;
            } else {
              customerSales[customerName] = {
                'name': customerName,
                'total_purchases': amount,
                'orders': 1,
              };
            }

            // حساب المديونيات المتأخرة
            if (dueAmount > 0) {
              outstandingDues += dueAmount;
              overdueCustomers++;
            }
          } catch (e) {
            continue;
          }
        }
      }

      // حساب العملاء النشطين
      activeCustomers = customerSales.length;

      // حساب العملاء الجدد (الذين لديهم أقل من 3 طلبات)
      newCustomersThisMonth = customerSales.values
          .where((customer) => customer['orders'] <= 3)
          .length;

      // ترتيب أفضل العملاء
      var topCustomers = customerSales.values.toList();
      topCustomers
          .sort((a, b) => b['total_purchases'].compareTo(a['total_purchases']));
      topCustomers = topCustomers.take(5).toList();

      // تصنيف العملاء
      List<Map<String, dynamic>> customerSegments = [
        {
          'segment': 'عملاء VIP',
          'count': customerSales.values
              .where((c) => c['total_purchases'] > 50000)
              .length,
          'value': customerSales.values
              .where((c) => c['total_purchases'] > 50000)
              .fold(0.0, (sum, c) => sum + c['total_purchases']),
        },
        {
          'segment': 'عملاء منتظمون',
          'count': customerSales.values
              .where((c) =>
                  c['total_purchases'] >= 10000 &&
                  c['total_purchases'] <= 50000)
              .length,
          'value': customerSales.values
              .where((c) =>
                  c['total_purchases'] >= 10000 &&
                  c['total_purchases'] <= 50000)
              .fold(0.0, (sum, c) => sum + c['total_purchases']),
        },
        {
          'segment': 'عملاء جدد',
          'count': customerSales.values
              .where((c) => c['total_purchases'] < 10000)
              .length,
          'value': customerSales.values
              .where((c) => c['total_purchases'] < 10000)
              .fold(0.0, (sum, c) => sum + c['total_purchases']),
        },
      ];

      return {
        'total_customers': allCustomers.length,
        'active_customers': activeCustomers,
        'new_customers_this_month': newCustomersThisMonth,
        'top_customers': topCustomers,
        'customer_segments': customerSegments,
        'outstanding_dues': outstandingDues,
        'overdue_customers': overdueCustomers,
      };
    } catch (e) {
      return {
        'error': 'خطأ في جلب بيانات العملاء: $e',
        'total_customers': 0,
        'active_customers': 0,
        'new_customers_this_month': 0,
        'top_customers': [],
        'customer_segments': [],
        'outstanding_dues': 0.0,
        'overdue_customers': 0,
      };
    }
  }

  // الحصول على بيانات الموردين الحقيقية من العملاء مع type: "Supplier"
  static Future<Map<String, dynamic>> getSuppliersData() async {
    try {
      // جلب الموردين من Customers node مع فلترة type: "Supplier"
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        return {
          'total_suppliers': 0,
          'active_suppliers': 0,
          'top_suppliers': [],
          'supplier_performance': [],
          'pending_orders': 0,
          'overdue_payments': 0.0,
          'total_dues': 0.0,
        };
      }

      List<Map<String, dynamic>> allSuppliers = [];
      double totalDues = 0.0;

      // فلترة الموردين فقط (type: "Supplier")
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));

          // التحقق من أن النوع هو "Supplier"
          if (customerData['type']?.toString() == 'Supplier') {
            // حساب المديونيات
            final dueAmount =
                double.tryParse(customerData['due']?.toString() ?? '0') ?? 0.0;
            totalDues += dueAmount;

            allSuppliers.add({
              'id': element.key,
              'name': customerData['customerName'] ?? 'مورد غير محدد',
              'phone': customerData['phoneNumber'] ?? '',
              'email': customerData['emailAddress'] ?? '',
              'address': customerData['customerAddress'] ?? '',
              'due_amount': dueAmount,
              'opening_balance': double.tryParse(
                      customerData['openingBalance']?.toString() ?? '0') ??
                  0.0,
              'remained_balance': double.tryParse(
                      customerData['remainedBalance']?.toString() ?? '0') ??
                  0.0,
              'profile_picture': customerData['profilePicture'] ?? '',
              'note': customerData['note'] ?? '',
            });
          }
        } catch (e) {
          continue;
        }
      }

      // الحصول على بيانات المشتريات لحساب أداء الموردين
      final purchaseRef = FirebaseDatabase.instance
          .ref(constUserId)
          .child('Purchase Transition');
      final purchaseSnapshot = await purchaseRef.get();

      Map<String, Map<String, dynamic>> supplierPerformance = {};
      int pendingOrders = 0;
      double overduePayments = 0.0;

      if (purchaseSnapshot.exists) {
        for (var element in purchaseSnapshot.children) {
          try {
            var purchaseData = jsonDecode(jsonEncode(element.value));
            final supplierName =
                purchaseData['customerName']?.toString() ?? 'مورد غير محدد';
            final amount = double.tryParse(
                    purchaseData['totalAmount']?.toString() ?? '0') ??
                0.0;
            final isPaid = purchaseData['isPaid'] == true;
            final dueAmount =
                double.tryParse(purchaseData['dueAmount']?.toString() ?? '0') ??
                    0.0;

            if (supplierPerformance.containsKey(supplierName)) {
              supplierPerformance[supplierName]!['total_orders'] += amount;
              supplierPerformance[supplierName]!['orders'] += 1;
              supplierPerformance[supplierName]!['total_due'] += dueAmount;
            } else {
              supplierPerformance[supplierName] = {
                'name': supplierName,
                'total_orders': amount,
                'orders': 1,
                'total_due': dueAmount,
              };
            }

            // حساب الطلبات المعلقة والمدفوعات المتأخرة
            if (!isPaid) {
              if (dueAmount > 0) {
                overduePayments += dueAmount;
              } else {
                pendingOrders++;
              }
            }
          } catch (e) {
            continue;
          }
        }
      }

      // ترتيب الموردين حسب الأداء
      var topSuppliers = supplierPerformance.values.toList();
      topSuppliers
          .sort((a, b) => b['total_orders'].compareTo(a['total_orders']));
      topSuppliers = topSuppliers.take(5).toList();

      return {
        'total_suppliers': allSuppliers.length,
        'active_suppliers': supplierPerformance.length,
        'top_suppliers': topSuppliers,
        'supplier_performance': supplierPerformance.values.toList(),
        'pending_orders': pendingOrders,
        'overdue_payments': overduePayments,
        'total_dues': totalDues,
        'suppliers_list': allSuppliers,
      };
    } catch (e) {
      debugPrint('خطأ في جلب بيانات الموردين: $e');
      return {};
    }
  }

  // الحصول على التقارير المالية الحقيقية
  static Future<Map<String, dynamic>> getFinancialReports() async {
    try {
      // جلب بيانات المبيعات والمشتريات الحقيقية
      final salesData = await getSalesData();
      final purchasesData = await getPurchasesData();

      final revenue = salesData['total_sales'] ?? 0.0;
      final expenses = purchasesData['total_purchases'] ?? 0.0;
      final profit = revenue - expenses;
      final profitMargin = revenue > 0 ? (profit / revenue * 100) : 0.0;

      // حساب التدفق النقدي من المبيعات والمشتريات
      final todaySales = salesData['today_sales'] ?? 0.0;
      final todayPurchases = purchasesData['today_purchases'] ?? 0.0;
      final cashFlow = todaySales - todayPurchases;

      // حساب الذمم المدينة والدائنة من البيانات الحقيقية
      final customersData = await getCustomersData();
      final accountsReceivable = customersData['outstanding_dues'] ?? 0.0;
      final accountsPayable = purchasesData['overdue_payments'] ?? 0.0;

      // تحليل شهري للمبيعات (آخر 3 شهور)
      List<Map<String, dynamic>> monthlyBreakdown = [];
      final now = DateTime.now();
      for (int i = 2; i >= 0; i--) {
        final month = DateTime(now.year, now.month - i, 1);
        final monthName = _getMonthName(month.month);

        // هنا يمكن إضافة حساب دقيق للشهر من البيانات الحقيقية
        // لكن حالياً سنستخدم تقسيم تقريبي
        final monthlyRevenue = revenue / 12; // تقسيم سنوي تقريبي
        final monthlyProfit = profit / 12;

        monthlyBreakdown.add({
          'month': monthName,
          'revenue': monthlyRevenue,
          'profit': monthlyProfit,
        });
      }

      return {
        'revenue': revenue,
        'expenses': expenses,
        'profit': profit,
        'profit_margin': profitMargin,
        'cash_flow': cashFlow,
        'accounts_receivable': accountsReceivable,
        'accounts_payable': accountsPayable,
        'monthly_breakdown': monthlyBreakdown,
        'expense_categories': [
          {'category': 'مشتريات', 'amount': expenses},
          {'category': 'مصاريف أخرى', 'amount': 0.0},
        ],
      };
    } catch (e) {
      debugPrint('خطأ في جلب التقارير المالية: $e');
      return {
        'error': 'خطأ في جلب التقارير المالية: $e',
        'revenue': 0.0,
        'expenses': 0.0,
        'profit': 0.0,
        'profit_margin': 0.0,
        'cash_flow': 0.0,
        'accounts_receivable': 0.0,
        'accounts_payable': 0.0,
        'monthly_breakdown': [],
        'expense_categories': [],
      };
    }
  }

  // دالة مساعدة لأسماء الشهور
  static String _getMonthName(int month) {
    const months = [
      '',
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return months[month];
  }

  // البحث عن عميل حقيقي
  static Future<Map<String, dynamic>?> searchCustomer(String query) async {
    try {
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) return null;

      // البحث في العملاء الحقيقيين
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          final customerName = customerData['customerName']?.toString() ?? '';
          final customerPhone = customerData['phoneNumber']?.toString() ?? '';
          final customerEmail = customerData['emailAddress']?.toString() ?? '';

          // التحقق من تطابق البحث
          if (customerName.toLowerCase().contains(query.toLowerCase()) ||
              customerPhone.contains(query) ||
              customerEmail.toLowerCase().contains(query.toLowerCase())) {
            // حساب إجمالي المشتريات من المبيعات
            final salesRef = FirebaseDatabase.instance
                .ref(constUserId)
                .child('Sales Transition');
            final salesSnapshot = await salesRef.get();

            double totalPurchases = 0.0;
            int ordersCount = 0;
            String lastOrderDate = 'لا توجد طلبات';

            if (salesSnapshot.exists) {
              for (var saleElement in salesSnapshot.children) {
                try {
                  var saleData = jsonDecode(jsonEncode(saleElement.value));
                  if (saleData['customerName'] == customerName) {
                    final amount = double.tryParse(
                            saleData['totalAmount']?.toString() ?? '0') ??
                        0.0;
                    totalPurchases += amount;
                    ordersCount++;
                    lastOrderDate = saleData['purchaseDate'] ?? lastOrderDate;
                  }
                } catch (e) {
                  continue;
                }
              }
            }

            return {
              'id': element.key,
              'name': customerName,
              'phone': customerPhone,
              'email': customerEmail,
              'address': customerData['customerAddress'] ?? '',
              'total_purchases': totalPurchases,
              'orders_count': ordersCount,
              'last_order_date': lastOrderDate,
              'outstanding_balance': double.tryParse(
                      customerData['dueAmount']?.toString() ?? '0') ??
                  0.0,
              'status': totalPurchases > 0 ? 'نشط' : 'غير نشط',
              'type': customerData['type'] ?? 'عادي',
            };
          }
        } catch (e) {
          continue;
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في البحث عن العميل: $e');
      return null;
    }
  }

  // البحث عن مورد حقيقي
  static Future<Map<String, dynamic>?> searchSupplier(String query) async {
    try {
      final suppliersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Suppliers');
      final snapshot = await suppliersRef.get();

      if (!snapshot.exists) return null;

      // البحث في الموردين الحقيقيين
      for (var element in snapshot.children) {
        try {
          var supplierData = jsonDecode(jsonEncode(element.value));
          final supplierName = supplierData['customerName']?.toString() ?? '';
          final supplierPhone = supplierData['phoneNumber']?.toString() ?? '';
          final supplierEmail = supplierData['emailAddress']?.toString() ?? '';

          // التحقق من تطابق البحث
          if (supplierName.toLowerCase().contains(query.toLowerCase()) ||
              supplierPhone.contains(query) ||
              supplierEmail.toLowerCase().contains(query.toLowerCase())) {
            // حساب إجمالي الطلبات من المشتريات
            final purchaseRef = FirebaseDatabase.instance
                .ref(constUserId)
                .child('Purchase Transition');
            final purchaseSnapshot = await purchaseRef.get();

            double totalOrders = 0.0;
            int ordersCount = 0;
            String lastOrderDate = 'لا توجد طلبات';

            if (purchaseSnapshot.exists) {
              for (var purchaseElement in purchaseSnapshot.children) {
                try {
                  var purchaseData =
                      jsonDecode(jsonEncode(purchaseElement.value));
                  if (purchaseData['customerName'] == supplierName) {
                    final amount = double.tryParse(
                            purchaseData['totalAmount']?.toString() ?? '0') ??
                        0.0;
                    totalOrders += amount;
                    ordersCount++;
                    lastOrderDate =
                        purchaseData['purchaseDate'] ?? lastOrderDate;
                  }
                } catch (e) {
                  continue;
                }
              }
            }

            return {
              'id': element.key,
              'name': supplierName,
              'phone': supplierPhone,
              'email': supplierEmail,
              'address': supplierData['customerAddress'] ?? '',
              'total_orders': totalOrders,
              'orders_count': ordersCount,
              'last_order_date': lastOrderDate,
              'outstanding_balance': double.tryParse(
                      supplierData['dueAmount']?.toString() ?? '0') ??
                  0.0,
              'status': totalOrders > 0 ? 'نشط' : 'غير نشط',
              'type': supplierData['type'] ?? 'مورد',
            };
          }
        } catch (e) {
          continue;
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في البحث عن المورد: $e');
      return null;
    }
  }

  // البحث عن منتج حقيقي
  static Future<Map<String, dynamic>?> searchProduct(String query) async {
    try {
      final productsRef =
          FirebaseDatabase.instance.ref(constUserId).child('Products');
      final snapshot = await productsRef.get();

      if (!snapshot.exists) return null;

      // البحث في المنتجات الحقيقية
      for (var element in snapshot.children) {
        try {
          var productData = jsonDecode(jsonEncode(element.value));
          final productName = productData['productName']?.toString() ?? '';
          final productCode = productData['productCode']?.toString() ?? '';
          final productCategory =
              productData['productCategory']?.toString() ?? '';

          // التحقق من تطابق البحث
          if (productName.toLowerCase().contains(query.toLowerCase()) ||
              productCode.toLowerCase().contains(query.toLowerCase()) ||
              productCategory.toLowerCase().contains(query.toLowerCase())) {
            return {
              'id': element.key,
              'name': productName,
              'category': productCategory,
              'price': double.tryParse(
                      productData['productSalePrice']?.toString() ?? '0') ??
                  0.0,
              'purchase_price': double.tryParse(
                      productData['productPurchasePrice']?.toString() ?? '0') ??
                  0.0,
              'stock': int.tryParse(
                      productData['productStock']?.toString() ?? '0') ??
                  0,
              'sku': productCode,
              'description': productData['productDescription'] ?? 'لا يوجد وصف',
              'brand': productData['brandName'] ?? 'غير محدد',
              'unit': productData['productUnit'] ?? 'قطعة',
              'low_stock_alert': int.tryParse(
                      productData['lowerStockAlert']?.toString() ?? '5') ??
                  5,
              'weight': productData['weight'] ?? '',
              'size': productData['size'] ?? '',
              'color': productData['color'] ?? '',
              'manufacturer': productData['productManufacturer'] ?? '',
              'last_updated': DateTime.now().toString().split(' ')[0],
              'status': _getProductStatus(productData),
            };
          }
        } catch (e) {
          continue;
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في البحث عن المنتج: $e');
      return null;
    }
  }

  // دالة مساعدة لتحديد حالة المنتج
  static String _getProductStatus(Map<String, dynamic> productData) {
    final stock =
        int.tryParse(productData['productStock']?.toString() ?? '0') ?? 0;
    final lowStockAlert =
        int.tryParse(productData['lowerStockAlert']?.toString() ?? '5') ?? 5;

    if (stock == 0) {
      return 'نفد من المخزون';
    } else if (stock <= lowStockAlert) {
      return 'مخزون منخفض';
    } else {
      return 'متوفر';
    }
  }

  // الحصول على جميع البيانات
  static Future<Map<String, dynamic>> getAllBusinessData() async {
    try {
      final sales = await getSalesData();
      final purchases = await getPurchasesData();
      final inventory = await getInventoryData();
      final customers = await getCustomersData();
      final suppliers = await getSuppliersData();
      final financial = await getFinancialReports();

      return {
        'sales': sales,
        'purchases': purchases,
        'inventory': inventory,
        'customers': customers,
        'suppliers': suppliers,
        'financial': financial,
        'summary': {
          'total_revenue': sales['total_sales'] ?? 0.0,
          'total_profit': financial['profit'] ?? 0.0,
          'total_customers': customers['total_customers'] ?? 0,
          'total_products': inventory['total_products'] ?? 0,
          'inventory_value': inventory['total_value'] ?? 0.0,
        },
      };
    } catch (e) {
      debugPrint('خطأ في جلب جميع البيانات: $e');
      return {};
    }
  }

  // الحصول على المستخدمين الحقيقيين من Firebase Admin Panel/Seller List
  static Future<List<Map<String, dynamic>>> getRealUsers() async {
    try {
      List<Map<String, dynamic>> users = [];

      // جلب المستخدمين من Admin Panel/Seller List
      try {
        final sellerListRef =
            FirebaseDatabase.instance.ref('Admin Panel/Seller List');
        final sellerListSnapshot = await sellerListRef.get();

        if (sellerListSnapshot.exists) {
          for (var element in sellerListSnapshot.children) {
            try {
              var userData = jsonDecode(jsonEncode(element.value));

              // استخراج اسم المستخدم من البريد الإلكتروني
              String extractedUsername =
                  _extractUsernameFromEmail(userData['email'] ?? '');

              users.add({
                'id': element.key,
                'name': extractedUsername.isNotEmpty
                    ? extractedUsername
                    : (userData['companyName'] ?? 'مستخدم غير محدد'),
                'email': userData['email'] ?? '',
                'phone': userData['phoneNumber'] ?? '',
                'avatar': userData['pictureUrl'] ??
                    'https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3',
                'isOnline': _isUserOnline(userData['userId']),
                'lastSeen': userData['userRegistrationDate'] ??
                    DateTime.now().toString(),
                'lastMessage': 'مرحباً! كيف يمكنني مساعدتك؟',
                'time': _formatTime(userData['userRegistrationDate']),
                'unreadCount': 0,
                'role': 'seller',
                'isActive': true,
                'companyName': userData['companyName'] ?? '',
                'businessCategory': userData['businessCategory'] ?? '',
                'countryName': userData['countryName'] ?? '',
                'language': userData['language'] ?? 'Arabic',
                'subscriptionName': userData['subscriptionName'] ?? '',
                'subscriptionDate': userData['subscriptionDate'] ?? '',
                'userId': userData['userId'] ?? element.key,
              });
            } catch (e) {
              debugPrint('خطأ في معالجة بيانات المستخدم: $e');
              continue;
            }
          }
        }
      } catch (e) {
        debugPrint('خطأ في جلب المستخدمين من Admin Panel/Seller List: $e');
      }

      // إضافة المستخدم الرئيسي الحالي إذا لم يكن موجوداً
      try {
        final personalInfoRef = FirebaseDatabase.instance
            .ref(constUserId)
            .child('Personal Information');
        final personalInfoSnapshot = await personalInfoRef.get();

        if (personalInfoSnapshot.exists) {
          var personalData = jsonDecode(jsonEncode(personalInfoSnapshot.value));

          // التحقق من عدم وجود المستخدم الحالي في القائمة
          bool currentUserExists = users.any((user) =>
              user['email'] == personalData['emailAddress'] ||
              user['userId'] == constUserId);

          if (!currentUserExists) {
            String extractedUsername =
                _extractUsernameFromEmail(personalData['emailAddress'] ?? '');

            users.insert(0, {
              'id': 'current_user',
              'name': extractedUsername.isNotEmpty
                  ? extractedUsername
                  : (personalData['companyName'] ?? 'المستخدم الحالي'),
              'email': personalData['emailAddress'] ?? '',
              'phone': personalData['phoneNumber'] ?? '',
              'avatar': personalData['pictureUrl'] ??
                  'https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3',
              'isOnline': true,
              'lastSeen': DateTime.now().toString(),
              'lastMessage': 'المستخدم الحالي',
              'time': _formatTime(DateTime.now().toString()),
              'unreadCount': 0,
              'role': 'admin',
              'isActive': true,
              'companyName': personalData['companyName'] ?? '',
              'userId': constUserId,
            });
          }
        }
      } catch (e) {
        debugPrint('خطأ في جلب بيانات المستخدم الحالي: $e');
      }

      // ترتيب المستخدمين: المستخدم الحالي أولاً، ثم حسب آخر نشاط
      users.sort((a, b) {
        // المستخدم الحالي أولاً
        if (a['role'] == 'admin') return -1;
        if (b['role'] == 'admin') return 1;

        // ثم ترتيب حسب آخر نشاط
        final aTime = DateTime.tryParse(a['lastSeen']) ?? DateTime.now();
        final bTime = DateTime.tryParse(b['lastSeen']) ?? DateTime.now();
        return bTime.compareTo(aTime);
      });

      debugPrint(
          'تم جلب ${users.length} مستخدم بنجاح من Admin Panel/Seller List');
      return users;
    } catch (e) {
      debugPrint('خطأ في جلب المستخدمين: $e');
      return [];
    }
  }

  // دالة مساعدة لاستخراج اسم المستخدم من البريد الإلكتروني
  static String _extractUsernameFromEmail(String email) {
    if (email.isEmpty) return '';

    try {
      // استخراج الجزء قبل @ من البريد الإلكتروني
      final username = email.split('@')[0];

      // تنظيف اسم المستخدم وجعل أول حرف كبير
      if (username.isNotEmpty) {
        return username[0].toUpperCase() + username.substring(1).toLowerCase();
      }

      return username;
    } catch (e) {
      debugPrint('خطأ في استخراج اسم المستخدم من البريد: $e');
      return email;
    }
  }

  // دالة مساعدة للتحقق من حالة المستخدم (يمكن تطويرها لاحقاً)
  static bool _isUserOnline(String? userId) {
    // يمكن تطوير هذه الدالة لاحقاً للتحقق من حالة المستخدم الفعلية
    // مؤقتاً نعتبر المستخدمين متصلين بشكل عشوائي
    return DateTime.now().millisecondsSinceEpoch % 2 == 0;
  }

  // الحصول على المستخدمين النشطين فقط
  static Future<List<Map<String, dynamic>>> getActiveUsers() async {
    try {
      final allUsers = await getRealUsers();
      return allUsers.where((user) => user['isActive'] == true).toList();
    } catch (e) {
      debugPrint('خطأ في جلب المستخدمين النشطين: $e');
      return [];
    }
  }

  // البحث في المستخدمين
  static Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    try {
      final allUsers = await getRealUsers();
      if (query.isEmpty) return allUsers;

      return allUsers.where((user) {
        final name = user['name'].toString().toLowerCase();
        final email = user['email'].toString().toLowerCase();
        final phone = user['phone'].toString().toLowerCase();
        final searchQuery = query.toLowerCase();

        return name.contains(searchQuery) ||
            email.contains(searchQuery) ||
            phone.contains(searchQuery);
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث عن المستخدمين: $e');
      return [];
    }
  }

  // تحديث حالة المستخدم (متصل/غير متصل)
  static Future<void> updateUserStatus(String userId, bool isOnline) async {
    try {
      final userRef = FirebaseDatabase.instance.ref('users/$userId');
      await userRef.update({
        'isOnline': isOnline,
        'lastSeen': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المستخدم: $e');
    }
  }

  // تحديث آخر رسالة للمستخدم
  static Future<void> updateUserLastMessage(
      String userId, String message) async {
    try {
      final userRef = FirebaseDatabase.instance.ref('users/$userId');
      await userRef.update({
        'lastMessage': message,
        'lastSeen': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في تحديث آخر رسالة: $e');
    }
  }

  // تحديث عدد الرسائل غير المقروءة
  static Future<void> updateUnreadCount(String userId, int count) async {
    try {
      final userRef = FirebaseDatabase.instance.ref('users/$userId');
      await userRef.update({
        'unreadCount': count,
      });
    } catch (e) {
      debugPrint('خطأ في تحديث عدد الرسائل غير المقروءة: $e');
    }
  }

  // دالة مساعدة لتنسيق الوقت
  static String _formatTime(String? dateTimeString) {
    try {
      if (dateTimeString == null) return 'غير محدد';

      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return 'الآن';
      } else if (difference.inMinutes < 60) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else if (difference.inHours < 24) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inDays == 1) {
        return 'أمس';
      } else if (difference.inDays < 7) {
        return 'منذ ${difference.inDays} أيام';
      } else {
        return '${dateTime.day}/${dateTime.month}';
      }
    } catch (e) {
      return 'غير محدد';
    }
  }

  // الحصول على معلومات مستخدم محدد
  static Future<Map<String, dynamic>?> getUserById(String userId) async {
    try {
      final userRef = FirebaseDatabase.instance.ref('users/$userId');
      final snapshot = await userRef.get();

      if (!snapshot.exists) return null;

      var userData = jsonDecode(jsonEncode(snapshot.value));
      return {
        'id': userId,
        'name':
            userData['name'] ?? userData['displayName'] ?? 'مستخدم غير محدد',
        'email': userData['email'] ?? '',
        'phone': userData['phone'] ?? userData['phoneNumber'] ?? '',
        'avatar': userData['photoURL'] ??
            userData['avatar'] ??
            userData['profileImage'],
        'isOnline': userData['isOnline'] ?? false,
        'lastSeen': userData['lastSeen'] ?? DateTime.now().toString(),
        'role': userData['role'] ?? 'user',
        'isActive': userData['isActive'] ?? true,
      };
    } catch (e) {
      debugPrint('خطأ في جلب معلومات المستخدم: $e');
      return null;
    }
  }

  // ===== خدمات الرسائل =====

  // الحصول على الرسائل بين مستخدمين
  static Future<List<Map<String, dynamic>>> getChatMessages(
      String userId1, String userId2) async {
    try {
      final chatId = _generateChatId(userId1, userId2);
      final messagesRef =
          FirebaseDatabase.instance.ref('chats/$chatId/messages');
      final snapshot = await messagesRef.orderByChild('timestamp').get();

      if (!snapshot.exists) {
        return [];
      }

      List<Map<String, dynamic>> messages = [];
      for (var element in snapshot.children) {
        try {
          var messageData = jsonDecode(jsonEncode(element.value));
          messages.add({
            'id': element.key,
            'text': messageData['text'] ?? '',
            'senderId': messageData['senderId'] ?? '',
            'receiverId': messageData['receiverId'] ?? '',
            'timestamp': messageData['timestamp'] ??
                DateTime.now().millisecondsSinceEpoch,
            'isRead': messageData['isRead'] ?? false,
            'messageType':
                messageData['messageType'] ?? 'text', // text, image, file
            'fileUrl': messageData['fileUrl'],
            'fileName': messageData['fileName'],
          });
        } catch (e) {
          debugPrint('خطأ في معالجة الرسالة: $e');
          continue;
        }
      }

      // ترتيب الرسائل حسب الوقت
      messages.sort((a, b) => a['timestamp'].compareTo(b['timestamp']));

      return messages;
    } catch (e) {
      debugPrint('خطأ في جلب الرسائل: $e');
      return [];
    }
  }

  // إرسال رسالة جديدة
  static Future<bool> sendMessage({
    required String senderId,
    required String receiverId,
    required String text,
    String messageType = 'text',
    String? fileUrl,
    String? fileName,
  }) async {
    try {
      final chatId = _generateChatId(senderId, receiverId);
      final messagesRef =
          FirebaseDatabase.instance.ref('chats/$chatId/messages');
      final newMessageRef = messagesRef.push();

      final messageData = {
        'text': text,
        'senderId': senderId,
        'receiverId': receiverId,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'isRead': false,
        'messageType': messageType,
        if (fileUrl != null) 'fileUrl': fileUrl,
        if (fileName != null) 'fileName': fileName,
      };

      await newMessageRef.set(messageData);

      // تحديث معلومات الدردشة
      await _updateChatInfo(chatId, senderId, receiverId, text);

      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');
      return false;
    }
  }

  // تحديث حالة قراءة الرسائل
  static Future<void> markMessagesAsRead(
      String userId1, String userId2, String currentUserId) async {
    try {
      final chatId = _generateChatId(userId1, userId2);
      final messagesRef =
          FirebaseDatabase.instance.ref('chats/$chatId/messages');
      final snapshot = await messagesRef
          .orderByChild('receiverId')
          .equalTo(currentUserId)
          .get();

      if (!snapshot.exists) return;

      Map<String, Object> updates = {};
      for (var element in snapshot.children) {
        var messageData = jsonDecode(jsonEncode(element.value));
        if (messageData['isRead'] == false) {
          updates['${element.key}/isRead'] = true;
        }
      }

      if (updates.isNotEmpty) {
        await messagesRef.update(updates);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة القراءة: $e');
    }
  }

  // الحصول على آخر رسالة في الدردشة
  static Future<Map<String, dynamic>?> getLastMessage(
      String userId1, String userId2) async {
    try {
      final chatId = _generateChatId(userId1, userId2);
      final messagesRef =
          FirebaseDatabase.instance.ref('chats/$chatId/messages');
      final snapshot =
          await messagesRef.orderByChild('timestamp').limitToLast(1).get();

      if (!snapshot.exists) return null;

      for (var element in snapshot.children) {
        var messageData = jsonDecode(jsonEncode(element.value));
        return {
          'text': messageData['text'] ?? '',
          'timestamp':
              messageData['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
          'senderId': messageData['senderId'] ?? '',
        };
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في جلب آخر رسالة: $e');
      return null;
    }
  }

  // عدد الرسائل غير المقروءة
  static Future<int> getUnreadMessagesCount(
      String userId1, String userId2, String currentUserId) async {
    try {
      final chatId = _generateChatId(userId1, userId2);
      final messagesRef =
          FirebaseDatabase.instance.ref('chats/$chatId/messages');
      final snapshot = await messagesRef
          .orderByChild('receiverId')
          .equalTo(currentUserId)
          .get();

      if (!snapshot.exists) return 0;

      int unreadCount = 0;
      for (var element in snapshot.children) {
        var messageData = jsonDecode(jsonEncode(element.value));
        if (messageData['isRead'] == false) {
          unreadCount++;
        }
      }

      return unreadCount;
    } catch (e) {
      debugPrint('خطأ في عد الرسائل غير المقروءة: $e');
      return 0;
    }
  }

  // دالة مساعدة لتوليد معرف الدردشة
  static String _generateChatId(String userId1, String userId2) {
    List<String> ids = [userId1, userId2];
    ids.sort();
    return '${ids[0]}_${ids[1]}';
  }

  // تحديث معلومات الدردشة
  static Future<void> _updateChatInfo(String chatId, String senderId,
      String receiverId, String lastMessage) async {
    try {
      final chatRef = FirebaseDatabase.instance.ref('chats/$chatId/info');
      await chatRef.update({
        'lastMessage': lastMessage,
        'lastMessageTime': DateTime.now().millisecondsSinceEpoch,
        'lastSenderId': senderId,
        'participants': [senderId, receiverId],
      });
    } catch (e) {
      debugPrint('خطأ في تحديث معلومات الدردشة: $e');
    }
  }

  // الحصول على جميع الدردشات للمستخدم
  static Future<List<Map<String, dynamic>>> getUserChats(String userId) async {
    try {
      final chatsRef = FirebaseDatabase.instance.ref('chats');
      final snapshot = await chatsRef.get();

      if (!snapshot.exists) return [];

      List<Map<String, dynamic>> userChats = [];
      for (var element in snapshot.children) {
        try {
          var chatData = jsonDecode(jsonEncode(element.value));
          var info = chatData['info'];

          if (info != null && info['participants'] != null) {
            List<dynamic> participants = info['participants'];
            if (participants.contains(userId)) {
              // الحصول على معلومات المستخدم الآخر
              String otherUserId =
                  participants.firstWhere((id) => id != userId);
              var otherUser = await getUserById(otherUserId);

              if (otherUser != null) {
                userChats.add({
                  'chatId': element.key,
                  'user': otherUser,
                  'lastMessage': info['lastMessage'] ?? '',
                  'lastMessageTime': info['lastMessageTime'] ?? 0,
                  'unreadCount':
                      await getUnreadMessagesCount(userId, otherUserId, userId),
                });
              }
            }
          }
        } catch (e) {
          debugPrint('خطأ في معالجة بيانات الدردشة: $e');
          continue;
        }
      }

      // ترتيب الدردشات حسب آخر رسالة
      userChats
          .sort((a, b) => b['lastMessageTime'].compareTo(a['lastMessageTime']));

      return userChats;
    } catch (e) {
      debugPrint('خطأ في جلب دردشات المستخدم: $e');
      return [];
    }
  }
}
