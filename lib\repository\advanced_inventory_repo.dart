// مستودع بيانات المخزون المتقدم
import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/models/advanced_inventory_model.dart';
import 'package:mobile_pos/model/product_model.dart';

/// مستودع بيانات المخزون المتقدم
class AdvancedInventoryRepo {
  /// الحصول على بيانات المخزون المتقدم
  Future<List<AdvancedInventoryModel>> getAdvancedInventoryData(
    List<ProductModel> products,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<AdvancedInventoryModel> result = [];

    for (var product in products) {
      try {
        // حساب رصيد أول المدة
        final openingStock =
            await getStockAtDate(product.productCode, startDate);

        // حساب رصيد آخر المدة
        final closingStock = await getStockAtDate(product.productCode, endDate);

        // حساب حركات المخزون خلال الفترة
        final movements = await getInventoryMovements(
            product.productCode, startDate, endDate);

        // حساب المشتريات والمبيعات والتعديلات
        int purchases = 0;
        int sales = 0;
        int adjustments = 0;

        for (var movement in movements) {
          if (movement.type == 'purchase') {
            purchases += movement.quantity;
          } else if (movement.type == 'sale') {
            sales += movement.quantity.abs();
          } else {
            adjustments += movement.quantity;
          }
        }

        // إنشاء نموذج المخزون المتقدم
        final inventoryModel = AdvancedInventoryModel.fromProduct(
          product,
          openingStock,
          closingStock,
          purchases,
          sales,
          adjustments,
          startDate,
          endDate,
          movements,
        );

        result.add(inventoryModel);
      } catch (e) {
        // خطأ في الحصول على بيانات المخزون المتقدم للمنتج ${product.productCode}
      }
    }

    return result;
  }

  /// الحصول على رصيد المخزون في تاريخ معين
  Future<int> getStockAtDate(String productCode, DateTime date) async {
    try {
      // الحصول على المخزون الحالي
      final productRef = FirebaseDatabase.instance.ref('$constUserId/Products');
      final productSnapshot = await productRef
          .orderByChild('productCode')
          .equalTo(productCode)
          .get();

      if (!productSnapshot.exists) {
        return 0;
      }

      int currentStock = 0;

      for (var child in productSnapshot.children) {
        var data = jsonDecode(jsonEncode(child.value));
        currentStock = int.tryParse(data['productStock'].toString()) ?? 0;
      }

      // إذا كان التاريخ هو اليوم، نعيد المخزون الحالي
      final now = DateTime.now();
      if (date.year == now.year &&
          date.month == now.month &&
          date.day == now.day) {
        return currentStock;
      }

      // إذا كان التاريخ في المستقبل، نعيد المخزون الحالي
      if (date.isAfter(now)) {
        return currentStock;
      }

      // حساب حركات المخزون بين التاريخ المطلوب واليوم
      final movements = await getInventoryMovements(productCode, date, now);

      // حساب المخزون في التاريخ المطلوب
      int stockAtDate = currentStock;

      for (var movement in movements) {
        // عكس تأثير الحركات للوصول إلى الرصيد السابق
        // المشتريات تزيد المخزون، لذا نطرحها للوصول إلى الرصيد السابق
        // المبيعات تنقص المخزون، لذا نضيفها للوصول إلى الرصيد السابق
        if (movement.type == 'purchase') {
          stockAtDate -= movement.quantity; // طرح المشتريات
        } else if (movement.type == 'sale') {
          stockAtDate += movement.quantity
              .abs(); // إضافة المبيعات (القيمة المطلقة لأنها سالبة)
        }
      }

      return stockAtDate;
    } catch (e) {
      // خطأ في الحصول على رصيد المخزون في تاريخ معين
      return 0;
    }
  }

  /// الحصول على حركات المخزون خلال فترة
  Future<List<InventoryMovement>> getInventoryMovements(
    String productCode,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<InventoryMovement> movements = [];

    try {
      // الحصول على حركات المشتريات
      final purchaseMovements =
          await getPurchaseMovements(productCode, startDate, endDate);
      movements.addAll(purchaseMovements);

      // الحصول على حركات المبيعات
      final saleMovements =
          await getSaleMovements(productCode, startDate, endDate);
      movements.addAll(saleMovements);

      // ترتيب الحركات حسب التاريخ
      movements.sort((a, b) => a.date.compareTo(b.date));

      return movements;
    } catch (e) {
      // خطأ في الحصول على حركات المخزون
      return [];
    }
  }

  /// الحصول على حركات المشتريات
  Future<List<InventoryMovement>> getPurchaseMovements(
    String productCode,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<InventoryMovement> movements = [];

    try {
      final purchaseRef =
          FirebaseDatabase.instance.ref('$constUserId/Purchase Transition');
      final purchaseSnapshot = await purchaseRef.get();

      if (!purchaseSnapshot.exists) {
        return [];
      }

      for (var child in purchaseSnapshot.children) {
        var data = jsonDecode(jsonEncode(child.value));

        // التحقق من التاريخ
        final purchaseDate =
            DateTime.parse(data['purchaseDate'] ?? DateTime.now().toString());

        if (purchaseDate.isBefore(startDate) || purchaseDate.isAfter(endDate)) {
          continue;
        }

        // البحث عن المنتج في قائمة المنتجات
        if (data['productList'] != null) {
          for (var product in data['productList']) {
            if (product['productCode'] == productCode) {
              final quantity =
                  int.tryParse(product['quantity'].toString()) ?? 0;
              final price =
                  double.tryParse(product['productPurchasePrice'].toString()) ??
                      0;

              movements.add(InventoryMovement(
                id: child.key ?? '',
                type: 'purchase',
                reference: data['invoiceNumber'] ?? '',
                quantity: quantity,
                price: price,
                date: purchaseDate,
                notes: 'مشتريات',
              ));
            }
          }
        }
      }

      return movements;
    } catch (e) {
      // خطأ في الحصول على حركات المشتريات
      return [];
    }
  }

  /// الحصول على حركات المبيعات
  Future<List<InventoryMovement>> getSaleMovements(
    String productCode,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<InventoryMovement> movements = [];

    try {
      final saleRef =
          FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
      final saleSnapshot = await saleRef.get();

      if (!saleSnapshot.exists) {
        return [];
      }

      for (var child in saleSnapshot.children) {
        var data = jsonDecode(jsonEncode(child.value));

        // التحقق من التاريخ
        // تصحيح: استخدام saleDate بدلاً من purchaseDate للمبيعات
        final saleDate = DateTime.parse(data['saleDate'] ??
            data['purchaseDate'] ??
            DateTime.now().toString());

        if (saleDate.isBefore(startDate) || saleDate.isAfter(endDate)) {
          continue;
        }

        // البحث عن المنتج في قائمة المنتجات
        if (data['productList'] != null) {
          for (var product in data['productList']) {
            if (product['productCode'] == productCode) {
              final quantity =
                  int.tryParse(product['quantity'].toString()) ?? 0;
              final price =
                  double.tryParse(product['subTotal'].toString()) ?? 0;

              movements.add(InventoryMovement(
                id: child.key ?? '',
                type: 'sale',
                reference: data['invoiceNumber'] ?? '',
                quantity: -quantity, // سالب لأنها مبيعات
                price: price / quantity, // سعر الوحدة
                date: saleDate,
                notes: 'مبيعات',
              ));
            }
          }
        }
      }

      return movements;
    } catch (e) {
      // خطأ في الحصول على حركات المبيعات
      return [];
    }
  }
}
