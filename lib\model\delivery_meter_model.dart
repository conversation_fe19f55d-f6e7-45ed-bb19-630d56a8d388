class DeliveryMeterModel {
  late String id;
  late String tripId;
  late String meterType; // 'start' أو 'end'
  late String meterReading;
  late String readingMethod; // 'camera' أو 'manual'
  late String imageUrl;
  late String timestamp;
  late String vehicleId;
  late String driverId;
  late String notes;

  DeliveryMeterModel({
    required this.id,
    required this.tripId,
    required this.meterType,
    required this.meterReading,
    required this.readingMethod,
    required this.imageUrl,
    required this.timestamp,
    required this.vehicleId,
    required this.driverId,
    required this.notes,
  });

  DeliveryMeterModel.fromJson(Map<dynamic, dynamic> json) {
    id = json['id']?.toString() ?? '';
    tripId = json['tripId']?.toString() ?? '';
    meterType = json['meterType']?.toString() ?? '';
    meterReading = json['meterReading']?.toString() ?? '';
    readingMethod = json['readingMethod']?.toString() ?? '';
    imageUrl = json['imageUrl']?.toString() ?? '';
    timestamp = json['timestamp']?.toString() ?? '';
    vehicleId = json['vehicleId']?.toString() ?? '';
    driverId = json['driverId']?.toString() ?? '';
    notes = json['notes']?.toString() ?? '';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'id': id,
        'tripId': tripId,
        'meterType': meterType,
        'meterReading': meterReading,
        'readingMethod': readingMethod,
        'imageUrl': imageUrl,
        'timestamp': timestamp,
        'vehicleId': vehicleId,
        'driverId': driverId,
        'notes': notes,
      };
}
