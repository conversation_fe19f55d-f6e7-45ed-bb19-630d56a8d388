class SaleTransitionItemModel {
  final String productCode;
  final String productName;
  final int quantity;
  final String subTotal;
  final String productPurchasePrice;
  final String discount;
  final String vat;

  SaleTransitionItemModel({
    required this.productCode,
    required this.productName,
    required this.quantity,
    required this.subTotal,
    required this.productPurchasePrice,
    required this.discount,
    required this.vat,
  });

  Map<String, dynamic> toJson() {
    return {
      'productCode': productCode,
      'productName': productName,
      'quantity': quantity,
      'subTotal': subTotal,
      'productPurchasePrice': productPurchasePrice,
      'discount': discount,
      'vat': vat,
    };
  }

  factory SaleTransitionItemModel.fromJson(Map<String, dynamic> json) {
    return SaleTransitionItemModel(
      productCode: json['productCode'] ?? '',
      productName: json['productName'] ?? '',
      quantity: json['quantity'] ?? 0,
      subTotal: json['subTotal'] ?? '0',
      productPurchasePrice: json['productPurchasePrice'] ?? '0',
      discount: json['discount'] ?? '0',
      vat: json['vat'] ?? '0',
    );
  }

  factory SaleTransitionItemModel.fromAddToCartModel(dynamic item) {
    return SaleTransitionItemModel(
      productCode: item.productId ?? '',
      productName: item.productName ?? '',
      quantity: item.quantity ?? 0,
      subTotal: item.subTotal?.toString() ?? '0',
      productPurchasePrice: item.productPurchasePrice?.toString() ?? '0',
      discount: '0', // Valor por defecto
      vat: item.incTax?.toString() ?? '0',
    );
  }
}
