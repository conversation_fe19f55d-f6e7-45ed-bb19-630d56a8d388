// بسم الله الرحمن الرحيم
// نموذج بيانات ملف المستخدم

import 'dart:convert';

/// نموذج بيانات ملف المستخدم
class UserProfileModel {
  final String uid;
  final String? displayName;
  final String? email;
  final String? phoneNumber;
  final String? photoURL;
  final String? address;
  final String? bio;
  final DateTime? birthDate;
  final String? gender;
  final Map<String, dynamic>? preferences;
  final Map<String, dynamic>? statistics;
  final List<String>? roles;
  final DateTime? createdAt;
  final DateTime? lastActive;

  UserProfileModel({
    required this.uid,
    this.displayName,
    this.email,
    this.phoneNumber,
    this.photoURL,
    this.address,
    this.bio,
    this.birthDate,
    this.gender,
    this.preferences,
    this.statistics,
    this.roles,
    this.createdAt,
    this.lastActive,
  });

  /// نسخ النموذج مع تحديث بعض الحقول
  UserProfileModel copyWith({
    String? displayName,
    String? email,
    String? phoneNumber,
    String? photoURL,
    String? address,
    String? bio,
    DateTime? birthDate,
    String? gender,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? statistics,
    List<String>? roles,
    DateTime? lastActive,
  }) {
    return UserProfileModel(
      uid: uid,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoURL: photoURL ?? this.photoURL,
      address: address ?? this.address,
      bio: bio ?? this.bio,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      preferences: preferences ?? this.preferences,
      statistics: statistics ?? this.statistics,
      roles: roles ?? this.roles,
      createdAt: createdAt,
      lastActive: lastActive ?? this.lastActive,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'displayName': displayName,
      'email': email,
      'phoneNumber': phoneNumber,
      'photoURL': photoURL,
      'address': address,
      'bio': bio,
      'birthDate': birthDate?.millisecondsSinceEpoch,
      'gender': gender,
      'preferences': preferences,
      'statistics': statistics,
      'roles': roles,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'lastActive': lastActive?.millisecondsSinceEpoch,
    };
  }

  /// إنشاء نموذج من Map
  factory UserProfileModel.fromMap(Map<String, dynamic> map) {
    return UserProfileModel(
      uid: map['uid'] ?? '',
      displayName: map['displayName'],
      email: map['email'],
      phoneNumber: map['phoneNumber'],
      photoURL: map['photoURL'],
      address: map['address'],
      bio: map['bio'],
      birthDate: map['birthDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['birthDate'])
          : null,
      gender: map['gender'],
      preferences: map['preferences'],
      statistics: map['statistics'],
      roles: map['roles'] != null ? List<String>.from(map['roles']) : null,
      createdAt: map['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : null,
      lastActive: map['lastActive'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastActive'])
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory UserProfileModel.fromJson(String source) =>
      UserProfileModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'UserProfileModel(uid: $uid, displayName: $displayName, email: $email, phoneNumber: $phoneNumber)';
  }
}
