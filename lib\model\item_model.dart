class ItemModel {
  final String itemName;
  final String itemCode;
  final String invoiceNumber;
  final String purchaseDate;
  final double? totalDue;
  final double? dueAmountAfterPay;

  ItemModel({
    required this.itemName,
    required this.itemCode,
    required this.invoiceNumber,
    required this.purchaseDate,
    required this.totalDue,
    required this.dueAmountAfterPay,
  });

  factory ItemModel.fromJson(Map<String, dynamic> json) {
    return ItemModel(
      itemName: json['itemName'],
      itemCode: json['itemCode'],
      invoiceNumber: json['invoiceNumber'],
      purchaseDate: json['purchaseDate'],
      totalDue: json['totalDue'],
      dueAmountAfterPay: json['dueAmountAfterPay'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'itemName': itemName,
      'itemCode': itemCode,
      'invoiceNumber': invoiceNumber,
      'purchaseDate': purchaseDate,
      'totalDue': totalDue,
      'dueAmountAfterPay': dueAmountAfterPay,
    };
  }
}
