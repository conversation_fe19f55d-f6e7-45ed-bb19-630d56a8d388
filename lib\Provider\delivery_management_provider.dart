import 'package:flutter/material.dart';
import '../model/delivery_trip_model.dart';
import '../model/delivery_vehicle_model.dart';
import '../model/delivery_meter_model.dart';
import '../model/delivery_cost_model.dart';
import '../Screens/DeliveryManagement/services/delivery_management_service.dart';

class DeliveryManagementProvider extends ChangeNotifier {
  final List<DeliveryTripModel> _trips = [];
  final List<DeliveryVehicleModel> _vehicles = [];
  final List<DeliveryMeterModel> _meterReadings = [];
  final List<DeliveryCostModel> _costs = [];
  final Map<String, dynamic> _statistics = {};

  bool _isLoading = false;
  String _errorMessage = '';

  // Getters
  List<DeliveryTripModel> get trips => _trips;
  List<DeliveryVehicleModel> get vehicles => _vehicles;
  List<DeliveryMeterModel> get meterReadings => _meterReadings;
  List<DeliveryCostModel> get costs => _costs;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  /// تحميل جميع البيانات
  Future<void> loadAllData() async {
    _setLoading(true);
    try {
      await Future.wait([
        loadTrips(),
        loadVehicles(),
        loadStatistics(),
      ]);
      _clearError();
    } catch (e) {
      _setError('خطأ في تحميل البيانات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الرحلات
  Future<void> loadTrips() async {
    try {
      final trips = await DeliveryManagementService.getAllTrips();
      _trips.clear();
      _trips.addAll(trips);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الرحلات: $e');
    }
  }

  /// تحميل المركبات
  Future<void> loadVehicles() async {
    try {
      final vehicles = await DeliveryManagementService.getAllVehicles();
      _vehicles.clear();
      _vehicles.addAll(vehicles);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل المركبات: $e');
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final statistics =
          await DeliveryManagementService.getDeliveryStatistics();
      _statistics.clear();
      _statistics.addAll(statistics);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الإحصائيات: $e');
    }
  }

  /// إضافة رحلة جديدة
  Future<bool> addTrip(DeliveryTripModel trip) async {
    _setLoading(true);
    try {
      final success = await DeliveryManagementService.addTrip(trip);
      if (success) {
        _trips.insert(0, trip);
        await loadStatistics();
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في إضافة الرحلة');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة الرحلة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث رحلة
  Future<bool> updateTrip(DeliveryTripModel trip) async {
    _setLoading(true);
    try {
      final success = await DeliveryManagementService.updateTrip(trip);
      if (success) {
        final index = _trips.indexWhere((t) => t.id == trip.id);
        if (index != -1) {
          _trips[index] = trip;
        }
        await loadStatistics();
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في تحديث الرحلة');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث الرحلة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف رحلة
  Future<bool> deleteTrip(String tripId) async {
    _setLoading(true);
    try {
      final success = await DeliveryManagementService.deleteTrip(tripId);
      if (success) {
        _trips.removeWhere((trip) => trip.id == tripId);
        await loadStatistics();
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في حذف الرحلة');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الرحلة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة مركبة جديدة
  Future<bool> addVehicle(DeliveryVehicleModel vehicle) async {
    _setLoading(true);
    try {
      final success = await DeliveryManagementService.addVehicle(vehicle);
      if (success) {
        _vehicles.add(vehicle);
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في إضافة المركبة');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة المركبة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث مركبة
  Future<bool> updateVehicle(DeliveryVehicleModel vehicle) async {
    _setLoading(true);
    try {
      final success = await DeliveryManagementService.updateVehicle(vehicle);
      if (success) {
        final index = _vehicles.indexWhere((v) => v.id == vehicle.id);
        if (index != -1) {
          _vehicles[index] = vehicle;
        }
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في تحديث المركبة');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث المركبة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة قراءة عداد
  Future<bool> addMeterReading(DeliveryMeterModel meter) async {
    try {
      final success = await DeliveryManagementService.addMeterReading(meter);
      if (success) {
        _meterReadings.add(meter);
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في إضافة قراءة العداد');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة قراءة العداد: $e');
      return false;
    }
  }

  /// إضافة تكاليف رحلة
  Future<bool> addTripCosts(DeliveryCostModel costs) async {
    try {
      final success = await DeliveryManagementService.addTripCosts(costs);
      if (success) {
        _costs.add(costs);
        await loadStatistics();
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError('فشل في إضافة التكاليف');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة التكاليف: $e');
      return false;
    }
  }

  /// الحصول على الرحلات النشطة
  List<DeliveryTripModel> getActiveTrips() {
    return _trips.where((trip) => trip.status == 'active').toList();
  }

  /// الحصول على الرحلات المكتملة
  List<DeliveryTripModel> getCompletedTrips() {
    return _trips.where((trip) => trip.status == 'completed').toList();
  }

  /// البحث في الرحلات
  List<DeliveryTripModel> searchTrips(String query) {
    if (query.isEmpty) return _trips;

    return _trips.where((trip) {
      return trip.customerName.toLowerCase().contains(query.toLowerCase()) ||
          trip.vehicleName.toLowerCase().contains(query.toLowerCase()) ||
          trip.driverName.toLowerCase().contains(query.toLowerCase()) ||
          trip.invoiceNumber.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة خطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await loadAllData();
  }
}
