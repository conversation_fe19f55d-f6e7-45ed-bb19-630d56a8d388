# تحديث Android SDK إلى الإصدار 36

## التحديثات المطبقة:

### 1. ملف android/app/build.gradle
- تحديث `compileSdkVersion` من 35 إلى 36
- تحديث `targetSdkVersion` من 35 إلى 36
- تحديث dependencies:
  - `com.google.android.material` من 1.10.0 إلى 1.12.0
  - `androidx.core:core-ktx` من 1.12.0 إلى 1.15.0

### 2. ملف android/build.gradle
- إضافة `com.google.gms:google-services:4.4.2`
- الحفاظ على Android Gradle Plugin 8.9.0

### 3. ملف android/gradle.properties
- تحديث `android.suppressUnsupportedCompileSdk` إلى 36
- إضافة إعدادات Android 36:
  - `android.enableDexingArtifactTransform=false`
  - `android.enableDexingArtifactTransform.desugaring=false`
  - `android.experimental.enableSourceSetPathsMap=true`
  - `android.experimental.enableArtProfiles=false`

### 4. ملف android/app/proguard-rules.pro
- إضافة قواعد ProGuard للتوافق مع Android 36
- إضافة قواعد لتجاهل تحذيرات Java الجديدة

### 5. ملفات XML جديدة
- `android36_config.xml`: إعدادات التوافق
- `network_security_config.xml`: إعدادات أمان الشبكة

### 6. ملف AndroidManifest.xml
- إضافة `android:networkSecurityConfig="@xml/network_security_config"`

## متطلبات النظام:
- Android Studio Arctic Fox أو أحدث
- Android SDK 36
- Gradle 8.13
- Android Gradle Plugin 8.9.0

## خطوات التشغيل:
1. تأكد من تثبيت Android SDK 36
2. قم بتشغيل `flutter clean`
3. قم بتشغيل `flutter pub get`
4. قم بتشغيل `cd android && ./gradlew clean`
5. قم بتشغيل `flutter build apk` أو `flutter run`

## ملاحظات:
- جميع التحديثات متوافقة مع الإصدارات السابقة
- تم الحفاظ على جميع الإعدادات الحالية
- تم إضافة إعدادات الأمان المطلوبة لـ Android 36
