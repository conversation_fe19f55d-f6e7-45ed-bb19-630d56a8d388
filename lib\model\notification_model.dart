/// نموذج الإشعارات المستخدم في التطبيق
class NotificationModel {
  /// معرف الإشعار الفريد
  final String id;

  /// عنوان الإشعار
  final String title;

  /// محتوى الإشعار
  final String body;

  /// بيانات إضافية مرتبطة بالإشعار
  final Map<String, dynamic> data;

  /// تاريخ إنشاء الإشعار
  final DateTime timestamp;

  /// حالة قراءة الإشعار
  final bool isRead;

  /// معرف المستخدم المستلم للإشعار (اختياري)
  final String? recipientId;

  /// إنشاء نموذج إشعار جديد
  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.data,
    required this.timestamp,
    this.isRead = false,
    this.recipientId,
  });

  // إنشاء نسخة جديدة مع تغيير بعض الخصائص
  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    bool? isRead,
    String? recipientId,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      recipientId: recipientId ?? this.recipientId,
    );
  }

  /// تحويل النموذج إلى Map لتخزينه
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'data': data,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isRead': isRead,
      'recipientId': recipientId,
    };
  }

  /// إنشاء نموذج من Map
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      timestamp: DateTime.fromMillisecondsSinceEpoch(
          map['timestamp'] ?? DateTime.now().millisecondsSinceEpoch),
      isRead: map['isRead'] ?? false,
      recipientId: map['recipientId'],
    );
  }

  /// تحويل النموذج إلى JSON (للتوافق مع الكود القديم)
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء نموذج من JSON (للتوافق مع الكود القديم)
  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      NotificationModel.fromMap(json);

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, body: $body, data: $data, timestamp: $timestamp, isRead: $isRead, recipientId: $recipientId)';
  }
}
