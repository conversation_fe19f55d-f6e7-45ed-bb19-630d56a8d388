// نموذج بيانات أداء المندوبين
import 'package:mobile_pos/model/transition_model.dart';

/// نموذج بيانات أداء المندوبين
class SalesPerformanceModel {
  final String userId;
  final String userName;
  final String userEmail;
  final int totalSales;
  final double totalAmount;
  final int totalCustomers;
  final int totalProducts;
  final double averageOrderValue;
  final List<SalesTransitionModel> transactions;
  final Map<String, double> dailySales;
  final Map<String, int> productCategorySales;
  final DateTime periodStart;
  final DateTime periodEnd;

  SalesPerformanceModel({
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.totalSales,
    required this.totalAmount,
    required this.totalCustomers,
    required this.totalProducts,
    required this.averageOrderValue,
    required this.transactions,
    required this.dailySales,
    required this.productCategorySales,
    required this.periodStart,
    required this.periodEnd,
  });

  /// إنشاء نموذج من قائمة المعاملات
  factory SalesPerformanceModel.fromTransactions(
    String userId,
    String userName,
    String userEmail,
    List<SalesTransitionModel> transactions,
    DateTime periodStart,
    DateTime periodEnd,
  ) {
    // تصفية المعاملات حسب الفترة
    final filteredTransactions = transactions.where((transaction) {
      final transactionDate = DateTime.parse(transaction.purchaseDate);
      return (transactionDate.isAfter(periodStart) ||
              transactionDate.isAtSameMomentAs(periodStart)) &&
          (transactionDate.isBefore(periodEnd) ||
              transactionDate.isAtSameMomentAs(periodEnd));
    }).toList();

    // حساب إجمالي المبيعات
    final totalSales = filteredTransactions.length;

    // حساب إجمالي المبلغ
    final totalAmount = filteredTransactions.fold(0.0,
        (sum, transaction) => sum + (transaction.totalAmount?.toDouble() ?? 0));

    // حساب عدد العملاء الفريدين
    final uniqueCustomers = <String>{};
    for (var transaction in filteredTransactions) {
      if (transaction.customerPhone.isNotEmpty) {
        uniqueCustomers.add(transaction.customerPhone);
      }
    }
    final totalCustomers = uniqueCustomers.length;

    // حساب عدد المنتجات الفريدة
    final uniqueProducts = <String>{};
    for (var transaction in filteredTransactions) {
      if (transaction.productList != null) {
        for (var product in transaction.productList!) {
          uniqueProducts.add(product.productId.toString());
        }
      }
    }
    final totalProducts = uniqueProducts.length;

    // حساب متوسط قيمة الطلب
    final averageOrderValue = totalSales > 0 ? totalAmount / totalSales : 0;

    // حساب المبيعات اليومية
    final dailySales = <String, double>{};
    for (var transaction in filteredTransactions) {
      final date = DateTime.parse(transaction.purchaseDate);
      final dateString =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      if (!dailySales.containsKey(dateString)) {
        dailySales[dateString] = 0;
      }

      dailySales[dateString] =
          dailySales[dateString]! + (transaction.totalAmount?.toDouble() ?? 0);
    }

    // حساب المبيعات حسب فئة المنتج
    final productCategorySales = <String, int>{};
    for (var transaction in filteredTransactions) {
      if (transaction.productList != null) {
        for (var product in transaction.productList!) {
          final category = product.productBrandName?.toString() ?? 'غير محدد';

          if (!productCategorySales.containsKey(category)) {
            productCategorySales[category] = 0;
          }

          productCategorySales[category] =
              productCategorySales[category]! + product.quantity.toInt();
        }
      }
    }

    return SalesPerformanceModel(
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      totalSales: totalSales,
      totalAmount: totalAmount,
      totalCustomers: totalCustomers,
      totalProducts: totalProducts,
      averageOrderValue: averageOrderValue.toDouble(),
      transactions: filteredTransactions,
      dailySales: dailySales,
      productCategorySales: productCategorySales,
      periodStart: periodStart,
      periodEnd: periodEnd,
    );
  }

  /// حساب نسبة تحقيق الهدف
  double calculateTargetAchievement(SalesTargetModel target) {
    switch (target.targetType) {
      case TargetType.amount:
        return totalAmount / target.targetValue * 100;
      case TargetType.sales:
        return totalSales / target.targetValue * 100;
      case TargetType.customers:
        return totalCustomers / target.targetValue * 100;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'totalSales': totalSales,
      'totalAmount': totalAmount,
      'totalCustomers': totalCustomers,
      'totalProducts': totalProducts,
      'averageOrderValue': averageOrderValue,
      'transactions':
          transactions.map((transaction) => transaction.toJson()).toList(),
      'dailySales': dailySales,
      'productCategorySales': productCategorySales,
      'periodStart': periodStart.toString(),
      'periodEnd': periodEnd.toString(),
    };
  }
}

/// نموذج بيانات أهداف المبيعات
class SalesTargetModel {
  final String id;
  final String userId;
  final String userName;
  final TargetType targetType;
  final double targetValue;
  final DateTime startDate;
  final DateTime endDate;
  final String description;

  SalesTargetModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.targetType,
    required this.targetValue,
    required this.startDate,
    required this.endDate,
    required this.description,
  });

  factory SalesTargetModel.fromJson(Map<String, dynamic> json, String id) {
    return SalesTargetModel(
      id: id,
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      targetType: _parseTargetType(json['targetType']),
      targetValue: double.tryParse(json['targetValue'].toString()) ?? 0,
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toString()),
      endDate: DateTime.parse(json['endDate'] ?? DateTime.now().toString()),
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'targetType': targetType.toString().split('.').last,
      'targetValue': targetValue,
      'startDate': startDate.toString(),
      'endDate': endDate.toString(),
      'description': description,
    };
  }

  static TargetType _parseTargetType(String? type) {
    switch (type) {
      case 'amount':
        return TargetType.amount;
      case 'sales':
        return TargetType.sales;
      case 'customers':
        return TargetType.customers;
      default:
        return TargetType.amount;
    }
  }
}

/// أنواع أهداف المبيعات
enum TargetType {
  amount,
  sales,
  customers,
}
