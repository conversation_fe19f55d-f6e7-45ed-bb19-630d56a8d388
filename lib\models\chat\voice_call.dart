/// نموذج المكالمة الصوتية
class VoiceCall {
  String? objectId;
  String? callId;
  String? callerId;
  String? recipientId;
  String? status;
  DateTime? startTime;
  DateTime? endTime;
  int? duration;
  DateTime? createdAt;
  DateTime? updatedAt;

  // أسماء الحقول (للتوافق مع الكود القديم)
  static const String keyTableName = 'VoiceCall';
  static const String keyCallId = 'callId';
  static const String keyCaller = 'callerId';
  static const String keyRecipient = 'recipientId';
  static const String keyStatus = 'status';
  static const String keyStartTime = 'startTime';
  static const String keyEndTime = 'endTime';
  static const String keyDuration = 'duration';

  // حالات المكالمة
  static const String statusRinging = 'ringing';
  static const String statusAnswered = 'answered';
  static const String statusRejected = 'rejected';
  static const String statusMissed = 'missed';
  static const String statusEnded = 'ended';

  // المُنشئ
  VoiceCall({
    this.objectId,
    this.callId,
    this.callerId,
    this.recipientId,
    this.status,
    this.startTime,
    this.endTime,
    this.duration,
    this.createdAt,
    this.updatedAt,
  });

  // إنشاء نسخة من الكائن
  VoiceCall clone(Map<String, dynamic> map) {
    return VoiceCall.fromMap(map);
  }

  // دوال مساعدة للتوافق مع الكود القديم

  // الحصول على قيمة من الخريطة
  T? get<T>(String key) {
    switch (key) {
      case keyCallId:
        return callId as T?;
      case keyCaller:
        return callerId as T?;
      case keyRecipient:
        return recipientId as T?;
      case keyStatus:
        return status as T?;
      case keyStartTime:
        return startTime as T?;
      case keyEndTime:
        return endTime as T?;
      case keyDuration:
        return duration as T?;
      default:
        return null;
    }
  }

  // تعيين قيمة في الخريطة
  void set<T>(String key, T? value) {
    switch (key) {
      case keyCallId:
        callId = value as String?;
        break;
      case keyCaller:
        callerId = value as String?;
        break;
      case keyRecipient:
        recipientId = value as String?;
        break;
      case keyStatus:
        status = value as String?;
        break;
      case keyStartTime:
        startTime = value as DateTime?;
        break;
      case keyEndTime:
        endTime = value as DateTime?;
        break;
      case keyDuration:
        duration = value as int?;
        break;
      default:
        break;
    }
  }

  // تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'objectId': objectId,
      'callId': callId,
      'callerId': callerId,
      'recipientId': recipientId,
      'status': status,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // إنشاء نموذج من Map
  factory VoiceCall.fromMap(Map<String, dynamic> map) {
    return VoiceCall(
      objectId: map['objectId'],
      callId: map['callId'],
      callerId: map['callerId'],
      recipientId: map['recipientId'],
      status: map['status'],
      startTime:
          map['startTime'] != null ? DateTime.parse(map['startTime']) : null,
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime']) : null,
      duration: map['duration'],
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  // تحويل النموذج إلى JSON
  Map<String, dynamic> fromJson(Map<String, dynamic> map) {
    objectId = map['objectId'];
    callId = map['callId'];
    callerId = map['callerId'];
    recipientId = map['recipientId'];
    status = map['status'];
    startTime =
        map['startTime'] != null ? DateTime.parse(map['startTime']) : null;
    endTime = map['endTime'] != null ? DateTime.parse(map['endTime']) : null;
    duration = map['duration'];
    createdAt =
        map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null;
    updatedAt =
        map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null;

    return map;
  }

  // حفظ المكالمة (للتوافق مع الكود القديم)
  Future<ParseResponse> save() async {
    // هذه الدالة للتوافق فقط، لا تقوم بأي شيء
    return ParseResponse(
      success: true,
      results: [this],
      statusCode: 200,
      error: null,
    );
  }
}

// فئة ParseResponse للتوافق مع الكود القديم
class ParseResponse {
  final bool success;
  final List<dynamic>? results;
  final int statusCode;
  final ParseError? error;

  ParseResponse({
    required this.success,
    this.results,
    required this.statusCode,
    this.error,
  });
}

// فئة ParseError للتوافق مع الكود القديم
class ParseError {
  final String message;
  final int code;

  ParseError({
    required this.message,
    required this.code,
  });
}
