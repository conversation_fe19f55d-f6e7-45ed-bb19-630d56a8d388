import 'package:mobile_pos/models/app_update_model.dart';

/// واجهة مستودع تحديثات التطبيق
abstract class AppUpdateRepository {
  /// التحقق من وجود تحديثات جديدة
  Future<AppUpdateModel> checkForUpdates({bool force = false});

  /// الحصول على سجل التغييرات للإصدارات السابقة
  Future<List<ChangelogModel>> getChangelogHistory();

  /// تنزيل التحديث
  Future<Stream<DownloadStatus>> downloadUpdate(AppUpdateModel updateInfo);

  /// التحقق من وجود ملف تحديث محمل مسبقًا
  Future<String?> checkForDownloadedUpdate(String version);

  /// تثبيت التحديث
  Future<bool> installUpdate(String filePath);

  /// تأجيل التحديث
  Future<void> postponeUpdate(AppUpdateModel updateInfo, {Duration? duration});

  /// تجاهل التحديث
  Future<void> ignoreUpdate(AppUpdateModel updateInfo);

  /// الحصول على حالة التحديث التلقائي
  Future<bool> getAutoUpdateEnabled();

  /// تعيين حالة التحديث التلقائي
  Future<void> setAutoUpdateEnabled(bool enabled);
}
