class WhatsappMarketingSmsTemplateModel {
  final String? saleTemplate;
  final String? saleReturnTemplate;
  final String? quotationTemplate;
  final String? purchaseTemplate;
  final String? purchaseReturnTemplate;
  final String? dueTemplate;
  final String? paymentTemplate;
  final String? bulkSmsTemplate;

  WhatsappMarketingSmsTemplateModel({
    this.saleTemplate,
    this.saleReturnTemplate,
    this.quotationTemplate,
    this.purchaseTemplate,
    this.purchaseReturnTemplate,
    this.dueTemplate,
    this.paymentTemplate,
    this.bulkSmsTemplate,
  });

  factory WhatsappMarketingSmsTemplateModel.fromJson(Map<dynamic, dynamic> json) {
    return WhatsappMarketingSmsTemplateModel(
      saleTemplate: json['saleTemplate'],
      saleReturnTemplate: json['saleReturnTemplate'],
      quotationTemplate: json['quotationTemplate'],
      purchaseTemplate: json['purchaseTemplate'],
      purchaseReturnTemplate: json['purchaseReturnTemplate'],
      dueTemplate: json['dueTemplate'],
      paymentTemplate: json['paymentTemplate'],
      bulkSmsTemplate: json['bulkSmsTemplate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'saleTemplate': saleTemplate,
      'saleReturnTemplate': saleReturnTemplate,
      'quotationTemplate': quotationTemplate,
      'purchaseTemplate': purchaseTemplate,
      'purchaseReturnTemplate': purchaseReturnTemplate,
      'dueTemplate': dueTemplate,
      'paymentTemplate': paymentTemplate,
      'bulkSmsTemplate': bulkSmsTemplate,
    };
  }

  WhatsappMarketingSmsTemplateModel copyWith({
    String? saleTemplate,
    String? saleReturnTemplate,
    String? quotationTemplate,
    String? purchaseTemplate,
    String? purchaseReturnTemplate,
    String? dueTemplate,
    String? paymentTemplate,
    String? bulkSmsTemplate,
  }) {
    return WhatsappMarketingSmsTemplateModel(
      saleTemplate: saleTemplate ?? this.saleTemplate,
      saleReturnTemplate: saleReturnTemplate ?? this.saleReturnTemplate,
      quotationTemplate: quotationTemplate ?? this.quotationTemplate,
      purchaseTemplate: purchaseTemplate ?? this.purchaseTemplate,
      purchaseReturnTemplate: purchaseReturnTemplate ?? this.purchaseReturnTemplate,
      dueTemplate: dueTemplate ?? this.dueTemplate,
      paymentTemplate: paymentTemplate ?? this.paymentTemplate,
      bulkSmsTemplate: bulkSmsTemplate ?? this.bulkSmsTemplate,
    );
  }
}
