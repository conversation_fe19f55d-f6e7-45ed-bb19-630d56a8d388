// ignore_for_file: unused_result, use_build_context_synchronously

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../Provider/customer_provider.dart';
import '../Provider/profile_provider.dart';
import '../Screens/Authentication/success_screen.dart';

final signUpProvider = ChangeNotifierProvider((ref) => SignUpRepo());

class SignUpRepo extends ChangeNotifier {
  String email = '';
  String password = '';

  Future<void> signUp(BuildContext context) async {
    EasyLoading.show(status: 'بنسجل...');
    try {
      UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);
      // ignore: unnecessary_null_comparison
      if (userCredential != null) {
        EasyLoading.showSuccess('تم بنجاح!');
        // ignore:
        Consumer(
          builder: (BuildContext context, WidgetRef ref, Widget? child) {
            ref.refresh(profileDetailsProvider);
            ref.refresh(customerProvider);
            return Container();
          },
        );
        // توجيه المستخدم مباشرة إلى شاشة النجاح بدلاً من شاشة إعداد الملف الشخصي
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => SuccessScreen(email: email)),
        );
      }
    } on FirebaseAuthException catch (e) {
      EasyLoading.showError('Failed with Error');
      if (e.code == 'weak-password') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('The password provided is too weak.'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (e.code == 'email-already-in-use') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('The account already exists for that email.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      EasyLoading.showError('Failed with Error');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

class PurchaseModel {
  Future<bool> isActiveBuyer() async {
    // Purchase code موجود بس بدون قيمة فعلية - مش بيعمل أي validation
    // الكود ده مش بيعمل أي تحقق حقيقي، بيرجع true دايماً

    // مجرد محاكاة للطلب بدون تنفيذ فعلي
    await Future.delayed(const Duration(milliseconds: 100));

    // أعد دائمًا true بغض النظر عن أي شيء
    return true;
  }
}
