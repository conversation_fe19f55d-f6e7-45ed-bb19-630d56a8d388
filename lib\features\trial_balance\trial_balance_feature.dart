// بسم الله الرحمن الرحيم
// ميزة تقرير Trial Balance - ملف الميزة الرئيسي

import 'package:flutter/material.dart';
import '../core/feature_interface.dart';
import 'screens/trial_balance_screen.dart';

/// ميزة تقرير Trial Balance
class TrialBalanceFeature implements FeatureInterface {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من ميزة Trial Balance
  static final TrialBalanceFeature _instance = TrialBalanceFeature._internal();
  factory TrialBalanceFeature() => _instance;
  TrialBalanceFeature._internal();

  // حالة تفعيل الميزة
  bool _isEnabled = true;

  @override
  String get featureName => 'تقرير ميزان المراجعة';

  @override
  String get featureDescription => 'إنشاء وعرض تقارير ميزان المراجعة المحاسبية';

  @override
  IconData get featureIcon => Icons.balance;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Widget getMainScreen() {
    return const TrialBalanceScreen();
  }

  @override
  Map<String, WidgetBuilder> getRoutes() {
    return {
      '/trial-balance': (context) => const TrialBalanceScreen(),
      '/trial-balance/create': (context) => const TrialBalanceScreen(),
      '/trial-balance/reports': (context) => const TrialBalanceScreen(),
    };
  }

  @override
  Future<void> initialize() async {
    // تهيئة ميزة Trial Balance
    // يمكن إضافة أي إعدادات مطلوبة هنا
  }

  @override
  Future<void> dispose() async {
    // تنظيف الموارد عند إزالة الميزة
  }

  /// معلومات إضافية عن الميزة
  Map<String, dynamic> get metadata => {
        'author': 'AmrDevPOS Team',
        'created_date': '2024-01-15',
        'last_updated': '2024-01-15',
        'supported_languages': ['ar', 'en'],
        'min_app_version': '1.0.0',
        'feature_type': 'report',
        'data_sources': [
          'Sales Transition',
          'Purchase Transition',
          'Customers',
          'Suppliers',
          'Expense',
          'Treasury',
          'Products',
        ],
        'report_formats': ['table', 'summary'],
        'export_formats': ['pdf', 'excel'], // للمستقبل
      };

  /// الحصول على إعدادات الميزة
  Map<String, dynamic> getConfiguration() {
    return {
      'default_date_range': 'current_month',
      'auto_save_reports': true,
      'show_account_codes': true,
      'group_by_account_type': true,
      'decimal_places': 2,
      'currency_symbol': 'جنيه',
      'date_format': 'yyyy/MM/dd',
      'number_format': '#,##0.00',
    };
  }

  /// بناء ويدجت الوصول السريع
  Widget buildQuickAccessWidget(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const TrialBalanceScreen()),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(featureIcon, size: 32, color: const Color(0xFF2196F3)),
              const SizedBox(height: 8),
              Text(
                featureName,
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                'إنشاء تقرير ميزان المراجعة',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// نص المساعدة
  String getHelpText() {
    return '''
تقرير ميزان المراجعة (Trial Balance) هو تقرير محاسبي يعرض جميع الحسابات وأرصدتها المدينة والدائنة في فترة زمنية محددة.

الميزات:
• إنشاء تقرير ميزان المراجعة تلقائياً من البيانات المتاحة
• عرض الحسابات مجمعة حسب النوع (أصول، خصوم، إيرادات، مصروفات)
• التحقق من توازن التقرير (إجمالي المدين = إجمالي الدائن)
• حفظ التقارير للمراجعة اللاحقة
• اختيار فترات زمنية مختلفة
• عرض تفصيلي لكل حساب

كيفية الاستخدام:
1. اختر الفترة الزمنية المطلوبة
2. اضغط على "إنشاء التقرير"
3. راجع النتائج والتأكد من التوازن
4. احفظ التقرير إذا لزم الأمر

ملاحظة: يتم حساب الأرصدة تلقائياً من بيانات المبيعات والمشتريات والمصروفات والخزينة.
''';
  }
}
