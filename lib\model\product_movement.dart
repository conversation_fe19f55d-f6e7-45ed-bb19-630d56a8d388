// في ملف واحد محدد، مثلاً lib/model/product_movement.dart
class ProductMovement {
  final DateTime date;
  final String type; // 'purchase' or 'sale'
  final int quantity;
  final double price;
  final String reference;
  final String productCode;
  final String productName;

  ProductMovement({
    required this.date,
    required this.type,
    required this.quantity,
    required this.price,
    required this.reference,
    required this.productCode,
    required this.productName,
    required invoiceNumber,
  });
}
