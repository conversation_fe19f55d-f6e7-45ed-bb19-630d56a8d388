// بسم الله الرحمن الرحيم
// خدمة التصدير - مسؤولة عن تصدير التقارير بتنسيقات مختلفة

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';

import '../models/report_model.dart';

/// نوع تنسيق التصدير
enum ExportFormat {
  /// PDF
  pdf,

  /// Excel
  excel,

  /// CSV
  csv,

  /// JSON
  json,
}

/// خدمة التصدير
class ExportService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة التصدير
  static final ExportService _instance = ExportService._internal();
  factory ExportService() => _instance;
  ExportService._internal();

  /// تصدير التقرير
  Future<String?> exportReport({
    required ReportModel report,
    required ExportFormat format,
  }) async {
    try {
      switch (format) {
        case ExportFormat.pdf:
          return await _exportToPdf(report);
        case ExportFormat.excel:
          return await _exportToExcel(report);
        case ExportFormat.csv:
          return await _exportToCsv(report);
        case ExportFormat.json:
          return await _exportToJson(report);
      }
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }

  /// مشاركة التقرير
  Future<void> shareReport({
    required ReportModel report,
    required ExportFormat format,
  }) async {
    try {
      final filePath = await exportReport(
        report: report,
        format: format,
      );

      if (filePath != null) {
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'تقرير: ${report.title}',
        );
      }
    } catch (e) {
      debugPrint('خطأ في مشاركة التقرير: $e');
    }
  }

  /// تصدير التقرير إلى PDF
  Future<String?> _exportToPdf(ReportModel report) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // إضافة صفحة العنوان
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'تقرير: ${report.title}',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd HH:mm').format(report.createdAt)}',
                    style: const pw.TextStyle(fontSize: 16),
                  ),
                  if (report.description != null) ...[
                    pw.SizedBox(height: 20),
                    pw.Text(
                      'الوصف: ${report.description}',
                      style: const pw.TextStyle(fontSize: 14),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      );

      // إضافة صفحة البيانات
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'بيانات التقرير',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 20),
                // جدول البيانات
                pw.Table(
                  border: pw.TableBorder.all(),
                  children: [
                    // رأس الجدول
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey300,
                      ),
                      children: _getPdfTableHeaders(report),
                    ),
                    // صفوف الجدول
                    ..._getPdfTableRows(report),
                  ],
                ),
              ],
            );
          },
        ),
      );

      // حفظ الملف
      final output = await getTemporaryDirectory();
      final file = File(
          '${output.path}/report_${report.id}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf');
      await file.writeAsBytes(await pdf.save());

      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى PDF: $e');
      return null;
    }
  }

  /// الحصول على رؤوس جدول PDF
  List<pw.Widget> _getPdfTableHeaders(ReportModel report) {
    if (report.tableData.isEmpty) {
      return [pw.Text('لا توجد بيانات')];
    }

    final headers = report.tableData.first.keys.toList();
    return headers
        .map((header) => pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                header,
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ))
        .toList();
  }

  /// الحصول على صفوف جدول PDF
  List<pw.TableRow> _getPdfTableRows(ReportModel report) {
    if (report.tableData.isEmpty) {
      return [];
    }

    final headers = report.tableData.first.keys.toList();
    return report.tableData.map((row) {
      return pw.TableRow(
        children: headers
            .map((header) => pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(row[header].toString()),
                ))
            .toList(),
      );
    }).toList();
  }

  /// تصدير التقرير إلى Excel
  Future<String?> _exportToExcel(ReportModel report) async {
    try {
      // في الإصدار الحقيقي، يجب استخدام مكتبة Excel
      // هنا نستخدم تنسيق CSV كبديل
      return _exportToCsv(report);
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى Excel: $e');
      return null;
    }
  }

  /// تصدير التقرير إلى CSV
  Future<String?> _exportToCsv(ReportModel report) async {
    try {
      if (report.tableData.isEmpty) {
        return null;
      }

      final headers = report.tableData.first.keys.toList();
      final rows = report.tableData.map((row) {
        return headers.map((header) => row[header].toString()).join(',');
      }).join('\n');

      final csv = '${headers.join(',')}\n$rows';

      // حفظ الملف
      final output = await getTemporaryDirectory();
      final file = File(
          '${output.path}/report_${report.id}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.csv');
      await file.writeAsString(csv);

      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى CSV: $e');
      return null;
    }
  }

  /// تصدير التقرير إلى JSON
  Future<String?> _exportToJson(ReportModel report) async {
    try {
      final json = report.toJson();

      // حفظ الملف
      final output = await getTemporaryDirectory();
      final file = File(
          '${output.path}/report_${report.id}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.json');
      await file.writeAsString(json);

      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى JSON: $e');
      return null;
    }
  }
}

/// مزود خدمة التصدير
final exportServiceProvider = Provider<ExportService>((ref) {
  return ExportService();
});
