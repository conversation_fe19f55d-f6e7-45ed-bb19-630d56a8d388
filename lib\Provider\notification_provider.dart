import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/services/notification_service.dart';

/// مزود خدمة الإشعارات
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

/// مزود رمز الجهاز
final deviceTokenProvider = FutureProvider<String?>((ref) async {
  final notificationService = ref.watch(notificationServiceProvider);
  return await notificationService.getDeviceToken();
});

/// مزود حالة الاشتراك في موضوع
final topicSubscriptionProvider = StateProvider.family<bool, String>((ref, topic) => false);

/// مزود لإرسال الإشعارات
final notificationSenderProvider = Provider<NotificationSender>((ref) {
  return NotificationSender(ref);
});

/// فئة مساعدة لإرسال الإشعارات
class NotificationSender {
  final Ref _ref;
  
  NotificationSender(this._ref);
  
  /// إرسال إشعار إلى مستخدم
  Future<bool> sendToUser({
    required String userToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    final notificationService = _ref.read(notificationServiceProvider);
    return await notificationService.sendNotificationToUser(
      userToken: userToken,
      title: title,
      body: body,
      data: data,
    );
  }
  
  /// إرسال إشعار إلى موضوع
  Future<bool> sendToTopic({
    required String topic,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    final notificationService = _ref.read(notificationServiceProvider);
    return await notificationService.sendNotificationToTopic(
      topic: topic,
      title: title,
      body: body,
      data: data,
    );
  }
  
  /// الاشتراك في موضوع
  Future<void> subscribeTopic(String topic) async {
    final notificationService = _ref.read(notificationServiceProvider);
    await notificationService.subscribeToTopic(topic);
    _ref.read(topicSubscriptionProvider(topic).notifier).state = true;
  }
  
  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeTopic(String topic) async {
    final notificationService = _ref.read(notificationServiceProvider);
    await notificationService.unsubscribeFromTopic(topic);
    _ref.read(topicSubscriptionProvider(topic).notifier).state = false;
  }
}
