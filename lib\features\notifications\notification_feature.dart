// بسم الله الرحمن الرحيم
// ميزة الإشعارات - تنفيذ واجهة الميزة للإشعارات

import 'package:flutter/material.dart';
import '../core/feature_interface.dart';
import 'screens/notifications_screen.dart';
import 'services/notification_service.dart';

/// ميزة الإشعارات
class NotificationFeature implements FeatureInterface {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من ميزة الإشعارات
  static final NotificationFeature _instance = NotificationFeature._internal();
  factory NotificationFeature() => _instance;
  NotificationFeature._internal();

  // حالة تفعيل الميزة
  bool _isEnabled = true;

  @override
  String get featureName => 'الإشعارات';

  @override
  String get featureDescription => 'إدارة وعرض الإشعارات والتنبيهات';

  @override
  IconData get featureIcon => Icons.notifications;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Widget getMainScreen() {
    return const NotificationsScreen();
  }

  @override
  Map<String, WidgetBuilder> getRoutes() {
    return {
      NotificationsScreen.routeName: (context) => const NotificationsScreen(),
    };
  }

  @override
  Future<void> initialize() async {
    // تهيئة خدمة الإشعارات
    await NotificationService().initialize();
  }

  @override
  Future<void> dispose() async {
    // إيقاف خدمة الإشعارات
    await NotificationService().dispose();
  }
}
