// بسم الله الرحمن الرحيم
// ملف السمات - يحتوي على تعريفات الألوان والسمات المستخدمة في التطبيق

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';

/// ألوان التطبيق الرئيسية
class AppColors {
  // الألوان الرئيسية - استخدام نفس ألوان التطبيق الرئيسي
  static const Color mainColor = kMainColor; // لون نبيتي غامق
  static const Color secondaryMainColor = kMainColor;
  static const Color greyTextColor = kGreyTextColor;
  static const Color borderColorTextField = kBorderColorTextField;
  static const Color borderColor = kBorderColor;
  static const Color darkWhite = kDarkWhite;
  static const Color titleColor = kTitleColor;
  static const Color white = kWhite;
  static const Color alertColor = kAlertColor;
  static const Color premiumPlanColor = kPremiumPlanColor;
  static const Color premiumPlanColor2 = kPremiumPlanColor2;
  static const Color maroonAccent = kMaroonAccent; // نبيتي أكسنت
  static const Color lightGreyColor = Color(0xFFEEEEEE); // رمادي فاتح

  // ألوان إضافية للميزات الجديدة
  static const Color chatBubbleUser = kMainColor;
  static const Color chatBubbleOther = Color(0xFFEEEEEE);
  static const Color notificationBackground = Color(0xFFF5F5F5);
  static const Color notificationUnread = Color(0xFFE8F4FF);
  static const Color profileBackground = Color(0xFFF9F9F9);
}

/// سمات التطبيق
class AppTheme {
  // زخرفة الأزرار - استخدام نفس زخرفة التطبيق الرئيسي
  static const buttonDecoration = kButtonDecoration;

  // زخرفة حقول الإدخال
  static const inputDecoration = InputDecoration(
    hintStyle: TextStyle(color: AppColors.borderColorTextField),
    filled: true,
    fillColor: Colors.white70,
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(8.0)),
      borderSide: BorderSide(color: AppColors.borderColorTextField, width: 2),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(6.0)),
      borderSide: BorderSide(color: AppColors.borderColorTextField, width: 2),
    ),
  );

  // نمط النص الرئيسي
  static TextStyle get textStyle => kTextStyle;

  // سمة التطبيق الفاتحة - تستخدم نفس سمات التطبيق الرئيسي
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.mainColor,
      scaffoldBackgroundColor: Colors.white,
      fontFamily: 'Cairo',
      textTheme: TextTheme(
        displayLarge: GoogleFonts.cairo(
          color: AppColors.titleColor,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: GoogleFonts.cairo(
          color: AppColors.titleColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: GoogleFonts.cairo(
          color: AppColors.titleColor,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        bodyLarge: GoogleFonts.cairo(
          color: AppColors.titleColor,
          fontSize: 16,
        ),
        bodyMedium: GoogleFonts.cairo(
          color: AppColors.titleColor,
          fontSize: 14,
        ),
        bodySmall: GoogleFonts.cairo(
          color: AppColors.greyTextColor,
          fontSize: 12,
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.mainColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.mainColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
      inputDecorationTheme: const InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        hintStyle: TextStyle(color: AppColors.greyTextColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
          borderSide: BorderSide(color: AppColors.borderColorTextField),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
          borderSide:
              BorderSide(color: AppColors.borderColorTextField, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
          borderSide: BorderSide(color: AppColors.mainColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
          borderSide: BorderSide(color: Colors.red, width: 1),
        ),
      ),
      colorScheme: ColorScheme.fromSwatch().copyWith(
        primary: AppColors.mainColor,
        secondary: AppColors.secondaryMainColor,
      ),
    );
  }
}
