// بسم الله الرحمن الرحيم
// نموذج المنتج - يمثل بيانات المنتج

import 'dart:convert';

/// حالة المنتج
enum ProductStatus {
  /// متوفر
  available,

  /// منخفض المخزون
  lowStock,

  /// نفذ من المخزون
  outOfStock,

  /// غير نشط
  inactive,
}

/// نموذج المنتج
class ProductModel {
  /// ينشئ نموذج المنتج
  const ProductModel({
    required this.id,
    required this.name,
    required this.barcode,
    required this.categoryId,
    required this.price,
    required this.cost,
    required this.quantity,
    required this.createdAt,
    this.description,
    this.imagePath,
    this.unit = 'قطعة',
    this.minQuantity = 5,
    this.maxQuantity,
    this.discount = 0,
    this.tax = 0,
    this.isActive = true,
    this.supplierId,
    this.locationId,
    this.attributes = const {},
    this.updatedAt,
  });

  /// معرف المنتج
  final String id;

  /// اسم المنتج
  final String name;

  /// الباركود
  final String barcode;

  /// معرف الفئة
  final String categoryId;

  /// سعر البيع
  final double price;

  /// تكلفة الشراء
  final double cost;

  /// الكمية المتوفرة
  final int quantity;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// وصف المنتج (اختياري)
  final String? description;

  /// مسار الصورة (اختياري)
  final String? imagePath;

  /// وحدة القياس
  final String unit;

  /// الحد الأدنى للكمية
  final int minQuantity;

  /// الحد الأقصى للكمية (اختياري)
  final int? maxQuantity;

  /// نسبة الخصم
  final double discount;

  /// نسبة الضريبة
  final double tax;

  /// هل المنتج نشط؟
  final bool isActive;

  /// معرف المورد (اختياري)
  final String? supplierId;

  /// معرف الموقع (اختياري)
  final String? locationId;

  /// سمات المنتج
  final Map<String, dynamic> attributes;

  /// تاريخ التحديث (اختياري)
  final DateTime? updatedAt;

  /// الحصول على حالة المنتج
  ProductStatus get status {
    if (!isActive) {
      return ProductStatus.inactive;
    }
    if (quantity <= 0) {
      return ProductStatus.outOfStock;
    }
    if (quantity <= minQuantity) {
      return ProductStatus.lowStock;
    }
    return ProductStatus.available;
  }

  /// الحصول على سعر البيع بعد الخصم
  double get sellingPrice {
    if (discount <= 0) {
      return price;
    }
    return price - (price * discount / 100);
  }

  /// الحصول على قيمة الضريبة
  double get taxAmount {
    return sellingPrice * tax / 100;
  }

  /// الحصول على السعر النهائي (بعد الخصم وإضافة الضريبة)
  double get finalPrice {
    return sellingPrice + taxAmount;
  }

  /// الحصول على هامش الربح
  double get profitMargin {
    if (cost <= 0) {
      return 0;
    }
    return ((sellingPrice - cost) / cost) * 100;
  }

  /// الحصول على قيمة المخزون
  double get stockValue {
    return quantity * cost;
  }

  /// نسخ النموذج مع تحديث بعض الحقول
  ProductModel copyWith({
    String? name,
    String? barcode,
    String? categoryId,
    double? price,
    double? cost,
    int? quantity,
    String? description,
    String? imagePath,
    String? unit,
    int? minQuantity,
    int? maxQuantity,
    double? discount,
    double? tax,
    bool? isActive,
    String? supplierId,
    String? locationId,
    Map<String, dynamic>? attributes,
    DateTime? updatedAt,
  }) {
    return ProductModel(
      id: id,
      name: name ?? this.name,
      barcode: barcode ?? this.barcode,
      categoryId: categoryId ?? this.categoryId,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      quantity: quantity ?? this.quantity,
      createdAt: createdAt,
      description: description ?? this.description,
      imagePath: imagePath ?? this.imagePath,
      unit: unit ?? this.unit,
      minQuantity: minQuantity ?? this.minQuantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      discount: discount ?? this.discount,
      tax: tax ?? this.tax,
      isActive: isActive ?? this.isActive,
      supplierId: supplierId ?? this.supplierId,
      locationId: locationId ?? this.locationId,
      attributes: attributes ?? this.attributes,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'barcode': barcode,
      'categoryId': categoryId,
      'price': price,
      'cost': cost,
      'quantity': quantity,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'description': description,
      'imagePath': imagePath,
      'unit': unit,
      'minQuantity': minQuantity,
      'maxQuantity': maxQuantity,
      'discount': discount,
      'tax': tax,
      'isActive': isActive,
      'supplierId': supplierId,
      'locationId': locationId,
      'attributes': attributes,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  /// إنشاء نموذج من Map
  factory ProductModel.fromMap(Map<String, dynamic> map) {
    return ProductModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      barcode: map['barcode'] ?? '',
      categoryId: map['categoryId'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      cost: (map['cost'] ?? 0).toDouble(),
      quantity: map['quantity'] ?? 0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      description: map['description'],
      imagePath: map['imagePath'],
      unit: map['unit'] ?? 'قطعة',
      minQuantity: map['minQuantity'] ?? 5,
      maxQuantity: map['maxQuantity'],
      discount: (map['discount'] ?? 0).toDouble(),
      tax: (map['tax'] ?? 0).toDouble(),
      isActive: map['isActive'] ?? true,
      supplierId: map['supplierId'],
      locationId: map['locationId'],
      attributes: Map<String, dynamic>.from(map['attributes'] ?? {}),
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory ProductModel.fromJson(String source) =>
      ProductModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, barcode: $barcode, quantity: $quantity)';
  }
}
