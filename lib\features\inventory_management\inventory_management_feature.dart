// بسم الله الرحمن الرحيم
// ميزة إدارة المخزون - تنفيذ واجهة الميزة لإدارة المخزون

import 'package:flutter/material.dart';
import '../core/feature_interface.dart';
import 'screens/inventory_home_screen.dart';
import 'screens/product_list_screen.dart';

import 'screens/stock_movement_screen.dart';
import 'screens/barcode_scanner_screen.dart';
import 'services/inventory_service.dart';
import 'services/barcode_service.dart';
import 'services/stock_alert_service.dart';

/// ميزة إدارة المخزون
class InventoryManagementFeature implements FeatureInterface {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من ميزة إدارة المخزون
  static final InventoryManagementFeature _instance =
      InventoryManagementFeature._internal();
  factory InventoryManagementFeature() => _instance;
  InventoryManagementFeature._internal();

  // حالة تفعيل الميزة
  bool _isEnabled = true;

  // خدمات الميزة
  final InventoryService _inventoryService = InventoryService();
  final BarcodeService _barcodeService = BarcodeService();
  final StockAlertService _stockAlertService = StockAlertService();

  @override
  String get featureName => 'إدارة المخزون';

  @override
  String get featureDescription => 'إدارة المنتجات والمخزون وحركات المخزون';

  @override
  IconData get featureIcon => Icons.inventory;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Widget getMainScreen() {
    return const InventoryHomeScreen();
  }

  @override
  Map<String, WidgetBuilder> getRoutes() {
    return {
      InventoryHomeScreen.routeName: (context) => const InventoryHomeScreen(),
      '/inventory/products': (context) => const ProductListScreen(),
      '/inventory/barcode': (context) => const BarcodeScannerScreen(),
      '/inventory/stock-movements': (context) => const StockMovementScreen(),
    };
  }

  @override
  Future<void> initialize() async {
    // تهيئة خدمات إدارة المخزون
    await _stockAlertService.initialize();
  }

  @override
  Future<void> dispose() async {
    // تنظيف موارد إدارة المخزون
    _stockAlertService.dispose();
    _barcodeService.dispose();
  }

  /// الحصول على خدمة المخزون
  InventoryService get inventoryService => _inventoryService;

  /// الحصول على خدمة الباركود
  BarcodeService get barcodeService => _barcodeService;

  /// الحصول على خدمة تنبيهات المخزون
  StockAlertService get stockAlertService => _stockAlertService;
}
