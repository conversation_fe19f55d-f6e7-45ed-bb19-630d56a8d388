// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:mobile_pos/GlobalComponents/button_global.dart';
// import 'package:mobile_pos/generated/l10n.dart' as lang;
// import 'package:nb_utils/nb_utils.dart';
// import '../../Provider/user_role_provider.dart';
// import '../../constant.dart';
// import '../../currency.dart';
// import '../Home/home.dart';

// class SuccessScreen extends StatelessWidget {
//   SuccessScreen({super.key, this.email});
//   final String? email;
//   final CurrentUserData currentUserData = CurrentUserData();

// ignore_for_file: avoid_print, use_build_context_synchronously

//   @override
//   Widget build(BuildContext context) {
//     return PopScope(
//       // onWillPop: () async => false,
//       canPop: false,
//       child: Consumer(builder: (context, ref, _) {
//         final userRoleData = ref.watch(allUserRoleProvider);
//         return userRoleData.when(data: (data) {
//           if (email == 'phone') {
//             print('-----user id---------${FirebaseAuth.instance.currentUser!.uid}------------');
//             currentUserData.putUserData(userId: FirebaseAuth.instance.currentUser!.uid, subUser: false, title: '', email: '');
//           } else {
//             bool isNotFound = true;
//             for (var element in data) {
//               if (element.email == email) {
//                 isNotFound = false;
//                 currentUserData.putUserData(userId: element.databaseId, subUser: true, title: element.userTitle, email: element.email);
//                 subUserTitle = element.userTitle;
//               }
//               print('-------------sub user id-------------$subUserTitle---------------');
//             }
//             if (isNotFound) {
//               currentUserData.putUserData(userId: FirebaseAuth.instance.currentUser!.uid, subUser: false, title: '', email: '');
//             }
//           }
//           return Scaffold(
//             resizeToAvoidBottomInset: true,
//             body: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 const Image(image: AssetImage('assets/images/success.png')),
//                 const SizedBox(height: 40.0),
//                 Text(
//                   lang.S.of(context).congratulations,
//                   style: GoogleFonts.poppins(
//                     fontSize: 25.0,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.black,
//                   ),
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.all(20.0),
//                   child: Text(
//                     lang.S.of(context).youHaveSuccefulyLogin,
//                     maxLines: 2,
//                     overflow: TextOverflow.ellipsis,
//                     textAlign: TextAlign.center,
//                     style: GoogleFonts.poppins(
//                       color: kGreyTextColor,
//                       fontSize: 20.0,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             bottomNavigationBar: ButtonGlobal(
//                 buttontext: lang.S.of(context).continu,
//                 buttonDecoration: kButtonDecoration.copyWith(color: kMainColor, borderRadius: const BorderRadius.all(Radius.circular(30))),
//                 onPressed: () async {
//                   await Future.delayed(const Duration(seconds: 1)).then((value) => const Home().launch(context));
//                   // Navigator.pushNamed(context, '/home');
//                 },
//                 iconWidget: null,
//                 iconColor: Colors.white),
//           );
//         }, error: (e, stack) {
//           print('---------stack-------$stack---------------------');
//           print('---------e-------$e---------------------');
//           return Text(e.toString());
//         }, loading: () {
//           return const Center(child: CircularProgressIndicator());
//         });
//       }),
//     );
//   }
// }
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/Provider/user_role_provider.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/subscription_model.dart';
import 'package:mobile_pos/model/user_role_model.dart';
import 'package:mobile_pos/repository/subscription_repo.dart';
import 'package:mobile_pos/subscription.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../constant.dart';
import '../../currency.dart';
import '../Home/home.dart';
import '../subscription/package_screen.dart';

class SuccessScreen extends StatefulWidget {
  const SuccessScreen({super.key, this.email});
  final String? email;

  @override
  State<SuccessScreen> createState() => _SuccessScreenState();
}

class _SuccessScreenState extends State<SuccessScreen> {
  final CurrentUserData currentUserData = CurrentUserData();
  bool isLoading = true;
  String? companyName;

  @override
  void initState() {
    super.initState();
    _initializeUserData();
  }

  Future<void> _initializeUserData() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      final userId = currentUser?.uid ?? '';

      debugPrint('بدء تهيئة بيانات المستخدم للمستخدم: $userId');

      // تحميل بيانات المستخدم من Firebase
      await _loadUserDataFromFirebase(userId);

      // تحميل اسم الشركة
      companyName = await _getUserCompanyName(userId);
      debugPrint('تم تحميل اسم الشركة: $companyName');

      // التحقق من وجود بيانات الاشتراك وإعدادها إذا لم تكن موجودة
      await _ensureSubscriptionData(userId);

      // تحميل بيانات الاشتراك
      await Subscription.getUserLimitsData(
          context: context, wannaShowMsg: false);

      debugPrint('تم تحميل بيانات الاشتراك: ${Subscription.selectedItem}');

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة بيانات المستخدم: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // التأكد من وجود بيانات الاشتراك
  Future<void> _ensureSubscriptionData(String userId) async {
    try {
      // التحقق من وجود بيانات الاشتراك
      final subscriptionRef =
          FirebaseDatabase.instance.ref('$userId/Subscription');
      final snapshot = await subscriptionRef.get();

      if (!snapshot.exists) {
        debugPrint('لم يتم العثور على بيانات الاشتراك، إعداد الاشتراك المجاني');

        // إعداد الاشتراك المجاني
        await setupFreeSubscription();
      } else {
        // تحميل بيانات الاشتراك الموجودة
        final subscriptionModel = await SubscriptionRepo.getSubscriptionData();

        // تعيين اسم الاشتراك في Subscription مع التصحيح
        if (subscriptionModel.subscriptionName.isNotEmpty) {
          String correctedSubscriptionName = subscriptionModel.subscriptionName;
          if (correctedSubscriptionName == 'Free') {
            correctedSubscriptionName = 'Admin';
            debugPrint(
                'تم تصحيح اسم الاشتراك من Free إلى Admin في success_screen');
          }
          Subscription.selectedItem = correctedSubscriptionName;
          debugPrint('تم تحميل بيانات الاشتراك: $correctedSubscriptionName');
        } else {
          debugPrint('اسم الاشتراك فارغ، استخدام القيمة الافتراضية');
          Subscription.selectedItem = 'Admin';
        }
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من بيانات الاشتراك: $e');
      // استخدام قيمة افتراضية في حالة حدوث خطأ
      Subscription.selectedItem = 'Admin';
    }
  }

  // تحميل بيانات المستخدم من Firebase
  Future<void> _loadUserDataFromFirebase(String userId) async {
    try {
      final userRef =
          FirebaseDatabase.instance.ref('$userId/Personal Information');
      final snapshot = await userRef.get();

      if (snapshot.exists) {
        debugPrint('تم العثور على بيانات المستخدم في Firebase');
      } else {
        debugPrint('لم يتم العثور على بيانات المستخدم في Firebase');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المستخدم من Firebase: $e');
    }
  }

  // الحصول على اسم الشركة من Firebase
  Future<String?> _getUserCompanyName(String userId) async {
    try {
      final userRef =
          FirebaseDatabase.instance.ref('$userId/Personal Information');
      final snapshot = await userRef.get();

      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>?;
        final name = data?['companyName'] as String?;

        if (name != null && name.isNotEmpty) {
          debugPrint('تم العثور على اسم الشركة: $name');
          return name;
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم الشركة: $e');
      return null;
    }
  }

  // معالجة بيانات المستخدم
  Future<void> _processUserData(
      WidgetRef ref, AsyncValue<List<UserRoleModel>> userRoleData) async {
    try {
      // التحقق من حالة البيانات
      if (userRoleData is AsyncData<List<UserRoleModel>>) {
        final data = userRoleData.value;
        final currentUser = FirebaseAuth.instance.currentUser;
        final userId = currentUser?.uid ?? '';

        if (widget.email == 'phone') {
          debugPrint('-----user id---------$userId------------');

          // تحميل بيانات المستخدم من Firebase
          await _loadUserDataFromFirebase(userId);

          currentUserData.putUserData(
              userId: userId,
              subUser: false,
              title: await _getUserCompanyName(userId) ?? '',
              email: currentUser?.email ?? '');
        } else {
          bool isNotFound = true;

          // تجنب معالجة بيانات User Role إذا كانت فارغة أو تسبب مشاكل صلاحيات
          if (data.isNotEmpty) {
            for (var element in data) {
              if (element.email == widget.email) {
                isNotFound = false;

                debugPrint('✅ تم العثور على المستخدم الفرعي: ${element.email}');
                debugPrint('🔍 صلاحية المبيعات: ${element.salePermission}');
                debugPrint('🔍 صلاحية الأطراف: ${element.partiesPermission}');
                debugPrint('🔍 صلاحية الدردشة: ${element.aiChatPermission}');

                // حفظ بيانات الصلاحيات في SharedPreferences
                final prefs = await SharedPreferences.getInstance();
                await prefs.setString(
                    'userPermission', json.encode(element.toJson()));
                await prefs.setString('subUserEmail', widget.email ?? '');
                await prefs.setBool('isSubUser', true);
                await prefs.setString('userId', element.databaseId);
                await prefs.setString('subUserTitle', element.userTitle);

                debugPrint('💾 تم حفظ بيانات الصلاحيات في SharedPreferences');

                // تحديث المتغيرات العامة
                constUserId = element.databaseId;
                constSubUserTitle = element.userTitle;
                isSubUser = true;
                subUserEmail = widget.email ?? '';
                finalUserRoleModel = element;

                // تعيين بيانات المستخدم الفرعي
                currentUserData.putUserData(
                    userId: element.databaseId,
                    subUser: true,
                    title: element.userTitle,
                    email: element.email);
                // تعيين عنوان المستخدم الفرعي
                subUserTitle = element.userTitle;
                // إعادة تعيين حالة الحذف إلى false لأن المستخدم موجود
                isSubUserDeleted = false;
                break;
              }
              debugPrint(
                  '-------------sub user id-------------$subUserTitle---------------');
            }
          }

          if (isNotFound) {
            // تحميل بيانات المستخدم من Firebase
            await _loadUserDataFromFirebase(userId);

            currentUserData.putUserData(
                userId: userId,
                subUser: false,
                title: await _getUserCompanyName(userId) ?? '',
                email: currentUser?.email ?? '');
          }
        }

        // تحميل بيانات الاشتراك مع معالجة الأخطاء
        try {
          await Subscription.getUserLimitsData(
              context: context,
              wannaShowMsg: false); // تغيير إلى false لتجنب رسائل الخطأ
        } catch (e) {
          debugPrint('خطأ في تحميل بيانات الاشتراك: $e');
          // استخدام قيمة افتراضية في حالة الخطأ
          Subscription.selectedItem = 'Admin';
        }
      }
    } catch (e) {
      debugPrint('خطأ في معالجة بيانات المستخدم: $e');
      // في حالة حدوث خطأ، استخدم بيانات افتراضية
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        currentUserData.putUserData(
            userId: currentUser.uid,
            subUser: false,
            title: 'مستخدم',
            email: currentUser.email ?? '');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Consumer(builder: (context, ref, _) {
        final userRoleData = ref.watch(allUserRoleProvider);

        // معالجة حالات userRoleData
        return userRoleData.when(
          data: (data) {
            // إذا كانت البيانات جاهزة ولكن لا تزال في حالة التحميل
            if (isLoading) {
              return const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              );
            }

            // معالجة البيانات وتحديث الواجهة
            _processUserData(ref, userRoleData);
            return Scaffold(
              resizeToAvoidBottomInset: true,
              body: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Image(image: AssetImage('assets/images/success.png')),
                  const SizedBox(height: 40.0),
                  Text(
                    lang.S.of(context).congratulations,
                    style: GoogleFonts.poppins(
                      fontSize: 25.0,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Text(
                      lang.S.of(context).youHaveSuccefulyLogin,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        color: kGreyTextColor,
                        fontSize: 20.0,
                      ),
                    ),
                  ),
                ],
              ),
              bottomNavigationBar: ButtonGlobal(
                buttontext: lang.S.of(context).continu,
                buttonDecoration: kButtonDecoration.copyWith(
                    color: kMainColor,
                    borderRadius: const BorderRadius.all(Radius.circular(30))),
                onPressed: () async {
                  try {
                    // عرض مؤشر التحميل
                    EasyLoading.show(
                        status: 'جاري التحقق من بيانات الاشتراك...');

                    // تحميل بيانات المستخدم أولاً
                    await currentUserData.updateData();

                    // التحقق من صحة بيانات الاشتراك باستخدام نفس منطق package_screen
                    final subscriptionValid =
                        await _checkRealSubscriptionData();

                    if (!subscriptionValid) {
                      EasyLoading.dismiss();
                      if (!mounted) return;

                      // عرض رسالة خطأ
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'لا يمكن الوصول للنظام - لا توجد بيانات اشتراك صالحة'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 5),
                        ),
                      );

                      // العودة لشاشة تسجيل الدخول
                      Future.delayed(const Duration(seconds: 2), () {
                        if (mounted) {
                          Navigator.of(context).pushReplacementNamed('/login');
                        }
                      });
                      return;
                    }

                    // التحقق من وجود اشتراك صالح
                    final currentSubscription = Subscription.selectedItem;
                    if (currentSubscription == null ||
                        currentSubscription.isEmpty) {
                      EasyLoading.dismiss();
                      if (!mounted) return;

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('لا يمكن الوصول للنظام - اشتراك غير صالح'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 5),
                        ),
                      );
                      return;
                    }

                    // تحميل بيانات الاشتراك في الخلفية قبل الانتقال
                    EasyLoading.show(status: 'جاري تحديث بيانات الاشتراك...');
                    await _loadSubscriptionDataInBackground();

                    EasyLoading.dismiss();

                    // التأكد من أن السياق ما زال صالحاً
                    if (!mounted) return;

                    debugPrint(
                        '✅ تم التحقق من صحة الاشتراك: $currentSubscription');
                    debugPrint('✅ تم تحميل بيانات الاشتراك في الخلفية');
                    debugPrint('✅ الانتقال للشاشة الرئيسية...');

                    // الانتقال للشاشة الرئيسية
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(builder: (context) => const Home()),
                    );
                  } catch (e) {
                    EasyLoading.dismiss();
                    debugPrint('❌ خطأ عند الانتقال للشاشة الرئيسية: $e');

                    if (!mounted) return;

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في الوصول للنظام: $e'),
                        backgroundColor: Colors.red,
                        duration: const Duration(seconds: 5),
                      ),
                    );
                  }
                },
                iconWidget: null,
                iconColor: Colors.white,
              ),
            );
          },
          error: (e, stack) {
            print('---------stack-------$stack---------------------');
            print('---------e-------$e---------------------');
            return Text(e.toString());
          },
          loading: () {
            return const Center(child: CircularProgressIndicator());
          },
        );
      }),
    );
  }

  /// دالة للتحقق من بيانات الاشتراك الحقيقية (نفس منطق package_screen)
  Future<bool> _checkRealSubscriptionData() async {
    try {
      debugPrint(
          '🔄 التحقق من بيانات الاشتراك باستخدام منطق package_screen...');

      // الحصول على معرف المستخدم
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // إذا كان مستخدم فرعي، استخدم بيانات الاشتراك من الـ Admin
      String subscriptionUserId = userId;
      if (isSubUser) {
        subscriptionUserId = finalUserRoleModel.databaseId;
        debugPrint(
            '🔄 مستخدم فرعي - استخدام اشتراك الـ Admin: $subscriptionUserId');
      }

      // الحصول على بيانات الاشتراك من Firebase (نفس منطق package_screen)
      DatabaseReference ref =
          FirebaseDatabase.instance.ref('$subscriptionUserId/Subscription');
      final model = await ref.get();

      if (!model.exists || model.value == null) {
        debugPrint('❌ لا توجد بيانات اشتراك في Firebase');
        return false;
      }

      var data = jsonDecode(jsonEncode(model.value));
      final finalModel = SubscriptionModel.fromJson(data);

      // تصحيح اسم الاشتراك إذا كان Free إلى Admin
      String correctedSubscriptionName = finalModel.subscriptionName;
      if (correctedSubscriptionName == 'Free') {
        correctedSubscriptionName = 'Admin';
        debugPrint('تم تصحيح اسم الاشتراك من Free إلى Admin');
      }

      Subscription.selectedItem = correctedSubscriptionName;

      // debugPrint(
      //     '🔍 الاشتراك الحقيقي من قاعدة البيانات: ${finalModel.subscriptionName}');
      // debugPrint(
      //     '🔍 تم تعيين Subscription.selectedItem إلى: ${Subscription.selectedItem}');

      // التحقق من صحة اسم الاشتراك
      if (finalModel.subscriptionName.isEmpty) {
        debugPrint('❌ اسم الاشتراك فارغ');
        return false;
      }

      debugPrint('✅ تم التحقق من بيانات الاشتراك بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من بيانات الاشتراك: $e');
      return false;
    }
  }

  /// دالة لتحميل بيانات الاشتراك في الخلفية (بدون عرض الشاشة)
  /// تشمل جميع المميزات الموجودة في السحب للتحديث
  Future<void> _loadSubscriptionDataInBackground() async {
    try {
      debugPrint(
          '🔄 بدء تحميل جميع البيانات في الخلفية (مثل السحب للتحديث)...');

      // 1. التحقق من الاتصال بالإنترنت (مثل _refreshData)
      bool hasConnection = await InternetConnectionChecker().hasConnection;
      if (!hasConnection) {
        debugPrint('❌ لا يوجد اتصال بالإنترنت');
        return;
      }

      // 2. تحميل بيانات الاشتراك باستخدام نفس منطق PackageScreen
      await _checkSubscriptionDataFromPackageScreen();

      // 3. التحقق من صحة الاشتراك (مثل _verifySubscriptionAccess)
      await _verifySubscriptionAccessBackground();

      // 4. تحديث جميع البيانات (مثل updateNotifier في home_screen)
      await _updateAllDataInBackground();

      debugPrint('✅ تم تحميل جميع البيانات في الخلفية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات في الخلفية: $e');
      // في حالة الخطأ، نستمر بالقيم الافتراضية
    }
  }

  /// دالة للتحقق من صحة الاشتراك في الخلفية
  Future<void> _verifySubscriptionAccessBackground() async {
    try {
      debugPrint('🔄 التحقق من صحة الاشتراك في الخلفية...');

      // التحقق من وجود اشتراك صالح
      final currentSubscription = Subscription.selectedItem;

      if (currentSubscription == null || currentSubscription.isEmpty) {
        debugPrint('❌ لا يوجد اشتراك صالح في الخلفية');
        return;
      }

      debugPrint('✅ تم التحقق من الاشتراك في الخلفية: $currentSubscription');
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الاشتراك في الخلفية: $e');
    }
  }

  /// دالة لتحديث جميع البيانات في الخلفية (مثل updateNotifier)
  Future<void> _updateAllDataInBackground() async {
    try {
      debugPrint('🔄 تحديث جميع البيانات في الخلفية...');

      // تحديث بيانات المستخدم
      await currentUserData.updateData();

      // تحديث بيانات الاشتراك مع معالجة الأخطاء
      try {
        await Subscription.getUserLimitsData(
            context: context, wannaShowMsg: false);
      } catch (e) {
        debugPrint('خطأ في تحميل بيانات الاشتراك: $e');
        // استخدام قيمة افتراضية في حالة الخطأ
        Subscription.selectedItem = 'Admin';
      }

      debugPrint('✅ تم تحديث جميع البيانات في الخلفية');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات في الخلفية: $e');
    }
  }

  /// دالة لتحميل بيانات الاشتراك باستخدام نفس منطق PackageScreen
  Future<void> _checkSubscriptionDataFromPackageScreen() async {
    try {
      debugPrint('🔄 تحميل بيانات الاشتراك باستخدام منطق PackageScreen...');

      // إذا كان مستخدم فرعي، استخدم بيانات الاشتراك من الـ Admin
      String subscriptionUserId = constUserId;
      if (isSubUser) {
        subscriptionUserId = finalUserRoleModel.databaseId;
        debugPrint(
            '🔄 مستخدم فرعي - استخدام اشتراك الـ Admin: $subscriptionUserId');
      }

      // جلب بيانات الاشتراك من قاعدة البيانات
      DatabaseReference ref =
          FirebaseDatabase.instance.ref('$subscriptionUserId/Subscription');
      final model = await ref.get();

      if (!model.exists || model.value == null) {
        // إذا لم توجد بيانات اشتراك، جرب جلب البيانات من Admin Panel/Seller List
        await _loadSubscriptionFromAdminPanelBackground();
        return;
      }

      var data = jsonDecode(jsonEncode(model.value));
      final finalModel = SubscriptionModel.fromJson(data);

      // الحصول على اسم الباقة الحقيقي من قاعدة البيانات
      String realSubscriptionName = finalModel.subscriptionName;

      // إذا كان الاسم فارغ، جرب جلب البيانات من Admin Panel
      if (realSubscriptionName.isEmpty) {
        await _loadSubscriptionFromAdminPanelBackground();
        return;
      }

      // تصحيح اسم الاشتراك إذا كان Free إلى Admin
      String correctedSubscriptionName = realSubscriptionName;
      if (correctedSubscriptionName == 'Free') {
        correctedSubscriptionName = 'Admin';
        debugPrint('تم تصحيح اسم الاشتراك من Free إلى Admin في الخلفية');
      }

      // تعيين اسم الباقة المصحح
      Subscription.selectedItem = correctedSubscriptionName;

      debugPrint(
          '✅ تم تحميل بيانات الاشتراك في الخلفية: $correctedSubscriptionName');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك من PackageScreen: $e');
      // في حالة الخطأ، جرب جلب البيانات من Admin Panel
      await _loadSubscriptionFromAdminPanelBackground();
    }
  }

  /// دالة لجلب بيانات الاشتراك من Admin Panel في الخلفية
  Future<void> _loadSubscriptionFromAdminPanelBackground() async {
    try {
      debugPrint('🔄 جاري جلب بيانات الاشتراك من Admin Panel في الخلفية...');

      // جلب بيانات المستخدم من Admin Panel/Seller List
      final sellerListRef =
          FirebaseDatabase.instance.ref('Admin Panel/Seller List');
      final sellerListSnapshot = await sellerListRef.get();

      if (sellerListSnapshot.exists) {
        String? userSubscriptionName;

        // البحث عن المستخدم الحالي في قائمة البائعين
        for (var element in sellerListSnapshot.children) {
          try {
            var userData = jsonDecode(jsonEncode(element.value));

            // التحقق من أن هذا هو المستخدم الحالي
            if (userData['userId'] == constUserId ||
                userData['email'] == FirebaseAuth.instance.currentUser?.email) {
              userSubscriptionName = userData['subscriptionName']?.toString();
              debugPrint(
                  'تم العثور على اسم الباقة في Admin Panel: $userSubscriptionName');
              break;
            }
          } catch (e) {
            debugPrint('خطأ في معالجة بيانات المستخدم: $e');
            continue;
          }
        }

        // إذا تم العثور على اسم الباقة، استخدمه مع التصحيح
        if (userSubscriptionName != null && userSubscriptionName.isNotEmpty) {
          // تصحيح اسم الاشتراك إذا كان Free إلى Admin
          String correctedSubscriptionName = userSubscriptionName;
          if (correctedSubscriptionName == 'Free') {
            correctedSubscriptionName = 'Admin';
            debugPrint(
                'تم تصحيح اسم الاشتراك من Free إلى Admin في Admin Panel');
          }

          Subscription.selectedItem = correctedSubscriptionName;
          debugPrint(
              'تم تعيين اسم الباقة المصحح في الخلفية: $correctedSubscriptionName');
        } else {
          // إذا لم يتم العثور على اسم باقة صالح، استخدم قيمة افتراضية
          debugPrint(
              'لم يتم العثور على اسم باقة صالح، استخدام القيمة الافتراضية');
          _setDefaultSubscriptionBackground();
        }
      } else {
        debugPrint('لا توجد بيانات في Admin Panel/Seller List');
        _setDefaultSubscriptionBackground();
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب البيانات من Admin Panel: $e');
      _setDefaultSubscriptionBackground();
    }
  }

  /// دالة لتعيين اشتراك افتراضي في الخلفية
  void _setDefaultSubscriptionBackground() {
    const defaultSubscriptionName = 'Admin';
    Subscription.selectedItem = defaultSubscriptionName;
    debugPrint(
        'تم تعيين الاشتراك الافتراضي في الخلفية: $defaultSubscriptionName');
  }

  /// دالة لإعداد الاشتراك المجاني
  Future<void> setupFreeSubscription() async {
    try {
      debugPrint('بدء إعداد الاشتراك المجاني...');

      // تعيين اسم الاشتراك في Subscription
      Subscription.selectedItem = 'Admin';

      // الحصول على معرف المستخدم الحالي
      final userId = FirebaseAuth.instance.currentUser!.uid;

      // إنشاء نموذج الاشتراك
      final Map<String, dynamic> subscriptionData = {
        'subscriptionName': 'Admin',
        'subscriptionDate': DateTime.now().toString(),
        'products': 'unlimited',
        'duration': 'unlimited',
        'dueNumber': 'unlimited',
        'partiesNumber': 'unlimited',
        'purchaseNumber': 'unlimited',
        'saleNumber': 'unlimited',
      };

      // الحصول على مرجع الاشتراك
      final DatabaseReference subscriptionRef =
          FirebaseDatabase.instance.ref().child(userId).child('Subscription');

      // حفظ بيانات الاشتراك
      await subscriptionRef.set(subscriptionData);

      debugPrint('تم إعداد الاشتراك المجاني بنجاح للمستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في إعداد الاشتراك المجاني: $e');
    }
  }
}
