// بسم الله الرحمن الرحيم
// نموذج التقرير - يمثل بيانات التقرير

import 'dart:convert';

/// نوع التقرير
enum ReportType {
  /// تقرير المبيعات
  sales,

  /// تقرير المخزون
  inventory,

  /// تقرير مالي
  financial,

  /// تقرير مخصص
  custom,
}

/// فترة التقرير
enum ReportPeriod {
  /// يومي
  daily,

  /// أسبوعي
  weekly,

  /// شهري
  monthly,

  /// ربع سنوي
  quarterly,

  /// سنوي
  yearly,

  /// مخصص
  custom,
}

/// نموذج التقرير
class ReportModel {
  /// ينشئ نموذج التقرير
  const ReportModel({
    required this.id,
    required this.title,
    required this.type,
    required this.period,
    required this.createdAt,
    this.description,
    this.parameters = const {},
    this.data = const {},
    this.chartData = const [],
    this.tableData = const [],
    this.createdBy,
  });

  /// معرف التقرير
  final String id;

  /// عنوان التقرير
  final String title;

  /// نوع التقرير
  final ReportType type;

  /// فترة التقرير
  final ReportPeriod period;

  /// تاريخ إنشاء التقرير
  final DateTime createdAt;

  /// وصف التقرير (اختياري)
  final String? description;

  /// معلمات التقرير
  final Map<String, dynamic> parameters;

  /// بيانات التقرير
  final Map<String, dynamic> data;

  /// بيانات الرسم البياني
  final List<Map<String, dynamic>> chartData;

  /// بيانات الجدول
  final List<Map<String, dynamic>> tableData;

  /// منشئ التقرير (اختياري)
  final String? createdBy;

  /// نسخ النموذج مع تحديث بعض الحقول
  ReportModel copyWith({
    String? title,
    ReportType? type,
    ReportPeriod? period,
    String? description,
    Map<String, dynamic>? parameters,
    Map<String, dynamic>? data,
    List<Map<String, dynamic>>? chartData,
    List<Map<String, dynamic>>? tableData,
    String? createdBy,
  }) {
    return ReportModel(
      id: id,
      title: title ?? this.title,
      type: type ?? this.type,
      period: period ?? this.period,
      createdAt: createdAt,
      description: description ?? this.description,
      parameters: parameters ?? this.parameters,
      data: data ?? this.data,
      chartData: chartData ?? this.chartData,
      tableData: tableData ?? this.tableData,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type.index,
      'period': period.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'description': description,
      'parameters': parameters,
      'data': data,
      'chartData': chartData,
      'tableData': tableData,
      'createdBy': createdBy,
    };
  }

  /// إنشاء نموذج من Map
  factory ReportModel.fromMap(Map<String, dynamic> map) {
    return ReportModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      type: ReportType.values[map['type'] ?? 0],
      period: ReportPeriod.values[map['period'] ?? 0],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      description: map['description'],
      parameters: Map<String, dynamic>.from(map['parameters'] ?? {}),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      chartData: List<Map<String, dynamic>>.from(
          map['chartData']?.map((x) => Map<String, dynamic>.from(x)) ?? []),
      tableData: List<Map<String, dynamic>>.from(
          map['tableData']?.map((x) => Map<String, dynamic>.from(x)) ?? []),
      createdBy: map['createdBy'],
    );
  }

  /// تحويل النموذج إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من JSON
  factory ReportModel.fromJson(String source) =>
      ReportModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ReportModel(id: $id, title: $title, type: $type, period: $period)';
  }
}

/// نموذج معلمات التقرير
class ReportParameters {
  /// ينشئ نموذج معلمات التقرير
  const ReportParameters({
    this.startDate,
    this.endDate,
    this.categories = const [],
    this.products = const [],
    this.customers = const [],
    this.suppliers = const [],
    this.includeVat = true,
    this.includeDiscounts = true,
    this.groupBy,
    this.sortBy,
    this.limit,
  });

  /// تاريخ البداية (اختياري)
  final DateTime? startDate;

  /// تاريخ النهاية (اختياري)
  final DateTime? endDate;

  /// الفئات (اختياري)
  final List<String> categories;

  /// المنتجات (اختياري)
  final List<String> products;

  /// العملاء (اختياري)
  final List<String> customers;

  /// الموردين (اختياري)
  final List<String> suppliers;

  /// هل يتضمن ضريبة القيمة المضافة؟
  final bool includeVat;

  /// هل يتضمن الخصومات؟
  final bool includeDiscounts;

  /// التجميع حسب (اختياري)
  final String? groupBy;

  /// الترتيب حسب (اختياري)
  final String? sortBy;

  /// الحد الأقصى للنتائج (اختياري)
  final int? limit;

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate?.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'categories': categories,
      'products': products,
      'customers': customers,
      'suppliers': suppliers,
      'includeVat': includeVat,
      'includeDiscounts': includeDiscounts,
      'groupBy': groupBy,
      'sortBy': sortBy,
      'limit': limit,
    };
  }

  /// إنشاء نموذج من Map
  factory ReportParameters.fromMap(Map<String, dynamic> map) {
    return ReportParameters(
      startDate: map['startDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['startDate'])
          : null,
      endDate: map['endDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['endDate'])
          : null,
      categories: List<String>.from(map['categories'] ?? []),
      products: List<String>.from(map['products'] ?? []),
      customers: List<String>.from(map['customers'] ?? []),
      suppliers: List<String>.from(map['suppliers'] ?? []),
      includeVat: map['includeVat'] ?? true,
      includeDiscounts: map['includeDiscounts'] ?? true,
      groupBy: map['groupBy'],
      sortBy: map['sortBy'],
      limit: map['limit'],
    );
  }
}
