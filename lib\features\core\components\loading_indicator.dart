// بسم الله الرحمن الرحيم
// مؤشر التحميل - يستخدم لعرض حالة التحميل في التطبيق

import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// مؤشر تحميل قابل لإعادة الاستخدام يمكن استخدامه في جميع أنحاء التطبيق
/// لتوفير تجربة تحميل متسقة
class LoadingIndicatorWidget extends StatelessWidget {
  /// ينشئ مؤشر تحميل مع تخصيص اختياري
  const LoadingIndicatorWidget({
    super.key,
    this.color,
    this.size = 40.0,
    this.strokeWidth = 4.0,
    this.message,
  });

  /// لون مؤشر التحميل. يستخدم اللون الرئيسي إذا لم يتم توفيره.
  final Color? color;

  /// حجم مؤشر التحميل.
  final double size;

  /// عرض الخط لمؤشر التحميل.
  final double strokeWidth;

  /// رسالة اختيارية لعرضها أسفل مؤشر التحميل.
  final String? message;

  @override
  Widget build(BuildContext context) {
    // استخدام الشعار المتحرك بدلاً من المؤشر الدائري
    // سيتم تفعيل هذا لاحقًا عند إضافة الملفات المطلوبة
    /*
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedLogo(
            logoPath: splashScreenLogo,
            size: size,
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.greyTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
    */

    // استخدام المؤشر الدائري مؤقتًا
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              strokeWidth: strokeWidth,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColors.mainColor,
              ),
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.greyTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// شاشة تحميل كاملة تغطي الشاشة بأكملها
class FullScreenLoadingIndicator extends StatelessWidget {
  /// ينشئ شاشة تحميل كاملة مع تخصيص اختياري
  const FullScreenLoadingIndicator({
    super.key,
    this.color,
    this.backgroundColor,
    this.message,
  });

  /// لون مؤشر التحميل. يستخدم اللون الرئيسي إذا لم يتم توفيره.
  final Color? color;

  /// لون خلفية شاشة التحميل. يستخدم أبيض شبه شفاف إذا لم يتم توفيره.
  final Color? backgroundColor;

  /// رسالة اختيارية لعرضها أسفل مؤشر التحميل.
  final String? message;

  @override
  Widget build(BuildContext context) {
    // استخدام الشعار المتحرك بدلاً من المؤشر الدائري
    // سيتم تفعيل هذا لاحقًا عند إضافة الملفات المطلوبة
    /*
    return Container(
      color: backgroundColor ?? Colors.white.withAlpha(204),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedLogo(
              logoPath: splashScreenLogo,
              size: 100,
            ),
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message!,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.greyTextColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
    */

    // استخدام المؤشر الدائري مؤقتًا
    return Container(
      color: backgroundColor ?? Colors.white.withAlpha(204),
      child: LoadingIndicatorWidget(
        color: color,
        message: message,
      ),
    );
  }
}
