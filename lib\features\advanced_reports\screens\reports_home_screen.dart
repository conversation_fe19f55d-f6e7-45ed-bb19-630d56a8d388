// بسم الله الرحمن الرحيم
// شاشة التقارير الرئيسية - تعرض قائمة التقارير المتاحة

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:mobile_pos/constant.dart';
import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/report_model.dart';
import '../services/report_service.dart';
import 'sales_report_screen.dart';
import 'inventory_report_screen.dart';
import 'financial_report_screen.dart';
import 'custom_report_screen.dart';

/// شاشة التقارير الرئيسية
class ReportsHomeScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة التقارير الرئيسية
  const ReportsHomeScreen({super.key});

  static const String routeName = '/reports';

  @override
  ConsumerState<ReportsHomeScreen> createState() => _ReportsHomeScreenState();
}

class _ReportsHomeScreenState extends ConsumerState<ReportsHomeScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  // تحميل التقارير
  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث مزود التقارير
      final _ = await ref.refresh(currentUserReportsProvider.future);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // حذف تقرير
  Future<void> _deleteReport(String reportId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success =
          await ref.read(reportServiceProvider).deleteReport(reportId);

      if (success) {
        // تحديث مزود التقارير
        final _ = await ref.refresh(currentUserReportsProvider.future);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف التقرير')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل في حذف التقرير')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // فتح شاشة إنشاء تقرير جديد
  void _createNewReport(ReportType type) {
    switch (type) {
      case ReportType.sales:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const SalesReportScreen(),
          ),
        );
        break;
      case ReportType.inventory:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const InventoryReportScreen(),
          ),
        );
        break;
      case ReportType.financial:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const FinancialReportScreen(),
          ),
        );
        break;
      case ReportType.custom:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CustomReportScreen(),
          ),
        );
        break;
    }
  }

  // فتح تقرير موجود
  void _openReport(ReportModel report) {
    switch (report.type) {
      case ReportType.sales:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SalesReportScreen(report: report),
          ),
        );
        break;
      case ReportType.inventory:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => InventoryReportScreen(report: report),
          ),
        );
        break;
      case ReportType.financial:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FinancialReportScreen(report: report),
          ),
        );
        break;
      case ReportType.custom:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CustomReportScreen(report: report),
          ),
        );
        break;
    }
  }

  // الحصول على أيقونة نوع التقرير
  IconData _getReportTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.sales:
        return Icons.point_of_sale;
      case ReportType.inventory:
        return Icons.inventory;
      case ReportType.financial:
        return Icons.attach_money;
      case ReportType.custom:
        return Icons.dashboard_customize;
    }
  }

  // الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.sales:
        return 'تقرير المبيعات';
      case ReportType.inventory:
        return 'تقرير المخزون';
      case ReportType.financial:
        return 'تقرير مالي';
      case ReportType.custom:
        return 'تقرير مخصص';
    }
  }

  // الحصول على لون نوع التقرير
  Color _getReportTypeColor(ReportType type) {
    switch (type) {
      case ReportType.sales:
        return Colors.blue;
      case ReportType.inventory:
        return Colors.green;
      case ReportType.financial:
        return Colors.purple;
      case ReportType.custom:
        return Colors.orange;
    }
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  @override
  Widget build(BuildContext context) {
    final reportsAsync = ref.watch(currentUserReportsProvider);

    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        backgroundColor: kMainColor,
        title: Text(
          'التقارير المتقدمة',
          style: kTextStyle.copyWith(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadReports,
          ),
        ],
        elevation: 0,
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل التقارير...',
            )
          : reportsAsync.when(
              data: (reports) {
                return Column(
                  children: [
                    // بطاقات أنواع التقارير
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildReportTypeCard(
                              title: 'تقرير المبيعات',
                              icon: Icons.point_of_sale,
                              color: Colors.blue,
                              onTap: () => _createNewReport(ReportType.sales),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildReportTypeCard(
                              title: 'تقرير المخزون',
                              icon: Icons.inventory,
                              color: Colors.green,
                              onTap: () =>
                                  _createNewReport(ReportType.inventory),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildReportTypeCard(
                              title: 'تقرير مالي',
                              icon: Icons.attach_money,
                              color: Colors.purple,
                              onTap: () =>
                                  _createNewReport(ReportType.financial),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildReportTypeCard(
                              title: 'تقرير مخصص',
                              icon: Icons.dashboard_customize,
                              color: Colors.orange,
                              onTap: () => _createNewReport(ReportType.custom),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Divider(),

                    // عنوان قائمة التقارير
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const Text(
                            'التقارير السابقة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${reports.length} تقرير',
                            style: const TextStyle(
                              color: AppColors.greyTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // قائمة التقارير
                    Expanded(
                      child: reports.isEmpty
                          ? const Center(
                              child: Text(
                                'لا توجد تقارير سابقة',
                                style: TextStyle(fontSize: 16),
                              ),
                            )
                          : ListView.builder(
                              itemCount: reports.length,
                              itemBuilder: (context, index) {
                                final report = reports[index];
                                return Dismissible(
                                  key: Key(report.id),
                                  direction: DismissDirection.endToStart,
                                  background: Container(
                                    color: Colors.red,
                                    alignment: Alignment.centerRight,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20),
                                    child: const Icon(
                                      Icons.delete,
                                      color: Colors.white,
                                    ),
                                  ),
                                  confirmDismiss: (direction) async {
                                    return await showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('حذف التقرير'),
                                        content: const Text(
                                            'هل أنت متأكد من حذف هذا التقرير؟'),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context, false),
                                            child: const Text('إلغاء'),
                                          ),
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context, true),
                                            child: const Text('حذف'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  onDismissed: (direction) {
                                    _deleteReport(report.id);
                                  },
                                  child: Card(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    child: ListTile(
                                      leading: CircleAvatar(
                                        backgroundColor:
                                            _getReportTypeColor(report.type),
                                        child: Icon(
                                          _getReportTypeIcon(report.type),
                                          color: Colors.white,
                                        ),
                                      ),
                                      title: Text(report.title),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(_getReportTypeName(report.type)),
                                          Text(
                                            'تاريخ الإنشاء: ${_formatDate(report.createdAt)}',
                                            style:
                                                const TextStyle(fontSize: 12),
                                          ),
                                        ],
                                      ),
                                      trailing: const Icon(
                                          Icons.arrow_forward_ios,
                                          size: 16),
                                      onTap: () => _openReport(report),
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                );
              },
              loading: () => const LoadingIndicatorWidget(
                message: 'جاري تحميل التقارير...',
              ),
              error: (error, stackTrace) => Center(
                child: Text('حدث خطأ: $error'),
              ),
            ),
    );
  }

  // بناء بطاقة نوع التقرير - تم تعديلها لتتوافق مع تصميم التطبيق الرئيسي
  Widget _buildReportTypeCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: kGreyTextColor.withAlpha(26),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 40,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: kTextStyle.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kTitleColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
