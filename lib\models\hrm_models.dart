// استخدام التاريخ العادي بدلاً من Timestamp

// نموذج الموظف
class EmployeeModel {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final String designation;
  final String department;
  final double salary;
  final DateTime joiningDate;
  final String imageUrl;
  final bool isActive;

  EmployeeModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.designation,
    required this.department,
    required this.salary,
    required this.joiningDate,
    required this.imageUrl,
    required this.isActive,
  });

  // تحويل من Firestore
  factory EmployeeModel.fromMap(Map<String, dynamic> map, String id) {
    return EmployeeModel(
      id: id,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      address: map['address'] ?? '',
      designation: map['designation'] ?? '',
      department: map['department'] ?? '',
      salary: (map['salary'] ?? 0).toDouble(),
      joiningDate: DateTime.fromMillisecondsSinceEpoch(map['joiningDate'] ?? 0),
      imageUrl: map['imageUrl'] ?? '',
      isActive: map['isActive'] ?? true,
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'designation': designation,
      'department': department,
      'salary': salary,
      'joiningDate': joiningDate.millisecondsSinceEpoch,
      'imageUrl': imageUrl,
      'isActive': isActive,
    };
  }

  // نسخة مع تعديلات
  EmployeeModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? designation,
    String? department,
    double? salary,
    DateTime? joiningDate,
    String? imageUrl,
    bool? isActive,
  }) {
    return EmployeeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      designation: designation ?? this.designation,
      department: department ?? this.department,
      salary: salary ?? this.salary,
      joiningDate: joiningDate ?? this.joiningDate,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
    );
  }
}

// نموذج المسمى الوظيفي
class DesignationModel {
  final String id;
  final String title;
  final String description;
  final String department;
  final DateTime createdAt;

  DesignationModel({
    required this.id,
    required this.title,
    required this.description,
    required this.department,
    required this.createdAt,
  });

  // تحويل من Firestore
  factory DesignationModel.fromMap(Map<String, dynamic> map, String id) {
    return DesignationModel(
      id: id,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      department: map['department'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'department': department,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  // نسخة مع تعديلات
  DesignationModel copyWith({
    String? id,
    String? title,
    String? description,
    String? department,
    DateTime? createdAt,
  }) {
    return DesignationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      department: department ?? this.department,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

// نموذج القسم
class DepartmentModel {
  final String id;
  final String name;
  final String description;

  DepartmentModel({
    required this.id,
    required this.name,
    required this.description,
  });

  // تحويل من Firestore
  factory DepartmentModel.fromMap(Map<String, dynamic> map, String id) {
    return DepartmentModel(
      id: id,
      name: map['name'] ?? '',
      description: map['description'] ?? '',
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
    };
  }

  // نسخة مع تعديلات
  DepartmentModel copyWith({
    String? id,
    String? name,
    String? description,
  }) {
    return DepartmentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
    );
  }
}

// نموذج الراتب
class SalaryModel {
  final String id;
  final String employeeId;
  final String employeeName;
  final double amount;
  final DateTime paymentDate;
  final String paymentMethod;
  final String description;
  final bool isPaid;

  SalaryModel({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.amount,
    required this.paymentDate,
    required this.paymentMethod,
    required this.description,
    required this.isPaid,
  });

  // تحويل من Firestore
  factory SalaryModel.fromMap(Map<String, dynamic> map, String id) {
    return SalaryModel(
      id: id,
      employeeId: map['employeeId'] ?? '',
      employeeName: map['employeeName'] ?? '',
      amount: (map['amount'] ?? 0).toDouble(),
      paymentDate: DateTime.fromMillisecondsSinceEpoch(map['paymentDate'] ?? 0),
      paymentMethod: map['paymentMethod'] ?? '',
      description: map['description'] ?? '',
      isPaid: map['isPaid'] ?? false,
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'employeeId': employeeId,
      'employeeName': employeeName,
      'amount': amount,
      'paymentDate': paymentDate.millisecondsSinceEpoch,
      'paymentMethod': paymentMethod,
      'description': description,
      'isPaid': isPaid,
    };
  }

  // نسخة مع تعديلات
  SalaryModel copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    double? amount,
    DateTime? paymentDate,
    String? paymentMethod,
    String? description,
    bool? isPaid,
  }) {
    return SalaryModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      description: description ?? this.description,
      isPaid: isPaid ?? this.isPaid,
    );
  }
}
