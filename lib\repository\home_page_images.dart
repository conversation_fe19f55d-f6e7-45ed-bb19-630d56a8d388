import 'dart:convert';

import 'package:mobile_pos/services/firebase_database_service.dart';

import '../model/homepage_image_model.dart';

class HomePageImageRepo {
  Future<List<HomePageImageModel>> getAllHomePageImage() async {
    List<HomePageImageModel> imageList = [];
    final imageRef = FirebaseDatabaseService.getReference(
        'Admin Panel/Homepage Image',
        keepSynced: true);
    await imageRef.orderByKey().get().then((value) {
      for (var element in value.children) {
        imageList.add(
            HomePageImageModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    });
    return imageList;
  }
}
